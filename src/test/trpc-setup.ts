import { beforeAll, afterAll, beforeEach } from 'vitest'
import { db } from '~/lib/core/db'
import { hash } from 'bcryptjs'
import { UserRole } from '@prisma/client'

// Test database cleanup helper
export async function cleanupTestData() {
  // Delete in correct order due to foreign key constraints
  await db.application.deleteMany()
  await db.job.deleteMany()
  await db.session.deleteMany()
  await db.account.deleteMany()
  await db.user.deleteMany()
  await db.organization.deleteMany()
}

// Test data creation helpers
export async function createTestOrganization(name = 'Test Organization') {
  return await db.organization.create({
    data: {
      name,
      slug: name.toLowerCase().replace(/\s+/g, '-'),
      settings: {
        allowCandidateApplications: true,
        requireCoverLetter: false,
      },
    },
  })
}

export async function createTestUser(data: {
  email: string
  name: string
  role: UserRole
  organizationId?: string
  password?: string
  twoFactorEnabled?: boolean
}) {
  const hashedPassword = data.password ? await hash(data.password, 12) : null

  return await db.user.create({
    data: {
      email: data.email,
      name: data.name,
      role: data.role,
      organizationId: data.organizationId,
      password: hashedPassword,
      emailVerified: new Date(),
      twoFactorEnabled: data.twoFactorEnabled || false,
    },
  })
}

export async function createTestJob(data: {
  title: string
  organizationId: string
  status?: 'DRAFT' | 'PUBLISHED' | 'ACTIVE' | 'PAUSED' | 'CLOSED' | 'EXPIRED'
}) {
  return await db.job.create({
    data: {
      title: data.title,
      description: 'Test job description',
      requirements: ['Test requirement 1', 'Test requirement 2'],
      benefits: ['Test benefit 1', 'Test benefit 2'],
      locationType: 'REMOTE',
      employmentType: 'FULL_TIME',
      experience: 'MID_LEVEL',
      status: data.status || 'DRAFT',
      organizationId: data.organizationId,
    },
  })
}

// Mock session helper for testing protected procedures
export function createMockSession(user: {
  id: string
  email: string
  role: UserRole
  organizationId?: string | null
}) {
  return {
    user: {
      id: user.id,
      email: user.email,
      name: `Test ${user.role}`,
      role: user.role,
      organizationId: user.organizationId || null,
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  }
}

// Test setup and teardown
beforeAll(async () => {
  // Ensure test database is clean before starting
  await cleanupTestData()
})

afterAll(async () => {
  // Clean up after all tests
  await cleanupTestData()
  await db.$disconnect()
})

beforeEach(async () => {
  // Clean up before each test to ensure isolation
  await cleanupTestData()
})