import { describe, it, expect, beforeEach } from 'vitest'
import { appRouter } from '~/server/api/root'
import { createInnerTRPCContext } from '~/server/api/test-utils'
import { cleanupTestData, createTestOrganization, createTestUser, createMockSession } from '../trpc-setup'

describe('Users Router', () => {
  beforeEach(async () => {
    await cleanupTestData()
  })

  describe('getAll (admin only)', () => {
    it('should return paginated users for admin', async () => {
      const org = await createTestOrganization()
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'SUPER_ADMIN',
      })

      // Create some test users
      await createTestUser({
        email: '<EMAIL>',
        name: 'User 1',
        role: 'CANDIDATE',
      })
      await createTestUser({
        email: '<EMAIL>',
        name: 'User 2',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.getAll({
        page: 1,
        limit: 10,
      })

      expect(result.users).toHaveLength(3) // admin + 2 test users
      expect(result.pagination.total).toBe(3)
      expect(result.pagination.page).toBe(1)
    })

    it('should filter users by role', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'SUPER_ADMIN',
      })

      await createTestUser({
        email: '<EMAIL>',
        name: 'Candidate',
        role: 'CANDIDATE',
      })
      await createTestUser({
        email: '<EMAIL>',
        name: 'Recruiter',
        role: 'RECRUITER',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.getAll({
        page: 1,
        limit: 10,
        role: 'CANDIDATE',
      })

      expect(result.users).toHaveLength(1)
      expect(result.users[0].role).toBe('CANDIDATE')
    })

    it('should search users by name and email', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'SUPER_ADMIN',
      })

      await createTestUser({
        email: '<EMAIL>',
        name: 'John Doe',
        role: 'CANDIDATE',
      })
      await createTestUser({
        email: '<EMAIL>',
        name: 'Jane Smith',
        role: 'RECRUITER',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.getAll({
        page: 1,
        limit: 10,
        search: 'john',
      })

      expect(result.users).toHaveLength(1)
      expect(result.users[0].name).toBe('John Doe')
    })

    it('should reject non-admin access', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Regular User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.users.getAll({ page: 1, limit: 10 }))
        .rejects.toThrow('FORBIDDEN')
    })
  })

  describe('getOrgUsers', () => {
    it('should return organization users', async () => {
      const org = await createTestOrganization()
      const orgAdmin = await createTestUser({
        email: '<EMAIL>',
        name: 'Org Admin',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      await createTestUser({
        email: '<EMAIL>',
        name: 'Recruiter',
        role: 'RECRUITER',
        organizationId: org.id,
      })

      // User from different org
      const otherOrg = await createTestOrganization('Other Company')
      await createTestUser({
        email: '<EMAIL>',
        name: 'Other User',
        role: 'RECRUITER',
        organizationId: otherOrg.id,
      })

      const session = createMockSession(orgAdmin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.getOrgUsers({
        page: 1,
        limit: 10,
      })

      expect(result.users).toHaveLength(2) // Only users from the same org
      expect(result.users.every(user => user.organizationId === org.id)).toBe(true)
    })
  })

  describe('getById', () => {
    it('should allow users to view their own profile', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Test User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.getById({ userId: user.id })

      expect(result.id).toBe(user.id)
      expect(result.email).toBe(user.email)
    })

    it('should allow admin to view any user', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Test User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.getById({ userId: user.id })

      expect(result.id).toBe(user.id)
    })

    it('should reject non-admin viewing other users', async () => {
      const user1 = await createTestUser({
        email: '<EMAIL>',
        name: 'User 1',
        role: 'CANDIDATE',
      })

      const user2 = await createTestUser({
        email: '<EMAIL>',
        name: 'User 2',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user1)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.users.getById({ userId: user2.id }))
        .rejects.toThrow('FORBIDDEN')
    })
  })

  describe('update', () => {
    it('should allow users to update their own profile', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Old Name',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.update({
        userId: user.id,
        name: 'New Name',
      })

      expect(result.name).toBe('New Name')
    })

    it('should allow admin to update user roles', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.update({
        userId: user.id,
        role: 'RECRUITER',
      })

      expect(result.role).toBe('RECRUITER')
    })

    it('should prevent regular users from updating roles', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.update({
        userId: user.id,
        name: 'New Name',
        role: 'ORG_ADMIN', // This should be ignored
      })

      expect(result.name).toBe('New Name')
      expect(result.role).toBe('CANDIDATE') // Role should not change
    })
  })

  describe('delete', () => {
    it('should allow admin to delete users', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.delete({ userId: user.id })

      expect(result.success).toBe(true)
    })

    it('should prevent admin from deleting themselves', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.users.delete({ userId: admin.id }))
        .rejects.toThrow('You cannot delete your own account')
    })
  })

  describe('getStats', () => {
    it('should return user statistics for admin', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const org = await createTestOrganization()
      await createTestUser({
        email: '<EMAIL>',
        name: 'Candidate',
        role: 'CANDIDATE',
      })
      await createTestUser({
        email: '<EMAIL>',
        name: 'Recruiter',
        role: 'RECRUITER',
        organizationId: org.id,
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.users.getStats()

      expect(result.totalUsers).toBe(3)
      expect(result.candidateCount).toBe(1)
      expect(result.businessUserCount).toBe(1)
      expect(result.adminCount).toBe(1)
    })
  })
})