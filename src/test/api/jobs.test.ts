import { describe, it, expect, beforeEach } from 'vitest'
import { appRouter } from '~/server/api/root'
import { createInnerTRPCContext } from '~/server/api/test-utils'
import { cleanupTestData, createTestOrganization, createTestUser, createTestJob, createMockSession } from '../trpc-setup'

describe('Jobs Router', () => {
  beforeEach(async () => {
    await cleanupTestData()
  })

  describe('create', () => {
    it('should allow hiring manager to create job', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const jobData = {
        title: 'Senior Software Engineer',
        description: 'We are looking for a senior software engineer to join our team.',
        requirements: ['5+ years experience', 'JavaScript expertise', 'React knowledge'],
        benefits: ['Health insurance', 'Remote work'],
        locationType: 'REMOTE' as const,
        employmentType: 'FULL_TIME' as const,
        experience: 'SENIOR_LEVEL' as const,
        salaryMin: 80000,
        salaryMax: 120000,
        salaryCurrency: 'USD',
        salaryPeriod: 'YEAR' as const,
      }

      const result = await caller.jobs.create(jobData)

      expect(result.title).toBe(jobData.title)
      expect(result.organizationId).toBe(org.id)
      expect(result.status).toBe('DRAFT')
      expect(result.organization.name).toBe(org.name)
    })

    it('should reject candidates creating jobs', async () => {
      const candidate = await createTestUser({
        email: '<EMAIL>',
        name: 'Candidate',
        role: 'CANDIDATE',
      })

      const session = createMockSession(candidate)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const jobData = {
        title: 'Software Engineer',
        description: 'A software engineering role.',
        requirements: ['Programming skills'],
        locationType: 'REMOTE' as const,
        employmentType: 'FULL_TIME' as const,
        experience: 'MID_LEVEL' as const,
      }

      await expect(caller.jobs.create(jobData))
        .rejects.toThrow('FORBIDDEN')
    })

    it('should validate salary ranges', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const jobData = {
        title: 'Software Engineer',
        description: 'A software engineering role with invalid salary range.',
        requirements: ['Programming skills'],
        locationType: 'REMOTE' as const,
        employmentType: 'FULL_TIME' as const,
        experience: 'MID_LEVEL' as const,
        salaryMin: 100000,
        salaryMax: 80000, // Invalid: max < min
      }

      await expect(caller.jobs.create(jobData))
        .rejects.toThrow()
    })
  })

  describe('getAll', () => {
    it('should return organization jobs with pagination', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      // Create test jobs
      await createTestJob({ title: 'Job 1', organizationId: org.id })
      await createTestJob({ title: 'Job 2', organizationId: org.id, status: 'PUBLISHED' })
      await createTestJob({ title: 'Job 3', organizationId: org.id })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.getAll({
        page: 1,
        limit: 2,
      })

      expect(result.jobs).toHaveLength(2)
      expect(result.pagination.total).toBe(3)
      expect(result.pagination.pages).toBe(2)
    })

    it('should filter jobs by status', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      await createTestJob({ title: 'Draft Job', organizationId: org.id, status: 'DRAFT' })
      await createTestJob({ title: 'Published Job', organizationId: org.id, status: 'PUBLISHED' })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.getAll({
        page: 1,
        limit: 10,
        status: 'PUBLISHED',
      })

      expect(result.jobs).toHaveLength(1)
      expect(result.jobs[0].status).toBe('PUBLISHED')
    })

    it('should search jobs by title and description', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      await createTestJob({ title: 'Senior Developer', organizationId: org.id })
      await createTestJob({ title: 'Junior Designer', organizationId: org.id })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.getAll({
        page: 1,
        limit: 10,
        search: 'developer',
      })

      expect(result.jobs).toHaveLength(1)
      expect(result.jobs[0].title).toBe('Senior Developer')
    })
  })

  describe('getPublic', () => {
    it('should return only published jobs for public API', async () => {
      const org = await createTestOrganization()

      // Create jobs with different statuses
      const publishedJob = await createTestJob({ 
        title: 'Published Job', 
        organizationId: org.id, 
        status: 'PUBLISHED' 
      })
      
      // Update the published job to have publishedAt date
      const baseCtx = await createInnerTRPCContext({ session: null })
      await baseCtx.db.job.update({
        where: { id: publishedJob.id },
        data: { publishedAt: new Date() }
      })

      await createTestJob({ title: 'Draft Job', organizationId: org.id, status: 'DRAFT' })

      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.getPublic({
        page: 1,
        limit: 10,
      })

      expect(result.jobs).toHaveLength(1)
      expect(result.jobs[0].title).toBe('Published Job')
      expect(result.jobs[0].organization.name).toBe(org.name)
    })

    it('should search public jobs by organization name', async () => {
      const org1 = await createTestOrganization('Tech Company')
      const org2 = await createTestOrganization('Design Studio')

      const job1 = await createTestJob({ title: 'Developer', organizationId: org1.id, status: 'PUBLISHED' })
      const job2 = await createTestJob({ title: 'Designer', organizationId: org2.id, status: 'PUBLISHED' })

      // Set published dates
      const baseCtx = await createInnerTRPCContext({ session: null })
      await Promise.all([
        baseCtx.db.job.update({
          where: { id: job1.id },
          data: { publishedAt: new Date() }
        }),
        baseCtx.db.job.update({
          where: { id: job2.id },
          data: { publishedAt: new Date() }
        })
      ])

      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.getPublic({
        page: 1,
        limit: 10,
        search: 'tech',
      })

      expect(result.jobs).toHaveLength(1)
      expect(result.jobs[0].organization.name).toBe('Tech Company')
    })
  })

  describe('publish', () => {
    it('should publish a draft job', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      const job = await createTestJob({ title: 'Draft Job', organizationId: org.id, status: 'DRAFT' })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.publish({
        jobId: job.id,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      })

      expect(result.status).toBe('PUBLISHED')
      expect(result.publishedAt).toBeInstanceOf(Date)
      expect(result.expiresAt).toBeInstanceOf(Date)
    })

    it('should reject non-hiring manager publishing jobs', async () => {
      const org = await createTestOrganization()
      const recruiter = await createTestUser({
        email: '<EMAIL>',
        name: 'Recruiter',
        role: 'RECRUITER',
        organizationId: org.id,
      })

      const job = await createTestJob({ title: 'Draft Job', organizationId: org.id, status: 'DRAFT' })

      const session = createMockSession(recruiter)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.jobs.publish({ jobId: job.id }))
        .rejects.toThrow('FORBIDDEN')
    })
  })

  describe('delete', () => {
    it('should delete job without applications', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      const job = await createTestJob({ title: 'Job to Delete', organizationId: org.id })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.delete({ jobId: job.id })

      expect(result.success).toBe(true)
    })
  })

  describe('getStats', () => {
    it('should return organization job statistics', async () => {
      const org = await createTestOrganization()
      const hiringManager = await createTestUser({
        email: '<EMAIL>',
        name: 'Hiring Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      await createTestJob({ title: 'Draft Job', organizationId: org.id, status: 'DRAFT' })
      await createTestJob({ title: 'Published Job', organizationId: org.id, status: 'PUBLISHED' })
      await createTestJob({ title: 'Closed Job', organizationId: org.id, status: 'CLOSED' })

      const session = createMockSession(hiringManager)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.jobs.getStats({})

      expect(result.totalJobs).toBe(3)
      expect(result.publishedJobs).toBe(1)
      expect(result.draftJobs).toBe(1)
      expect(result.closedJobs).toBe(1)
    })
  })
})