import { describe, it, expect, beforeEach } from 'vitest'
import { TRPCError } from '@trpc/server'
import { appRouter } from '~/server/api/root'
import { createInnerTRPCContext } from '~/server/api/test-utils'
import { cleanupTestData, createTestOrganization, createTestUser, createMockSession } from '../trpc-setup'
import { UserRole } from '@prisma/client'

describe('Auth Router', () => {
  beforeEach(async () => {
    await cleanupTestData()
  })

  describe('signUpCandidate', () => {
    it('should create a candidate user successfully', async () => {
      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const input = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Test Candidate',
      }

      const result = await caller.auth.signUpCandidate(input)

      expect(result.success).toBe(true)
      expect(result.user.email).toBe(input.email)
      expect(result.user.name).toBe(input.name)
      expect(result.user.role).toBe('CANDIDATE')
    })

    it('should reject duplicate email', async () => {
      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const input = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Test User',
      }

      // Create first user
      await caller.auth.signUpCandidate(input)

      // Try to create duplicate
      await expect(caller.auth.signUpCandidate(input))
        .rejects.toThrow('User with this email already exists')
    })

    it('should reject weak passwords', async () => {
      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const input = {
        email: '<EMAIL>',
        password: 'weak',
        name: 'Test User',
      }

      await expect(caller.auth.signUpCandidate(input))
        .rejects.toThrow()
    })
  })

  describe('signUpBusiness', () => {
    it('should create business user and organization', async () => {
      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const input = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Business Admin',
        organizationName: 'Test Company',
      }

      const result = await caller.auth.signUpBusiness(input)

      expect(result.success).toBe(true)
      expect(result.user.email).toBe(input.email)
      expect(result.user.role).toBe('ORG_ADMIN')
      expect(result.organization.name).toBe(input.organizationName)
      expect(result.user.organizationId).toBe(result.organization.id)
    })

    it('should reject personal email domains', async () => {
      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const input = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        name: 'Test User',
        organizationName: 'Test Company',
      }

      await expect(caller.auth.signUpBusiness(input))
        .rejects.toThrow('Please use a business email address')
    })
  })

  describe('getCurrentUser', () => {
    it('should return current user data', async () => {
      const org = await createTestOrganization()
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Test User',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.auth.getCurrentUser()

      expect(result.id).toBe(user.id)
      expect(result.email).toBe(user.email)
      expect(result.organization?.id).toBe(org.id)
    })

    it('should require authentication', async () => {
      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.auth.getCurrentUser())
        .rejects.toThrow('UNAUTHORIZED')
    })
  })

  describe('updateProfile', () => {
    it('should update user profile', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Old Name',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.auth.updateProfile({
        name: 'New Name',
      })

      expect(result.name).toBe('New Name')
    })
  })

  describe('forgotPassword', () => {
    it('should generate reset token for existing user', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Test User',
        role: 'CANDIDATE',
        password: 'TestPassword123!',
      })

      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.auth.forgotPassword({
        email: user.email,
      })

      expect(result.success).toBe(true)
      expect(result.message).toContain('reset link has been sent')
    })

    it('should not reveal if user does not exist', async () => {
      const ctx = await createInnerTRPCContext({ session: null })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.auth.forgotPassword({
        email: '<EMAIL>',
      })

      expect(result.success).toBe(true)
      expect(result.message).toContain('reset link has been sent')
    })
  })

  describe('2FA procedures (admin only)', () => {
    it('should generate 2FA secret for admin', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'SUPER_ADMIN',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.auth.generate2FASecret()

      expect(result.secret).toBeDefined()
      expect(result.qrCodeUrl).toBeDefined()
      expect(result.qrCodeUrl).toContain('data:image/png;base64')
    })

    it('should reject 2FA setup for non-admin users', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Regular User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.auth.generate2FASecret())
        .rejects.toThrow('FORBIDDEN')
    })
  })
})