import { describe, it, expect, beforeEach } from 'vitest'
import { appRouter } from '~/server/api/root'
import { createInnerTRPCContext } from '~/server/api/test-utils'
import { cleanupTestData, createTestOrganization, createTestUser, createMockSession } from '../trpc-setup'

describe('Organizations Router', () => {
  beforeEach(async () => {
    await cleanupTestData()
  })

  describe('getAll (admin only)', () => {
    it('should return all organizations for admin', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      await createTestOrganization('Company A')
      await createTestOrganization('Company B')
      await createTestOrganization('Company C')

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.getAll({
        page: 1,
        limit: 10,
      })

      expect(result.organizations).toHaveLength(3)
      expect(result.pagination.total).toBe(3)
    })

    it('should search organizations by name', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      await createTestOrganization('Tech Company')
      await createTestOrganization('Healthcare Corp')
      await createTestOrganization('Tech Startup')

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.getAll({
        page: 1,
        limit: 10,
        search: 'tech',
      })

      expect(result.organizations).toHaveLength(2)
      expect(result.organizations.every(org => 
        org.name.toLowerCase().includes('tech')
      )).toBe(true)
    })

    it('should reject non-admin access', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.organizations.getAll({ page: 1, limit: 10 }))
        .rejects.toThrow('FORBIDDEN')
    })
  })

  describe('getCurrent', () => {
    it('should return current user organization', async () => {
      const org = await createTestOrganization('Test Company')
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.getCurrent()

      expect(result.id).toBe(org.id)
      expect(result.name).toBe('Test Company')
    })

    it('should reject users without organization', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'CANDIDATE',
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.organizations.getCurrent())
        .rejects.toThrow('User is not associated with any organization')
    })
  })

  describe('getById', () => {
    it('should allow org members to view their organization', async () => {
      const org = await createTestOrganization('Test Company')
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.getById({
        organizationId: org.id,
      })

      expect(result.id).toBe(org.id)
      expect(result.users).toHaveLength(1)
      expect(result.users[0].id).toBe(user.id)
    })

    it('should allow admin to view any organization', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const org = await createTestOrganization('Any Company')

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.getById({
        organizationId: org.id,
      })

      expect(result.id).toBe(org.id)
    })

    it('should reject unauthorized access', async () => {
      const org1 = await createTestOrganization('Company 1')
      const org2 = await createTestOrganization('Company 2')
      
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'ORG_ADMIN',
        organizationId: org1.id,
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.organizations.getById({
        organizationId: org2.id,
      })).rejects.toThrow('FORBIDDEN')
    })
  })

  describe('create', () => {
    it('should allow admin to create organization', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.create({
        name: 'New Company',
        settings: { feature1: true },
      })

      expect(result.name).toBe('New Company')
      expect(result.slug).toBe('new-company')
      expect(result.settings).toEqual({ feature1: true })
    })

    it('should reject duplicate organization names', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      await createTestOrganization('Existing Company')

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.organizations.create({
        name: 'Existing Company',
      })).rejects.toThrow('Organization with this name already exists')
    })
  })

  describe('update', () => {
    it('should allow org admin to update their organization', async () => {
      const org = await createTestOrganization('Old Name')
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      const session = createMockSession(user)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.update({
        organizationId: org.id,
        name: 'New Name',
        settings: { updated: true },
      })

      expect(result.name).toBe('New Name')
      expect(result.slug).toBe('new-name')
      expect(result.settings).toEqual({ updated: true })
    })

    it('should allow super admin to update any organization', async () => {
      const org = await createTestOrganization('Test Company')
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.update({
        organizationId: org.id,
        name: 'Updated by Admin',
      })

      expect(result.name).toBe('Updated by Admin')
    })
  })

  describe('delete', () => {
    it('should allow admin to delete empty organization', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const org = await createTestOrganization('Empty Company')

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.delete({
        organizationId: org.id,
      })

      expect(result.success).toBe(true)
    })

    it('should reject deleting organization with users', async () => {
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'SUPER_ADMIN',
      })

      const org = await createTestOrganization('Company with Users')
      await createTestUser({
        email: '<EMAIL>',
        name: 'User',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      await expect(caller.organizations.delete({
        organizationId: org.id,
      })).rejects.toThrow('Cannot delete organization with existing users')
    })
  })

  describe('getStats', () => {
    it('should return organization statistics', async () => {
      const org = await createTestOrganization('Test Company')
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      await createTestUser({
        email: '<EMAIL>',
        name: 'Recruiter',
        role: 'RECRUITER',
        organizationId: org.id,
      })

      await createTestUser({
        email: '<EMAIL>',
        name: 'Manager',
        role: 'HIRING_MANAGER',
        organizationId: org.id,
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      const result = await caller.organizations.getStats({})

      expect(result.totalUsers).toBe(3)
      expect(result.usersByRole.admins).toBe(1)
      expect(result.usersByRole.recruiters).toBe(1)
      expect(result.usersByRole.hiringManagers).toBe(1)
    })
  })

  describe('settings management', () => {
    it('should get and update organization settings', async () => {
      const org = await createTestOrganization('Test Company')
      const admin = await createTestUser({
        email: '<EMAIL>',
        name: 'Admin',
        role: 'ORG_ADMIN',
        organizationId: org.id,
      })

      const session = createMockSession(admin)
      const ctx = await createInnerTRPCContext({ session })
      const caller = appRouter.createCaller(ctx)

      // Update settings
      const newSettings = { feature1: true, feature2: 'enabled' }
      await caller.organizations.updateSettings({
        settings: newSettings,
      })

      // Get settings
      const result = await caller.organizations.getSettings({})

      expect(result).toEqual(newSettings)
    })
  })
})