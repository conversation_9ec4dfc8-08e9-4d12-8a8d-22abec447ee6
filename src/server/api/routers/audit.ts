import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  createTRPCRouter,
  adminProcedure,
  orgProcedure,
  protectedProcedure,
} from "~/server/api/trpc";

import {
  getAuditLogsSchema,
  getAuditLogByIdSchema,
  getAuditStatsSchema,
  exportAuditLogsSchema,
} from "~/lib/validations/audit";

import { getAuditLogs, getAuditStats, AuditLogger, createAuditContext } from "~/lib/security";

export const auditRouter = createTRPCRouter({
  // Get audit logs (admin and org admin)
  getLogs: orgProcedure
    .input(getAuditLogsSchema)
    .query(async ({ ctx, input }) => {
      const { organizationId, search, ...params } = input;

      // Only certain roles can view audit logs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view audit logs",
        });
      }

      // Super admin can view any organization, others only their own
      let targetOrgId = organizationId;
      if (ctx.session.user.role !== "SUPER_ADMIN") {
        targetOrgId = ctx.session.user.organizationId!;
      }

      // Build search conditions
      const searchConditions = search ? {
        OR: [
          { userEmail: { contains: search, mode: "insensitive" as const } },
          { userName: { contains: search, mode: "insensitive" as const } },
          { resourceId: { contains: search, mode: "insensitive" as const } },
        ],
      } : {};

      const result = await getAuditLogs({
        ...params,
        organizationId: targetOrgId,
      });

      // If search is provided, filter results in memory (not ideal for large datasets)
      if (search) {
        const filteredLogs = result.logs.filter(log => 
          log.userEmail?.toLowerCase().includes(search.toLowerCase()) ||
          log.userName?.toLowerCase().includes(search.toLowerCase()) ||
          log.resourceId?.toLowerCase().includes(search.toLowerCase()) ||
          log.action.toLowerCase().includes(search.toLowerCase())
        );

        return {
          ...result,
          logs: filteredLogs,
          pagination: {
            ...result.pagination,
            total: filteredLogs.length,
          },
        };
      }

      return result;
    }),

  // Get specific audit log
  getById: orgProcedure
    .input(getAuditLogByIdSchema)
    .query(async ({ ctx, input }) => {
      const { logId } = input;

      // Only certain roles can view audit logs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view audit logs",
        });
      }

      const log = await ctx.db.auditLog.findFirst({
        where: {
          id: logId,
          // Non-super admins can only view logs from their org
          ...(ctx.session.user.role !== "SUPER_ADMIN" && {
            organizationId: ctx.session.user.organizationId!,
          }),
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      if (!log) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Audit log not found or you don't have permission to view it",
        });
      }

      return log;
    }),

  // Get audit statistics
  getStats: orgProcedure
    .input(getAuditStatsSchema)
    .query(async ({ ctx, input }) => {
      const { organizationId, startDate, endDate } = input;

      // Only certain roles can view audit statistics
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view audit statistics",
        });
      }

      // Super admin can view any organization, others only their own
      let targetOrgId = organizationId;
      if (ctx.session.user.role !== "SUPER_ADMIN") {
        targetOrgId = ctx.session.user.organizationId!;
      }

      const stats = await getAuditStats({
        organizationId: targetOrgId,
        startDate,
        endDate,
      });

      return stats;
    }),

  // Export audit logs (admin only for now)
  exportLogs: adminProcedure
    .input(exportAuditLogsSchema)
    .mutation(async ({ ctx, input }) => {
      const { organizationId, startDate, endDate, format, actions, resourceTypes } = input;

      // Create audit context for logging this export
      const auditContext = createAuditContext(ctx.session);
      const auditLogger = new AuditLogger(auditContext);

      try {
        // Get logs for export
        const where: any = {
          ...(organizationId && { organizationId }),
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(actions && { action: { in: actions } }),
          ...(resourceTypes && { resourceType: { in: resourceTypes } }),
        };

        const logs = await ctx.db.auditLog.findMany({
          where,
          include: {
            user: {
              select: {
                email: true,
                name: true,
                role: true,
              },
            },
            organization: {
              select: {
                name: true,
                slug: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        });

        // Log the export action
        await auditLogger.logDataExport("SYSTEM", format, logs.length);

        if (format === "JSON") {
          return {
            format: "JSON",
            data: logs,
            exportedAt: new Date(),
            recordCount: logs.length,
          };
        } else {
          // Convert to CSV format
          const csvHeaders = [
            "Timestamp",
            "User",
            "User Email",
            "Organization",
            "Action",
            "Resource Type",
            "Resource ID",
            "Success",
            "IP Address",
            "User Agent",
          ];

          const csvRows = logs.map(log => [
            log.createdAt.toISOString(),
            log.userName || "-",
            log.userEmail || "-",
            log.organization?.name || "-",
            log.action,
            log.resourceType,
            log.resourceId || "-",
            log.success ? "Yes" : "No",
            log.ipAddress || "-",
            log.userAgent || "-",
          ]);

          const csvContent = [
            csvHeaders.join(","),
            ...csvRows.map(row => row.map(cell => `"${cell}"`).join(",")),
          ].join("\n");

          return {
            format: "CSV",
            data: csvContent,
            exportedAt: new Date(),
            recordCount: logs.length,
          };
        }
      } catch (error) {
        await auditLogger.logDataExport("SYSTEM", format, 0);
        throw error;
      }
    }),

  // Get recent activity for dashboard
  getRecentActivity: orgProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      organizationId: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { limit, organizationId } = input;

      // Only certain roles can view audit logs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view recent activity",
        });
      }

      // Super admin can view any organization, others only their own
      let targetOrgId = organizationId;
      if (ctx.session.user.role !== "SUPER_ADMIN") {
        targetOrgId = ctx.session.user.organizationId!;
      }

      const logs = await ctx.db.auditLog.findMany({
        where: {
          organizationId: targetOrgId,
          // Exclude some noisy actions from recent activity
          action: {
            notIn: ["USER_LOGIN", "USER_LOGOUT"],
          },
        },
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      return logs;
    }),

  // Get security events (failed logins, unauthorized access, etc.)
  getSecurityEvents: adminProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      organizationId: z.string().optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, organizationId, startDate, endDate } = input;
      const skip = (page - 1) * limit;

      const where: any = {
        ...(organizationId && { organizationId }),
        ...(startDate && endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }),
        action: {
          in: [
            "USER_LOGIN_FAILED",
            "UNAUTHORIZED_ACCESS_ATTEMPTED",
            "SUSPICIOUS_ACTIVITY_DETECTED",
            "API_RATE_LIMIT_EXCEEDED",
          ],
        },
      };

      const [logs, total] = await Promise.all([
        ctx.db.auditLog.findMany({
          where,
          skip,
          take: limit,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
                role: true,
              },
            },
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        ctx.db.auditLog.count({ where }),
      ]);

      return {
        logs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // Log user logout
  logLogout: protectedProcedure
    .mutation(async ({ ctx }) => {
      const auditContext = createAuditContext(ctx.session);
      const auditor = new AuditLogger(auditContext);
      
      await auditor.logLogout();
      
      return { success: true };
    }),
});