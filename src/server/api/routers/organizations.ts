import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  createTRPCRouter,
  adminProcedure,
  orgProcedure,
} from "~/server/api/trpc";

import {
  getOrganizationsSchema,
  getOrganizationByIdSchema,
  createOrganizationSchema,
  updateOrganizationSchema,
  deleteOrganizationSchema,
  getOrganizationSettingsSchema,
  updateOrganizationSettingsSchema,
  getOrganizationStatsSchema,
} from "~/lib/validations/organizations";

export const organizationsRouter = createTRPCRouter({
  // Get all organizations (admin only)
  getAll: adminProcedure
    .input(getOrganizationsSchema)
    .query(async ({ ctx, input }) => {
      const { page, limit, search } = input;
      const skip = (page - 1) * limit;

      const where = search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" as const } },
              { slug: { contains: search, mode: "insensitive" as const } },
            ],
          }
        : {};

      const [organizations, total] = await Promise.all([
        ctx.db.organization.findMany({
          where,
          skip,
          take: limit,
          select: {
            id: true,
            name: true,
            slug: true,
            settings: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                users: true,
                jobs: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        ctx.db.organization.count({ where }),
      ]);

      return {
        organizations,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // Get current user's organization
  getCurrent: orgProcedure.query(async ({ ctx }) => {
    if (!ctx.session.user.organizationId) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User is not associated with any organization",
      });
    }

    const organization = await ctx.db.organization.findUnique({
      where: { id: ctx.session.user.organizationId },
      select: {
        id: true,
        name: true,
        slug: true,
        settings: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            users: true,
            jobs: true,
          },
        },
      },
    });

    if (!organization) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Organization not found",
      });
    }

    return organization;
  }),

  // Get organization by ID (admin or members)
  getById: orgProcedure
    .input(getOrganizationByIdSchema)
    .query(async ({ ctx, input }) => {
      const { organizationId } = input;

      // Check permissions - super admin can view any, others only their own
      if (
        ctx.session.user.role !== "SUPER_ADMIN" &&
        ctx.session.user.organizationId !== organizationId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view this organization",
        });
      }

      const organization = await ctx.db.organization.findUnique({
        where: { id: organizationId },
        select: {
          id: true,
          name: true,
          slug: true,
          settings: true,
          createdAt: true,
          updatedAt: true,
          users: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
              createdAt: true,
            },
            orderBy: { createdAt: "desc" },
          },
          _count: {
            select: {
              users: true,
              jobs: true,
            },
          },
        },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      return organization;
    }),

  // Create organization (admin only)
  create: adminProcedure
    .input(createOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      const { name, settings } = input;

      // Create organization slug
      const slug = name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-|-$/g, "");

      // Check if organization slug exists
      const existingOrg = await ctx.db.organization.findUnique({
        where: { slug },
      });

      if (existingOrg) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Organization with this name already exists",
        });
      }

      const organization = await ctx.db.organization.create({
        data: {
          name,
          slug,
          settings,
        },
        select: {
          id: true,
          name: true,
          slug: true,
          settings: true,
          createdAt: true,
        },
      });

      return organization;
    }),

  // Update organization (admin or org admin)
  update: orgProcedure
    .input(updateOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      const { organizationId, name, settings } = input;

      // Check permissions
      const canUpdate =
        ctx.session.user.role === "SUPER_ADMIN" ||
        (ctx.session.user.organizationId === organizationId &&
          ["ORG_ADMIN"].includes(ctx.session.user.role));

      if (!canUpdate) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to update this organization",
        });
      }

      const updateData: any = {};
      if (name !== undefined) {
        // Update slug if name changes
        updateData.name = name;
        updateData.slug = name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/^-|-$/g, "");

        // Check if new slug conflicts
        const existingOrg = await ctx.db.organization.findFirst({
          where: {
            slug: updateData.slug,
            id: { not: organizationId },
          },
        });

        if (existingOrg) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Organization with this name already exists",
          });
        }
      }
      
      if (settings !== undefined) {
        updateData.settings = settings;
      }

      const updatedOrganization = await ctx.db.organization.update({
        where: { id: organizationId },
        data: updateData,
        select: {
          id: true,
          name: true,
          slug: true,
          settings: true,
          updatedAt: true,
        },
      });

      return updatedOrganization;
    }),

  // Delete organization (admin only)
  delete: adminProcedure
    .input(deleteOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      const { organizationId } = input;

      // Check if organization has users
      const userCount = await ctx.db.user.count({
        where: { organizationId },
      });

      if (userCount > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete organization with existing users. Please remove all users first.",
        });
      }

      await ctx.db.organization.delete({
        where: { id: organizationId },
      });

      return { success: true };
    }),

  // Get organization statistics (admin or org admin)
  getStats: orgProcedure
    .input(getOrganizationStatsSchema)
    .query(async ({ ctx, input }) => {
      let { organizationId } = input;

      // If no organizationId provided, use current user's organization
      if (!organizationId) {
        organizationId = ctx.session.user.organizationId || undefined;
      }

      // Check permissions
      const canView =
        ctx.session.user.role === "SUPER_ADMIN" ||
        ctx.session.user.organizationId === organizationId;

      if (!canView) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view this organization's statistics",
        });
      }

      if (!organizationId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      const [
        totalUsers,
        adminCount,
        hiringManagerCount,
        recruiterCount,
        salesRepCount,
        totalJobs,
        activeJobs,
        recentUsers,
      ] = await Promise.all([
        ctx.db.user.count({ where: { organizationId } }),
        ctx.db.user.count({ where: { organizationId, role: "ORG_ADMIN" } }),
        ctx.db.user.count({ where: { organizationId, role: "HIRING_MANAGER" } }),
        ctx.db.user.count({ where: { organizationId, role: "RECRUITER" } }),
        ctx.db.user.count({ where: { organizationId, role: "SALES_REP" } }),
        ctx.db.job.count({ where: { organizationId } }),
        ctx.db.job.count({ where: { organizationId, status: "PUBLISHED" } }),
        ctx.db.user.count({
          where: {
            organizationId,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        }),
      ]);

      return {
        totalUsers,
        usersByRole: {
          admins: adminCount,
          hiringManagers: hiringManagerCount,
          recruiters: recruiterCount,
          salesReps: salesRepCount,
        },
        totalJobs,
        activeJobs,
        recentUsers,
      };
    }),

  // Get organization settings (admin or org admin)
  getSettings: orgProcedure
    .input(getOrganizationSettingsSchema)
    .query(async ({ ctx, input }) => {
      let { organizationId } = input;

      // If no organizationId provided, use current user's organization
      if (!organizationId) {
        organizationId = ctx.session.user.organizationId || undefined;
      }

      // Check permissions
      const canView =
        ctx.session.user.role === "SUPER_ADMIN" ||
        ctx.session.user.organizationId === organizationId;

      if (!canView) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view this organization's settings",
        });
      }

      if (!organizationId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      const organization = await ctx.db.organization.findUnique({
        where: { id: organizationId },
        select: {
          id: true,
          name: true,
          slug: true,
          settings: true,
        },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      return organization.settings || {};
    }),

  // Update organization settings (admin or org admin)
  updateSettings: orgProcedure
    .input(updateOrganizationSettingsSchema)
    .mutation(async ({ ctx, input }) => {
      let { organizationId, settings } = input;

      // If no organizationId provided, use current user's organization
      if (!organizationId) {
        organizationId = ctx.session.user.organizationId || undefined;
      }

      // Check permissions
      const canUpdate =
        ctx.session.user.role === "SUPER_ADMIN" ||
        (ctx.session.user.organizationId === organizationId &&
          ["ORG_ADMIN"].includes(ctx.session.user.role));

      if (!canUpdate) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to update this organization's settings",
        });
      }

      if (!organizationId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      const updatedOrganization = await ctx.db.organization.update({
        where: { id: organizationId },
        data: { settings },
        select: {
          id: true,
          settings: true,
          updatedAt: true,
        },
      });

      return updatedOrganization.settings || {};
    }),
});