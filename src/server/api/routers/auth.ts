import { TRPCError } from "@trpc/server";
import { z } from "zod";
import bcrypt from "bcryptjs";
import { UserRole } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
  adminProcedure,
} from "~/server/api/trpc";

import {
  signUpCandidateSchema,
  signUpBusinessSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  enable2FASchema,
  verify2FASchema,
  disable2FASchema,
  updateProfileSchema,
} from "~/lib/validations/auth";

import { AuditLogger, createAuditContext } from "~/lib/security";

export const authRouter = createTRPCRouter({
  // Public procedures for authentication
  signUpCandidate: publicProcedure
    .input(signUpCandidateSchema)
    .mutation(async ({ ctx, input }) => {
      const { email, password, name } = input;

      // Check if user already exists
      const existingUser = await ctx.db.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User with this email already exists",
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user
      const user = await ctx.db.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          role: UserRole.CANDIDATE,
          emailVerified: new Date(), // Auto-verify for development
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
        },
      });

      // Send welcome email to candidate
      const { sendWelcomeEmail } = await import("~/lib/email");
      await sendWelcomeEmail(email, name);

      // Log user creation
      const auditContext = createAuditContext(null);
      const auditLogger = new AuditLogger(auditContext);
      await auditLogger.logUserCreated(user);

      return {
        success: true,
        user,
      };
    }),

  signUpBusiness: publicProcedure
    .input(signUpBusinessSchema)
    .mutation(async ({ ctx, input }) => {
      const { email, password, name, organizationName } = input;

      // Validate business email domain (basic check)
      const emailDomain = email.split("@")[1];
      const personalDomains = [
        "gmail.com",
        "yahoo.com",
        "hotmail.com",
        "outlook.com",
        "icloud.com",
      ];

      if (personalDomains.includes(emailDomain?.toLowerCase() || "")) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Please use a business email address",
        });
      }

      // Check if user already exists
      const existingUser = await ctx.db.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User with this email already exists",
        });
      }

      // Create organization slug
      const slug = organizationName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-|-$/g, "");

      // Check if organization slug exists
      const existingOrg = await ctx.db.organization.findUnique({
        where: { slug },
      });

      if (existingOrg) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Organization with this name already exists",
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create organization and user in transaction
      const result = await ctx.db.$transaction(async (tx) => {
        const organization = await tx.organization.create({
          data: {
            name: organizationName,
            slug,
          },
        });

        const user = await tx.user.create({
          data: {
            email,
            password: hashedPassword,
            name,
            role: UserRole.ORG_ADMIN,
            organizationId: organization.id,
            emailVerified: new Date(), // Auto-verify for development
          },
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            organizationId: true,
          },
        });

        return { user, organization };
      });

      return {
        success: true,
        user: result.user,
        organization: result.organization,
      };
    }),

  forgotPassword: publicProcedure
    .input(forgotPasswordSchema)
    .mutation(async ({ ctx, input }) => {
      const { email } = input;

      const user = await ctx.db.user.findUnique({
        where: { email },
      });

      if (!user) {
        // Don't reveal if user exists for security
        return {
          success: true,
          message: "If an account with that email exists, a reset link has been sent.",
        };
      }

      // Generate reset token
      const resetToken = crypto.randomUUID();
      const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      await ctx.db.user.update({
        where: { email },
        data: {
          resetToken,
          resetTokenExpiry,
        },
      });

      // Send password reset email
      const { sendPasswordResetEmail } = await import("~/lib/email");
      const emailSent = await sendPasswordResetEmail(email, resetToken);

      if (!emailSent) {
        console.error(`Failed to send password reset email to ${email}`);
        // Don't throw error to avoid revealing if email exists
      }

      return {
        success: true,
        message: "If an account with that email exists, a reset link has been sent.",
      };
    }),

  resetPassword: publicProcedure
    .input(resetPasswordSchema)
    .mutation(async ({ ctx, input }) => {
      const { token, password } = input;

      const user = await ctx.db.user.findFirst({
        where: {
          resetToken: token,
          resetTokenExpiry: {
            gt: new Date(),
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid or expired reset token",
        });
      }

      const hashedPassword = await bcrypt.hash(password, 12);

      await ctx.db.user.update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          resetToken: null,
          resetTokenExpiry: null,
        },
      });

      return {
        success: true,
        message: "Password has been reset successfully",
      };
    }),

  // Protected procedures for authenticated users
  getCurrentUser: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        organizationId: true,
        twoFactorEnabled: true,
        createdAt: true,
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    return user;
  }),

  updateProfile: protectedProcedure
    .input(updateProfileSchema)
    .mutation(async ({ ctx, input }) => {
      const updatedUser = await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          name: input.name,
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
        },
      });

      return updatedUser;
    }),

  // 2FA procedures for admin users
  get2FAStatus: adminProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: {
        twoFactorEnabled: true,
        twoFactorBackupCodes: true,
      },
    });

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    return {
      enabled: user.twoFactorEnabled,
      hasBackupCodes: user.twoFactorBackupCodes && user.twoFactorBackupCodes.length > 0,
    };
  }),

  generate2FASecret: adminProcedure.mutation(async ({ ctx }) => {
    const { generateTOTPSecret, generateQRCode } = await import("~/lib/two-factor");
    
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: { email: true, name: true },
    });

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    const totpData = generateTOTPSecret(user.email, "Sourceflex");
    const qrCodeUrl = await generateQRCode(totpData.uri);

    return {
      secret: totpData.secret,
      qrCodeUrl,
    };
  }),

  enable2FA: adminProcedure
    .input(enable2FASchema)
    .mutation(async ({ ctx, input }) => {
      const { verifyTOTP, generateBackupCodes } = await import("~/lib/two-factor");
      
      const { secret, token } = input;

      // Verify the TOTP token
      const isValid = verifyTOTP(token, secret);
      if (!isValid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid authentication code",
        });
      }

      // Generate backup codes
      const backupCodes = generateBackupCodes();

      // Update user with 2FA settings
      await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          twoFactorSecret: secret,
          twoFactorEnabled: true,
          twoFactorBackupCodes: backupCodes,
        },
      });

      return {
        success: true,
        backupCodes,
      };
    }),

  verify2FA: adminProcedure
    .input(verify2FASchema)
    .mutation(async ({ ctx, input }) => {
      const { verifyTOTP } = await import("~/lib/two-factor");
      
      const { token } = input;

      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: {
          twoFactorSecret: true,
          twoFactorEnabled: true,
          twoFactorBackupCodes: true,
        },
      });

      if (!user?.twoFactorEnabled || !user.twoFactorSecret) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Two-factor authentication is not enabled",
        });
      }

      // Check if it's a backup code
      if (user.twoFactorBackupCodes?.includes(token)) {
        // Remove used backup code
        const updatedBackupCodes = user.twoFactorBackupCodes.filter(code => code !== token);
        
        await ctx.db.user.update({
          where: { id: ctx.session.user.id },
          data: {
            twoFactorBackupCodes: updatedBackupCodes,
          },
        });

        return { success: true, usedBackupCode: true };
      }

      // Verify TOTP token
      const isValid = verifyTOTP(token, user.twoFactorSecret);
      if (!isValid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid authentication code",
        });
      }

      return { success: true };
    }),

  disable2FA: adminProcedure
    .input(disable2FASchema)
    .mutation(async ({ ctx, input }) => {
      const { verifyTOTP } = await import("~/lib/two-factor");
      
      const { token } = input;

      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: {
          twoFactorSecret: true,
          twoFactorEnabled: true,
          twoFactorBackupCodes: true,
        },
      });

      if (!user?.twoFactorEnabled || !user.twoFactorSecret) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Two-factor authentication is not enabled",
        });
      }

      // Verify current TOTP token or backup code
      let isValid = false;
      
      if (user.twoFactorBackupCodes?.includes(token)) {
        isValid = true;
      } else {
        isValid = verifyTOTP(token, user.twoFactorSecret);
      }

      if (!isValid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid authentication code",
        });
      }

      // Disable 2FA
      await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          twoFactorSecret: null,
          twoFactorEnabled: false,
          twoFactorBackupCodes: [],
        },
      });

      return { success: true };
    }),
});