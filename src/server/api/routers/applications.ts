import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { ApplicationStatus } from "@prisma/client";

import {
  createTRPCRouter,
  publicProcedure,
  protectedProcedure,
  orgProcedure,
} from "~/server/api/trpc";

import {
  createApplicationSchema,
  getApplicationsSchema,
  updateApplicationSchema,
  getApplicationByIdSchema,
} from "~/lib/validations/jobs";

export const applicationsRouter = createTRPCRouter({
  // Public application submission (no auth required)
  create: publicProcedure
    .input(createApplicationSchema)
    .mutation(async ({ ctx, input }) => {
      const { jobId, ...applicationData } = input;

      // Verify job exists and is published
      const job = await ctx.db.job.findFirst({
        where: {
          id: jobId,
          status: "PUBLISHED",
          publishedAt: { lte: new Date() },
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!job) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found or no longer accepting applications",
        });
      }

      // Check for duplicate application
      const existingApplication = await ctx.db.application.findFirst({
        where: {
          jobId,
          email: applicationData.email,
        },
      });

      if (existingApplication) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "You have already applied for this position",
        });
      }

      const application = await ctx.db.application.create({
        data: {
          ...applicationData,
          jobId,
          status: "PENDING",
        },
        include: {
          job: {
            select: {
              id: true,
              title: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      // TODO: Send application confirmation email to candidate
      // TODO: Send new application notification to hiring team

      return {
        success: true,
        application: {
          id: application.id,
          status: application.status,
          appliedAt: application.appliedAt,
          job: application.job,
        },
      };
    }),

  // Get applications for organization (hiring team)
  getAll: orgProcedure
    .input(getApplicationsSchema)
    .query(async ({ ctx, input }) => {
      const { page, limit, search, jobId, status, source } = input;
      const skip = (page - 1) * limit;

      // Only certain roles can view applications
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER", "RECRUITER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view applications",
        });
      }

      const where = {
        job: {
          organizationId: ctx.session.user.organizationId!,
        },
        ...(jobId && { jobId }),
        ...(status && { status }),
        ...(source && { source }),
        ...(search && {
          OR: [
            { firstName: { contains: search, mode: "insensitive" as const } },
            { lastName: { contains: search, mode: "insensitive" as const } },
            { email: { contains: search, mode: "insensitive" as const } },
          ],
        }),
      };

      const [applications, total] = await Promise.all([
        ctx.db.application.findMany({
          where,
          skip,
          take: limit,
          include: {
            job: {
              select: {
                id: true,
                title: true,
                status: true,
                organization: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: { appliedAt: "desc" },
        }),
        ctx.db.application.count({ where }),
      ]);

      return {
        applications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // Get specific application
  getById: orgProcedure
    .input(getApplicationByIdSchema)
    .query(async ({ ctx, input }) => {
      const { applicationId } = input;

      // Only certain roles can view applications
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER", "RECRUITER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view applications",
        });
      }

      const application = await ctx.db.application.findFirst({
        where: {
          id: applicationId,
          job: {
            organizationId: ctx.session.user.organizationId!,
          },
        },
        include: {
          job: {
            select: {
              id: true,
              title: true,
              status: true,
              requirements: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Application not found or you don't have permission to view it",
        });
      }

      return application;
    }),

  // Update application status/stage
  update: orgProcedure
    .input(updateApplicationSchema)
    .mutation(async ({ ctx, input }) => {
      const { applicationId, ...updateData } = input;

      // Only certain roles can update applications
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER", "RECRUITER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to update applications",
        });
      }

      // Check if application exists and user has permission
      const existingApplication = await ctx.db.application.findFirst({
        where: {
          id: applicationId,
          job: {
            organizationId: ctx.session.user.organizationId!,
          },
        },
        include: {
          job: {
            select: {
              id: true,
              title: true,
              organization: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });

      if (!existingApplication) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Application not found or you don't have permission to update it",
        });
      }

      const updatedApplication = await ctx.db.application.update({
        where: { id: applicationId },
        data: updateData,
        include: {
          job: {
            select: {
              id: true,
              title: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      // TODO: Send status update email to candidate if status changed
      // if (updateData.status && updateData.status !== existingApplication.status) {
      //   await sendApplicationStatusUpdateEmail(
      //     updatedApplication.email,
      //     updatedApplication.firstName,
      //     updatedApplication.job.title,
      //     updatedApplication.job.organization.name,
      //     updateData.status
      //   );
      // }

      return updatedApplication;
    }),

  // Get applications for a candidate (if authenticated)
  getMy: protectedProcedure
    .input(getApplicationsSchema.omit({ search: true }))
    .query(async ({ ctx, input }) => {
      const { page, limit, status } = input;
      const skip = (page - 1) * limit;

      // Only candidates can view their own applications
      if (ctx.session.user.role !== "CANDIDATE") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Only candidates can view their applications",
        });
      }

      const where = {
        email: ctx.session.user.email!,
        ...(status && { status }),
      };

      const [applications, total] = await Promise.all([
        ctx.db.application.findMany({
          where,
          skip,
          take: limit,
          include: {
            job: {
              select: {
                id: true,
                title: true,
                status: true,
                locationType: true,
                city: true,
                country: true,
                employmentType: true,
                organization: {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
          orderBy: { appliedAt: "desc" },
        }),
        ctx.db.application.count({ where }),
      ]);

      return {
        applications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // Bulk update applications
  bulkUpdate: orgProcedure
    .input(z.object({
      applicationIds: z.array(z.string()).min(1, "At least one application ID is required"),
      updates: z.object({
        status: z.nativeEnum(ApplicationStatus).optional(),
        stage: z.string().optional().nullable(),
      }),
    }))
    .mutation(async ({ ctx, input }) => {
      const { applicationIds, updates } = input;

      // Only certain roles can bulk update applications
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to bulk update applications",
        });
      }

      // Verify all applications belong to user's organization
      const applications = await ctx.db.application.findMany({
        where: {
          id: { in: applicationIds },
          job: {
            organizationId: ctx.session.user.organizationId!,
          },
        },
        select: { id: true },
      });

      if (applications.length !== applicationIds.length) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Some applications were not found or you don't have permission to update them",
        });
      }

      const updatedApplications = await ctx.db.application.updateMany({
        where: {
          id: { in: applicationIds },
          job: {
            organizationId: ctx.session.user.organizationId!,
          },
        },
        data: updates,
      });

      return {
        success: true,
        updatedCount: updatedApplications.count,
      };
    }),

  // Get application statistics
  getStats: orgProcedure
    .input(z.object({
      jobId: z.string().optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { jobId, startDate, endDate } = input;

      // Only certain roles can view application statistics
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER", "RECRUITER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view application statistics",
        });
      }

      const dateFilter = startDate && endDate ? {
        appliedAt: {
          gte: startDate,
          lte: endDate,
        },
      } : {};

      const baseWhere = {
        job: {
          organizationId: ctx.session.user.organizationId!,
        },
        ...(jobId && { jobId }),
        ...dateFilter,
      };

      const [
        totalApplications,
        applicationsByStatus,
        applicationsBySource,
        recentApplications,
      ] = await Promise.all([
        ctx.db.application.count({ where: baseWhere }),
        ctx.db.application.groupBy({
          by: ['status'],
          where: baseWhere,
          _count: true,
        }),
        ctx.db.application.groupBy({
          by: ['source'],
          where: baseWhere,
          _count: true,
        }),
        ctx.db.application.count({
          where: {
            ...baseWhere,
            appliedAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
      ]);

      return {
        totalApplications,
        applicationsByStatus: applicationsByStatus.reduce((acc, stat) => {
          acc[stat.status] = stat._count;
          return acc;
        }, {} as Record<string, number>),
        applicationsBySource: applicationsBySource.reduce((acc, stat) => {
          acc[stat.source] = stat._count;
          return acc;
        }, {} as Record<string, number>),
        recentApplications,
      };
    }),
});