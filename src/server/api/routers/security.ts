import { z } from "zod"
import { createTR<PERSON><PERSON>outer, protectedProcedure, adminProcedure } from "@/server/api/trpc"
import { SecurityPolicyService } from "@/lib/security-policy"
import { SessionManager } from "@/lib/session-manager"
import { <PERSON><PERSON><PERSON>ForceProtection } from "@/lib/brute-force-protection"
import { PasswordSecurityService } from "@/lib/password-security"
import { WebAuthnService } from "@/lib/webauthn"
import { PermissionService } from "@/lib/permissions"
import { ApiRateLimiter } from "@/lib/api-rate-limiter"
import { db } from "@/lib/db"

export const securityRouter = createTRPCRouter({
  // Security Policy Management
  getSecurityPolicy: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Check permissions
      const hasPermission = await PermissionService.hasPermission(
        ctx.session.user.id,
        "organization.read"
      )
      
      if (!hasPermission.allowed) {
        throw new Error("Insufficient permissions")
      }

      return await SecurityPolicyService.getPolicy(input.organizationId)
    }),

  updateSecurityPolicy: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      updates: z.object({
        minPasswordLength: z.number().min(8).max(50).optional(),
        requireUppercase: z.boolean().optional(),
        requireLowercase: z.boolean().optional(),
        requireNumbers: z.boolean().optional(),
        requireSpecialChars: z.boolean().optional(),
        passwordHistoryCount: z.number().min(0).max(24).optional(),
        passwordExpiryDays: z.number().min(30).max(365).nullable().optional(),
        require2FA: z.boolean().optional(),
        require2FAForAdmins: z.boolean().optional(),
        allowSMS2FA: z.boolean().optional(),
        allowTOTP2FA: z.boolean().optional(),
        allowWebAuthn: z.boolean().optional(),
        maxConcurrentSessions: z.number().min(1).max(50).optional(),
        sessionTimeoutMinutes: z.number().min(15).max(1440).optional(),
        requireReauthForSensitive: z.boolean().optional(),
        allowedIpRanges: z.array(z.string()).optional(),
        blockedIpRanges: z.array(z.string()).optional(),
        maxFailedAttempts: z.number().min(3).max(20).optional(),
        lockoutDurationMinutes: z.number().min(5).max(1440).optional(),
      }),
    }))
    .mutation(async ({ ctx, input }) => {
      const hasPermission = await PermissionService.hasPermission(
        ctx.session.user.id,
        "organization.update"
      )
      
      if (!hasPermission.allowed) {
        throw new Error("Insufficient permissions")
      }

      return await SecurityPolicyService.updatePolicy(
        input.organizationId,
        input.updates,
        ctx.session.user.id
      )
    }),

  getSecurityRecommendations: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const hasPermission = await PermissionService.hasPermission(
        ctx.session.user.id,
        "organization.read"
      )
      
      if (!hasPermission.allowed) {
        throw new Error("Insufficient permissions")
      }

      return await SecurityPolicyService.getSecurityRecommendations(input.organizationId)
    }),

  // Session Management
  getUserSessions: protectedProcedure
    .query(async ({ ctx }) => {
      return await SessionManager.getUserSessions(ctx.session.user.id)
    }),

  terminateSession: protectedProcedure
    .input(z.object({ sessionToken: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Users can only terminate their own sessions (except admins)
      if (ctx.session.user.role !== "SUPER_ADMIN") {
        const session = await db.session.findUnique({
          where: { sessionToken: input.sessionToken },
        })
        
        if (!session || session.userId !== ctx.session.user.id) {
          throw new Error("Session not found or access denied")
        }
      }

      await SessionManager.deactivateSession(input.sessionToken, "admin")
      return { success: true }
    }),

  terminateAllSessions: protectedProcedure
    .mutation(async ({ ctx }) => {
      const count = await SessionManager.terminateAllUserSessions(
        ctx.session.user.id
      )
      return { terminatedSessions: count }
    }),

  // Password Security
  validatePassword: protectedProcedure
    .input(z.object({
      password: z.string(),
      organizationId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      return await PasswordSecurityService.validatePassword(
        input.password,
        input.organizationId,
        ctx.session.user.id
      )
    }),

  checkPasswordBreach: protectedProcedure
    .input(z.object({ password: z.string() }))
    .mutation(async ({ input }) => {
      return await PasswordSecurityService.checkPasswordBreach(input.password)
    }),

  generateSecurePassword: protectedProcedure
    .input(z.object({ length: z.number().min(12).max(64).default(16) }))
    .mutation(async ({ input }) => {
      return {
        password: PasswordSecurityService.generateSecurePassword(input.length),
      }
    }),

  // WebAuthn Management
  getUserWebAuthnCredentials: protectedProcedure
    .query(async ({ ctx }) => {
      return await WebAuthnService.getUserCredentials(ctx.session.user.id)
    }),

  removeWebAuthnCredential: protectedProcedure
    .input(z.object({ credentialId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await WebAuthnService.removeCredential(
        input.credentialId,
        ctx.session.user.id
      )
    }),

  // Login Attempt Analysis
  getLoginAttempts: protectedProcedure
    .input(z.object({
      email: z.string().email().optional(),
      hours: z.number().min(1).max(168).default(24),
    }))
    .query(async ({ ctx, input }) => {
      // Users can only see their own attempts, admins can see all
      const email = ctx.session.user.role === "SUPER_ADMIN" 
        ? input.email 
        : ctx.session.user.email!

      return await BruteForceProtection.getRecentAttempts(email, undefined, input.hours)
    }),

  detectSuspiciousActivity: protectedProcedure
    .input(z.object({
      email: z.string().email(),
      ipAddress: z.string(),
      location: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      return await BruteForceProtection.detectSuspiciousActivity(
        input.email,
        input.ipAddress,
        input.location
      )
    }),

  unlockUserAccount: adminProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await BruteForceProtection.unlockAccount(input.userId, ctx.session.user.id)
      return { success: true }
    }),

  // Permission Management
  getUserPermissions: protectedProcedure
    .query(async ({ ctx }) => {
      return await PermissionService.getUserPermissions(ctx.session.user.id)
    }),

  createCustomRole: protectedProcedure
    .input(z.object({
      organizationId: z.string(),
      name: z.string().min(1).max(50),
      description: z.string().max(200),
      permissions: z.array(z.string()),
    }))
    .mutation(async ({ ctx, input }) => {
      const hasPermission = await PermissionService.hasPermission(
        ctx.session.user.id,
        "users.manage"
      )
      
      if (!hasPermission.allowed) {
        throw new Error("Insufficient permissions")
      }

      return await PermissionService.createCustomRole(
        input.organizationId,
        input.name,
        input.description,
        input.permissions as any,
        ctx.session.user.id
      )
    }),

  assignRole: protectedProcedure
    .input(z.object({
      userId: z.string(),
      roleId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const hasPermission = await PermissionService.hasPermission(
        ctx.session.user.id,
        "users.manage"
      )
      
      if (!hasPermission.allowed) {
        throw new Error("Insufficient permissions")
      }

      return await PermissionService.assignRole(
        input.userId,
        input.roleId,
        ctx.session.user.id
      )
    }),

  // Security Dashboard Data
  getSecurityDashboard: protectedProcedure
    .input(z.object({ organizationId: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      const hasPermission = await PermissionService.hasPermission(
        ctx.session.user.id,
        "system.audit"
      )
      
      if (!hasPermission.allowed) {
        throw new Error("Insufficient permissions")
      }

      const orgId = input.organizationId || ctx.session.user.organizationId || undefined

      // Get security metrics
      const [
        activeSessions,
        recentFailedLogins,
        lockedAccounts,
        securityPolicy,
        auditLogCount,
      ] = await Promise.all([
        // Active sessions count
        db.session.count({
          where: {
            isActive: true,
            expires: { gt: new Date() },
            ...(orgId && {
              user: { organizationId: orgId }
            })
          }
        }),
        
        // Failed login attempts in last 24 hours
        db.loginAttempt.count({
          where: {
            success: false,
            createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
          }
        }),
        
        // Locked user accounts
        db.user.count({
          where: {
            accountLockedUntil: { gt: new Date() },
            ...(orgId && { organizationId: orgId })
          }
        }),
        
        // Security policy
        orgId ? SecurityPolicyService.getPolicy(orgId) : null,
        
        // Recent audit log entries
        db.auditLog.count({
          where: {
            createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
            ...(orgId && { organizationId: orgId })
          }
        }),
      ])

      // Get users with 2FA enabled
      const users2FAEnabled = await db.user.count({
        where: {
          twoFactorEnabled: true,
          ...(orgId && { organizationId: orgId })
        }
      })

      const totalUsers = await db.user.count({
        where: orgId ? { organizationId: orgId } : {}
      })

      return {
        metrics: {
          activeSessions,
          recentFailedLogins,
          lockedAccounts,
          auditLogCount,
          users2FAEnabled,
          totalUsers,
          twoFactorAdoption: totalUsers > 0 ? (users2FAEnabled / totalUsers * 100) : 0,
        },
        securityPolicy,
        recommendations: orgId 
          ? await SecurityPolicyService.getSecurityRecommendations(orgId)
          : [],
      }
    }),

  // Cleanup Operations (Admin only)
  cleanupSessions: adminProcedure
    .mutation(async () => {
      const count = await SessionManager.cleanupExpiredSessions()
      return { cleanedSessions: count }
    }),

  cleanupRateLimit: adminProcedure
    .mutation(async () => {
      const count = await ApiRateLimiter.cleanup()
      return { cleanedEntries: count }
    }),

  cleanupLoginAttempts: adminProcedure
    .mutation(async () => {
      const count = await BruteForceProtection.cleanup()
      return { cleanedAttempts: count }
    }),
})