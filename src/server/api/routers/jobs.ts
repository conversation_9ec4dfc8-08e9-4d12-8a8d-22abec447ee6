import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { JobStatus } from "@prisma/client";

import {
  createTRPCRouter,
  publicProcedure,
  protectedProcedure,
  orgProcedure,
} from "~/server/api/trpc";

import {
  createJobSchema,
  updateJobSchema,
  getJobsSchema,
  getJobByIdSchema,
  deleteJobSchema,
  publishJobSchema,
  bulkUpdateJobsSchema,
  createApplicationSchema,
  getApplicationsSchema,
  updateApplicationSchema,
  getApplicationByIdSchema,
  getJobStatsSchema,
  getPublicJobsSchema,
  getPublicJobSchema,
} from "~/lib/validations/jobs";

export const jobsRouter = createTRPCRouter({
  // Job Management (Organization members only)
  create: orgProcedure
    .input(createJobSchema)
    .mutation(async ({ ctx, input }) => {
      // Only certain roles can create jobs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDE<PERSON>",
          message: "You don't have permission to create jobs",
        });
      }

      const job = await ctx.db.job.create({
        data: {
          ...input,
          organizationId: ctx.session.user.organizationId!,
          benefits: input.benefits || [],
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
        },
      });

      return job;
    }),

  update: orgProcedure
    .input(updateJobSchema)
    .mutation(async ({ ctx, input }) => {
      const { jobId, ...updateData } = input;

      // Check if job exists and user has permission
      const existingJob = await ctx.db.job.findFirst({
        where: {
          id: jobId,
          organizationId: ctx.session.user.organizationId!,
        },
      });

      if (!existingJob) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found or you don't have permission to edit it",
        });
      }

      // Only certain roles can edit jobs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to edit jobs",
        });
      }

      const updatedJob = await ctx.db.job.update({
        where: { id: jobId },
        data: updateData,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
        },
      });

      return updatedJob;
    }),

  getAll: orgProcedure
    .input(getJobsSchema)
    .query(async ({ ctx, input }) => {
      const { page, limit, search, status, locationType, employmentType, experience, city, country, salaryMin, salaryMax, organizationId } = input;
      const skip = (page - 1) * limit;

      // Super admin can view all jobs, others only their organization
      const orgFilter = ctx.session.user.role === "SUPER_ADMIN" && organizationId
        ? { organizationId }
        : { organizationId: ctx.session.user.organizationId! };

      const where = {
        ...orgFilter,
        ...(status && { status }),
        ...(locationType && { locationType }),
        ...(employmentType && { employmentType }),
        ...(experience && { experience }),
        ...(city && { city: { contains: city, mode: "insensitive" as const } }),
        ...(country && { country }),
        ...(salaryMin && { salaryMin: { gte: salaryMin } }),
        ...(salaryMax && { salaryMax: { lte: salaryMax } }),
        ...(search && {
          OR: [
            { title: { contains: search, mode: "insensitive" as const } },
            { description: { contains: search, mode: "insensitive" as const } },
          ],
        }),
      };

      const [jobs, total] = await Promise.all([
        ctx.db.job.findMany({
          where,
          skip,
          take: limit,
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            _count: {
              select: {
                applications: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        ctx.db.job.count({ where }),
      ]);

      return {
        jobs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  getById: orgProcedure
    .input(getJobByIdSchema)
    .query(async ({ ctx, input }) => {
      const { jobId } = input;

      const job = await ctx.db.job.findFirst({
        where: {
          id: jobId,
          organizationId: ctx.session.user.organizationId!,
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          applications: {
            orderBy: { appliedAt: "desc" },
          },
          _count: {
            select: {
              applications: true,
            },
          },
        },
      });

      if (!job) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found or you don't have permission to view it",
        });
      }

      return job;
    }),

  delete: orgProcedure
    .input(deleteJobSchema)
    .mutation(async ({ ctx, input }) => {
      const { jobId } = input;

      // Check if job exists and user has permission
      const existingJob = await ctx.db.job.findFirst({
        where: {
          id: jobId,
          organizationId: ctx.session.user.organizationId!,
        },
        include: {
          _count: {
            select: {
              applications: true,
            },
          },
        },
      });

      if (!existingJob) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found or you don't have permission to delete it",
        });
      }

      // Only certain roles can delete jobs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to delete jobs",
        });
      }

      // Check if job has applications
      if (existingJob._count.applications > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete job with existing applications. Please close the job instead.",
        });
      }

      await ctx.db.job.delete({
        where: { id: jobId },
      });

      return { success: true };
    }),

  publish: orgProcedure
    .input(publishJobSchema)
    .mutation(async ({ ctx, input }) => {
      const { jobId, publishedAt, expiresAt } = input;

      // Check if job exists and user has permission
      const existingJob = await ctx.db.job.findFirst({
        where: {
          id: jobId,
          organizationId: ctx.session.user.organizationId!,
        },
      });

      if (!existingJob) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found or you don't have permission to publish it",
        });
      }

      // Only certain roles can publish jobs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to publish jobs",
        });
      }

      const updatedJob = await ctx.db.job.update({
        where: { id: jobId },
        data: {
          status: "PUBLISHED",
          publishedAt: publishedAt || new Date(),
          expiresAt,
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      return updatedJob;
    }),

  bulkUpdate: orgProcedure
    .input(bulkUpdateJobsSchema)
    .mutation(async ({ ctx, input }) => {
      const { jobIds, updates } = input;

      // Only certain roles can bulk update jobs
      const allowedRoles = ["SUPER_ADMIN", "ORG_ADMIN", "HIRING_MANAGER"];
      if (!allowedRoles.includes(ctx.session.user.role)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to bulk update jobs",
        });
      }

      // Verify all jobs belong to user's organization
      const jobs = await ctx.db.job.findMany({
        where: {
          id: { in: jobIds },
          organizationId: ctx.session.user.organizationId!,
        },
        select: { id: true },
      });

      if (jobs.length !== jobIds.length) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Some jobs were not found or you don't have permission to update them",
        });
      }

      const updatedJobs = await ctx.db.job.updateMany({
        where: {
          id: { in: jobIds },
          organizationId: ctx.session.user.organizationId!,
        },
        data: updates,
      });

      return {
        success: true,
        updatedCount: updatedJobs.count,
      };
    }),

  // Public job board endpoints (no authentication required)
  getPublic: publicProcedure
    .input(getPublicJobsSchema)
    .query(async ({ ctx, input }) => {
      const { page, limit, search, locationType, employmentType, experience, city, country, salaryMin, organizationSlug } = input;
      const skip = (page - 1) * limit;

      const where = {
        status: "PUBLISHED" as JobStatus,
        publishedAt: { lte: new Date() },
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
        ...(locationType && { locationType }),
        ...(employmentType && { employmentType }),
        ...(experience && { experience }),
        ...(city && { city: { contains: city, mode: "insensitive" as const } }),
        ...(country && { country }),
        ...(salaryMin && { salaryMin: { gte: salaryMin } }),
        ...(organizationSlug && {
          organization: {
            slug: organizationSlug,
          },
        }),
        ...(search && {
          OR: [
            { title: { contains: search, mode: "insensitive" as const } },
            { description: { contains: search, mode: "insensitive" as const } },
            { organization: { name: { contains: search, mode: "insensitive" as const } } },
          ],
        }),
      };

      const [jobs, total] = await Promise.all([
        ctx.db.job.findMany({
          where,
          skip,
          take: limit,
          select: {
            id: true,
            title: true,
            description: true,
            requirements: true,
            benefits: true,
            locationType: true,
            city: true,
            country: true,
            employmentType: true,
            experience: true,
            salaryMin: true,
            salaryMax: true,
            salaryCurrency: true,
            salaryPeriod: true,
            publishedAt: true,
            expiresAt: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            _count: {
              select: {
                applications: true,
              },
            },
          },
          orderBy: { publishedAt: "desc" },
        }),
        ctx.db.job.count({ where }),
      ]);

      return {
        jobs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  getPublicById: publicProcedure
    .input(getPublicJobSchema)
    .query(async ({ ctx, input }) => {
      const { jobId } = input;

      const job = await ctx.db.job.findFirst({
        where: {
          id: jobId,
          status: "PUBLISHED",
          publishedAt: { lte: new Date() },
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        select: {
          id: true,
          title: true,
          description: true,
          requirements: true,
          benefits: true,
          locationType: true,
          city: true,
          country: true,
          employmentType: true,
          experience: true,
          salaryMin: true,
          salaryMax: true,
          salaryCurrency: true,
          salaryPeriod: true,
          publishedAt: true,
          expiresAt: true,
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      if (!job) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found or no longer available",
        });
      }

      return job;
    }),

  // Job statistics
  getStats: orgProcedure
    .input(getJobStatsSchema)
    .query(async ({ ctx, input }) => {
      const { jobId, organizationId, startDate, endDate } = input;

      // Determine which organization to get stats for
      let targetOrgId = ctx.session.user.organizationId;
      
      if (ctx.session.user.role === "SUPER_ADMIN" && organizationId) {
        targetOrgId = organizationId;
      }

      if (!targetOrgId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      const dateFilter = startDate && endDate ? {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      } : {};

      const baseWhere = {
        organizationId: targetOrgId,
        ...dateFilter,
      };

      if (jobId) {
        // Stats for specific job
        const job = await ctx.db.job.findFirst({
          where: {
            id: jobId,
            organizationId: targetOrgId,
          },
          include: {
            _count: {
              select: {
                applications: true,
              },
            },
          },
        });

        if (!job) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Job not found",
          });
        }

        const applicationStats = await ctx.db.application.groupBy({
          by: ['status'],
          where: {
            jobId,
            ...dateFilter,
          },
          _count: true,
        });

        return {
          job,
          totalApplications: job._count.applications,
          applicationsByStatus: applicationStats.reduce((acc, stat) => {
            acc[stat.status] = stat._count;
            return acc;
          }, {} as Record<string, number>),
        };
      } else {
        // Organization-wide stats
        const [
          totalJobs,
          publishedJobs,
          draftJobs,
          closedJobs,
          totalApplications,
          recentJobs,
        ] = await Promise.all([
          ctx.db.job.count({ where: baseWhere }),
          ctx.db.job.count({ where: { ...baseWhere, status: "PUBLISHED" } }),
          ctx.db.job.count({ where: { ...baseWhere, status: "DRAFT" } }),
          ctx.db.job.count({ where: { ...baseWhere, status: "CLOSED" } }),
          ctx.db.application.count({
            where: {
              job: { organizationId: targetOrgId },
              ...dateFilter,
            },
          }),
          ctx.db.job.count({
            where: {
              ...baseWhere,
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
              },
            },
          }),
        ]);

        return {
          totalJobs,
          publishedJobs,
          draftJobs,
          closedJobs,
          totalApplications,
          recentJobs,
        };
      }
    }),
});