import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { UserRole } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
  adminProcedure,
  orgProcedure,
} from "~/server/api/trpc";

import {
  getUsersSchema,
  getUserByIdSchema,
  updateUserSchema,
  deleteUserSchema,
  inviteUserSchema,
} from "~/lib/validations/users";

export const usersRouter = createTRPCRouter({
  // Get all users (admin only)
  getAll: adminProcedure
    .input(getUsersSchema)
    .query(async ({ ctx, input }) => {
      const { page, limit, role, search } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(role && { role }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { email: { contains: search, mode: "insensitive" as const } },
          ],
        }),
      };

      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          skip,
          take: limit,
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            organizationId: true,
            emailVerified: true,
            twoFactorEnabled: true,
            createdAt: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        ctx.db.user.count({ where }),
      ]);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // Get organization users (org admin and above)
  getOrgUsers: orgProcedure
    .input(getUsersSchema)
    .query(async ({ ctx, input }) => {
      const { page, limit, role, search } = input;
      const skip = (page - 1) * limit;

      // Super admin can see all, others only their organization
      const organizationFilter = ctx.session.user.role === "SUPER_ADMIN" 
        ? {} 
        : { organizationId: ctx.session.user.organizationId };

      const where = {
        ...organizationFilter,
        ...(role && { role }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { email: { contains: search, mode: "insensitive" as const } },
          ],
        }),
      };

      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          skip,
          take: limit,
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            organizationId: true,
            emailVerified: true,
            createdAt: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        ctx.db.user.count({ where }),
      ]);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // Get user by ID
  getById: protectedProcedure
    .input(getUserByIdSchema)
    .query(async ({ ctx, input }) => {
      const { userId } = input;

      // Users can only view their own profile unless they're admin
      if (ctx.session.user.role !== "SUPER_ADMIN" && ctx.session.user.id !== userId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You can only view your own profile",
        });
      }

      const user = await ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          organizationId: true,
          emailVerified: true,
          twoFactorEnabled: true,
          createdAt: true,
          updatedAt: true,
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return user;
    }),

  // Update user (admin or self)
  update: protectedProcedure
    .input(updateUserSchema)
    .mutation(async ({ ctx, input }) => {
      const { userId, name, role } = input;

      // Check permissions
      const canUpdate = 
        ctx.session.user.role === "SUPER_ADMIN" ||
        (ctx.session.user.id === userId && !role); // Users can update their own profile but not role

      if (!canUpdate) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to update this user",
        });
      }

      // Super admin can update anything, others can only update name
      const updateData: any = {};
      if (name !== undefined) updateData.name = name;
      if (role !== undefined && ctx.session.user.role === "SUPER_ADMIN") {
        updateData.role = role;
      }

      const updatedUser = await ctx.db.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          organizationId: true,
          updatedAt: true,
        },
      });

      return updatedUser;
    }),

  // Delete user (admin only)
  delete: adminProcedure
    .input(deleteUserSchema)
    .mutation(async ({ ctx, input }) => {
      const { userId } = input;

      // Prevent admin from deleting themselves
      if (ctx.session.user.id === userId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You cannot delete your own account",
        });
      }

      await ctx.db.user.delete({
        where: { id: userId },
      });

      return { success: true };
    }),

  // Invite user to organization (org admin and above)
  inviteUser: orgProcedure
    .input(inviteUserSchema)
    .mutation(async ({ ctx, input }) => {
      const { email, role, name } = input;

      // Check if user already exists
      const existingUser = await ctx.db.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User with this email already exists",
        });
      }

      // Only super admin can create super admin users
      if (role === "SUPER_ADMIN" && ctx.session.user.role !== "SUPER_ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Only super admins can create super admin users",
        });
      }

      // Generate invitation token
      const invitationToken = crypto.randomUUID();
      const invitationExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      // Create user with invitation token
      const user = await ctx.db.user.create({
        data: {
          email,
          name,
          role,
          organizationId: ctx.session.user.organizationId,
          invitationToken,
          invitationExpiry,
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          organizationId: true,
        },
      });

      // Get organization and inviter details for email
      const organization = await ctx.db.organization.findUnique({
        where: { id: ctx.session.user.organizationId! },
        select: { name: true },
      });

      // Send invitation email
      const { sendUserInvitationEmail } = await import("~/lib/email");
      const emailSent = await sendUserInvitationEmail(
        email,
        invitationToken,
        organization?.name || "Unknown Organization",
        ctx.session.user.name || "Team Admin",
        role
      );

      if (!emailSent) {
        console.error(`Failed to send invitation email to ${email}`);
      }

      return {
        success: true,
        user,
        invitationToken: process.env.NODE_ENV === "development" ? invitationToken : undefined,
      };
    }),

  // Get user statistics (admin only)
  getStats: adminProcedure.query(async ({ ctx }) => {
    const [
      totalUsers,
      candidateCount,
      businessUserCount,
      adminCount,
      recentUsers,
      activeOrganizations,
    ] = await Promise.all([
      ctx.db.user.count(),
      ctx.db.user.count({ where: { role: "CANDIDATE" } }),
      ctx.db.user.count({ 
        where: { 
          role: { 
            in: ["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP"] 
          } 
        } 
      }),
      ctx.db.user.count({ where: { role: "SUPER_ADMIN" } }),
      ctx.db.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
      ctx.db.organization.count(),
    ]);

    return {
      totalUsers,
      candidateCount,
      businessUserCount,
      adminCount,
      recentUsers,
      activeOrganizations,
    };
  }),
});