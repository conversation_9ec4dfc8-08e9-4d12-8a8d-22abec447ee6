import { createTRPCRouter } from "~/server/api/trpc";
import { authRouter } from "~/server/api/routers/auth";
import { usersRouter } from "~/server/api/routers/users";
import { organizationsRouter } from "~/server/api/routers/organizations";
import { jobsRouter } from "~/server/api/routers/jobs";
import { applicationsRouter } from "~/server/api/routers/applications";
import { auditRouter } from "~/server/api/routers/audit";
import { securityRouter } from "~/server/api/routers/security";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  auth: authRouter,
  users: usersRouter,
  organizations: organizationsRouter,
  jobs: jobsRouter,
  applications: applicationsRouter,
  audit: auditRouter,
  security: securityRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;