"use client"

import { useState } from "react"
import Link from "next/link"
import { z } from "zod"

import { AuthLayout } from "@/components/auth/auth-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { forgotPasswordSchema } from "@/lib/auth-utils"
import { api } from "@/lib/api-client"

export default function ForgotPassword() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // Validate email
      const validatedData = forgotPasswordSchema.parse({ email })

      // Call forgot password API with CSRF protection
      const response = await api.post("/api/auth/forgot-password", validatedData)

      const result = await response.json()

      if (response.ok) {
        setIsSubmitted(true)
      } else {
        if (result.errors) {
          setError(result.errors.email || result.message || "An error occurred")
        } else {
          setError(result.message || "An error occurred")
        }
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const emailError = error.errors.find(err => err.path[0] === 'email')
        setError(emailError?.message || "Please enter a valid email address")
      } else {
        setError("An error occurred. Please try again.")
      }
    } finally {
      setIsLoading(false)
    }
  }

  if (isSubmitted) {
    return (
      <AuthLayout
        title="Check Your Email"
        subtitle="We've sent password reset instructions"
        type="candidate"
        backHref="/auth/candidate/signin"
      >
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">Instructions Sent!</h3>
            <p className="text-gray-600">
              If an account with <strong>{email}</strong> exists, we've sent password reset instructions to your email.
            </p>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 text-sm text-blue-800">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p>
                  <strong>What's next?</strong><br />
                  Check your email inbox and spam folder. Click the reset link within 1 hour to change your password.
                </p>
              </div>
            </div>
          </div>

          <div className="pt-4 space-y-3">
            <Button asChild className="w-full">
              <Link href="/auth/candidate/signin">
                Back to Sign In
              </Link>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => {
                setIsSubmitted(false)
                setEmail("")
              }}
              className="w-full"
            >
              Send to Different Email
            </Button>
          </div>
        </div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout
      title="Forgot Your Password?"
      subtitle="Enter your email address and we'll send you a reset link"
      type="candidate"
      backHref="/auth/candidate/signin"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium text-gray-700">
            Email Address
          </label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500">
            We'll send password reset instructions to this email if an account exists.
          </p>
        </div>

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "Sending Instructions..." : "Send Reset Instructions"}
        </Button>

        <div className="text-center text-sm">
          <span className="text-gray-600">Remember your password? </span>
          <Link
            href="/auth/candidate/signin"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            Back to Sign In
          </Link>
        </div>

        <div className="text-center">
          <Link
            href="/auth/business/signin"
            className="text-sm text-gray-600 hover:text-gray-500"
          >
            Business User? Reset Business Password →
          </Link>
        </div>
      </form>
    </AuthLayout>
  )
}