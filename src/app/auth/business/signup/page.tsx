"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { z } from "zod"

import { AuthLayout } from "@/components/auth/auth-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { PasswordInput } from "@/components/ui/password-input"
import { businessSignupSchema } from "@/lib/auth-utils"

export default function BusinessSignUp() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    organizationName: "",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const router = useRouter()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear specific field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrors({})

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setErrors({ confirmPassword: "Passwords do not match" })
      setIsLoading(false)
      return
    }

    try {
      // Validate form data
      const validatedData = businessSignupSchema.parse({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        organizationName: formData.organizationName,
      })

      // Call signup API
      const response = await fetch("/api/auth/signup/business", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(validatedData),
      })

      const result = await response.json()

      if (!response.ok) {
        if (result.errors) {
          setErrors(result.errors)
        } else if (result.accountExists) {
          if (result.needsVerification) {
            // Redirect to verification page
            router.push(`/auth/verify-email?email=${encodeURIComponent(validatedData.email)}`)
            return
          } else if (result.signInUrl) {
            // Show error with sign-in link
            setErrors({ 
              general: result.message,
              signInUrl: result.signInUrl
            })
          } else {
            setErrors({ general: result.message || "An error occurred" })
          }
        } else {
          setErrors({ general: result.message || "An error occurred" })
        }
        return
      }

      // Check if verification is required
      if (result.requiresVerification) {
        // Redirect to email verification with email pre-filled
        router.push(`/auth/verify-email?email=${encodeURIComponent(validatedData.email)}`)
      } else {
        // Redirect to sign-in with success message
        router.push("/auth/business/signin?message=Account created successfully")
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {}
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0] as string] = err.message
          }
        })
        setErrors(fieldErrors)
      } else {
        setErrors({ general: "An error occurred. Please try again." })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout
      title="Create Business Account"
      subtitle="Start hiring top talent for your organization"
      type="business"
      backHref="/business"
    >
      <div className="space-y-6">
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-amber-800">
                Business Email Required
              </h3>
              <div className="mt-2 text-sm text-amber-700">
                <p>
                  You must use a business email address. Personal email addresses 
                  (Gmail, Yahoo, etc.) are not accepted.
                </p>
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
              <p>{errors.general}</p>
              {errors.signInUrl && (
                <p className="mt-2">
                  <Link 
                    href={errors.signInUrl} 
                    className="text-blue-600 hover:text-blue-500 font-medium underline"
                  >
                    Sign in instead →
                  </Link>
                </p>
              )}
            </div>
          )}

          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium text-gray-700">
              Full Name
            </label>
            <Input
              id="name"
              name="name"
              type="text"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={handleChange}
              required
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium text-gray-700">
              Business Email
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleChange}
              required
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email}</p>
            )}
            <p className="text-xs text-gray-500">
              Use your company email address for verification
            </p>
          </div>

          <div className="space-y-2">
            <label htmlFor="organizationName" className="text-sm font-medium text-gray-700">
              Organization Name
            </label>
            <Input
              id="organizationName"
              name="organizationName"
              type="text"
              placeholder="Your Company Name"
              value={formData.organizationName}
              onChange={handleChange}
              required
            />
            {errors.organizationName && (
              <p className="text-sm text-red-600">{errors.organizationName}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium text-gray-700">
              Password
            </label>
            <PasswordInput
              id="password"
              name="password"
              placeholder="Create a password (10+ characters)"
              value={formData.password}
              onChange={handleChange}
              required
            />
            {errors.password && (
              <p className="text-sm text-red-600">{errors.password}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
              Confirm Password
            </label>
            <PasswordInput
              id="confirmPassword"
              name="confirmPassword"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
            />
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword}</p>
            )}
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Creating account..." : "Create Business Account"}
          </Button>
        </form>

        <div className="text-center text-sm">
          <span className="text-gray-600">Already have an account? </span>
          <Link
            href="/auth/business/signin"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            Sign in
          </Link>
        </div>

        <div className="border-t pt-4">
          <div className="text-center text-sm text-gray-600">
            <p className="mb-2">Looking for a job?</p>
            <Link
              href="/auth/candidate/signin"
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Candidate Sign In →
            </Link>
          </div>
        </div>
      </div>
    </AuthLayout>
  )
}