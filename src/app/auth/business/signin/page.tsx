"use client"

import { useState, Suspense } from "react"
import { signIn } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"

import { AuthLayout } from "@/components/auth/auth-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { PasswordInput } from "@/components/ui/password-input"
import { useCSRFToken } from "@/hooks/use-csrf-token"

function BusinessSignInForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams.get("callbackUrl") || "/business/dashboard"
  const { csrfToken } = useCSRFToken()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const result = await signIn("credentials", {
        email,
        password,
        userType: "business",
        redirect: false,
        csrfToken,
      })

      if (result?.error) {
        if (result.error === "EMAIL_NOT_VERIFIED") {
          // Redirect to email verification page
          router.push(`/auth/verify-email?email=${encodeURIComponent(email)}`)
          return
        }
        setError("Invalid email or password")
      } else {
        router.push(callbackUrl)
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout
      title="Business Sign In"
      subtitle="Access your hiring dashboard and manage your team"
      type="business"
      backHref="/business"
    >
      <div className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Business Account Required
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Please use your business email address. Personal email addresses 
                  (Gmail, Yahoo, etc.) are not accepted for business accounts.
                </p>
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium text-gray-700">
              Business Email
            </label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <p className="text-xs text-gray-500">
              Use your company email address for secure access
            </p>
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium text-gray-700">
              Password
            </label>
            <PasswordInput
              id="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Signing in..." : "Sign in to Dashboard"}
          </Button>
        </form>

        <div className="text-center text-sm">
          <span className="text-gray-600">Don't have a business account? </span>
          <Link
            href="/auth/business/signup"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            Get started
          </Link>
        </div>

        <div className="text-center">
          <Link
            href="/auth/forgot-password"
            className="text-sm text-gray-600 hover:text-gray-500"
          >
            Forgot your password?
          </Link>
        </div>

        <div className="border-t pt-4">
          <div className="text-center text-sm text-gray-600">
            <p className="mb-2">Looking to apply for jobs?</p>
            <Link
              href="/auth/candidate/signin"
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Candidate Sign In →
            </Link>
          </div>
        </div>
      </div>
    </AuthLayout>
  )
}

export default function BusinessSignIn() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <BusinessSignInForm />
    </Suspense>
  )
}