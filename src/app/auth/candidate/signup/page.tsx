"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { z } from "zod"

import { AuthLayout } from "@/components/auth/auth-layout"
import { OAuthButton } from "@/components/auth/oauth-button"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { PasswordInput } from "@/components/ui/password-input"
import { PasswordStrengthMeter } from "@/components/auth/password-strength-meter"
import { candidateSignupSchema } from "@/lib/auth-utils"
import { api } from "@/lib/api-client"
import type { EnhancedPasswordValidationResult } from "@/lib/enhanced-password-validator"

export default function CandidateSignUp() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [passwordValidation, setPasswordValidation] = useState<EnhancedPasswordValidationResult | null>(null)

  const router = useRouter()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear specific field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrors({})

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setErrors({ confirmPassword: "Passwords do not match" })
      setIsLoading(false)
      return
    }

    // Check password validation result
    if (passwordValidation && !passwordValidation.isValid) {
      setErrors({ 
        password: "Password does not meet security requirements. Please check the requirements above."
      })
      setIsLoading(false)
      return
    }

    try {
      // Validate form data
      const validatedData = candidateSignupSchema.parse({
        name: formData.name,
        email: formData.email,
        password: formData.password,
      })

      // Call signup API with CSRF protection
      const response = await api.post("/api/auth/signup/candidate", validatedData)

      const result = await response.json()

      if (!response.ok) {
        if (result.errors) {
          setErrors(result.errors)
        } else if (result.accountExists) {
          if (result.needsVerification) {
            // Redirect to verification page
            router.push(`/auth/verify-email?email=${encodeURIComponent(validatedData.email)}`)
            return
          } else if (result.signInUrl) {
            // Show error with sign-in link
            setErrors({ 
              general: result.message,
              signInUrl: result.signInUrl
            })
          } else {
            setErrors({ general: result.message || "An error occurred" })
          }
        } else {
          setErrors({ general: result.message || "An error occurred" })
        }
        return
      }

      // Check if verification is required
      if (result.requiresVerification) {
        // Redirect to email verification with email pre-filled
        router.push(`/auth/verify-email?email=${encodeURIComponent(validatedData.email)}`)
      } else {
        // Redirect to sign-in with success message
        router.push("/auth/candidate/signin?message=Account created successfully")
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: Record<string, string> = {}
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0] as string] = err.message
          }
        })
        setErrors(fieldErrors)
      } else {
        setErrors({ general: "An error occurred. Please try again." })
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout
      title="Create your candidate account"
      subtitle="Join thousands of professionals finding their next opportunity"
      type="candidate"
      backHref="/"
    >
      <div className="space-y-4">
        {/* OAuth Buttons */}
        <div className="space-y-3">
          <OAuthButton provider="google" callbackUrl="/candidate/dashboard">
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              <span>Sign up with Google</span>
            </div>
          </OAuthButton>

          <OAuthButton provider="linkedin" callbackUrl="/candidate/dashboard">
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
              <span>Sign up with LinkedIn</span>
            </div>
          </OAuthButton>
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-gray-500">Or sign up with email</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
              <p>{errors.general}</p>
              {errors.signInUrl && (
                <p className="mt-2">
                  <Link 
                    href={errors.signInUrl} 
                    className="text-blue-600 hover:text-blue-500 font-medium underline"
                  >
                    Sign in instead →
                  </Link>
                </p>
              )}
            </div>
          )}

          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium text-gray-700">
              Full Name
            </label>
            <Input
              id="name"
              name="name"
              type="text"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={handleChange}
              required
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium text-gray-700">
              Email
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleChange}
              required
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium text-gray-700">
              Password
            </label>
            <PasswordInput
              id="password"
              name="password"
              placeholder="Create a password (10+ characters)"
              value={formData.password}
              onChange={handleChange}
              required
            />
            {errors.password && (
              <p className="text-sm text-red-600">{errors.password}</p>
            )}
            
            {/* Password Strength Meter */}
            {formData.password && (
              <PasswordStrengthMeter
                password={formData.password}
                context={{
                  email: formData.email,
                  firstName: formData.name.split(' ')[0],
                  lastName: formData.name.split(' ').slice(1).join(' ')
                }}
                onValidationChange={setPasswordValidation}
                className="mt-2"
              />
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
              Confirm Password
            </label>
            <PasswordInput
              id="confirmPassword"
              name="confirmPassword"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
            />
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword}</p>
            )}
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Creating account..." : "Create account"}
          </Button>
        </form>

        <div className="text-center text-sm">
          <span className="text-gray-600">Already have an account? </span>
          <Link
            href="/auth/candidate/signin"
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            Sign in
          </Link>
        </div>
      </div>
    </AuthLayout>
  )
}