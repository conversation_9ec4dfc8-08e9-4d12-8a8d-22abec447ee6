"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, AlertCircle, CheckCircle, ArrowRight } from "lucide-react"
import { AuthLayout } from "@/components/auth/auth-layout"

export default function OAuthVerificationPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  useEffect(() => {
    if (status === "loading") return

    // If no verification needed, redirect to dashboard
    if (!session?.needsVerification) {
      router.push("/dashboard")
      return
    }
  }, [session, status, router])

  const handleCreatePendingLink = async () => {
    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/auth/oauth/create-pending-link", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          provider: session?.oauthProvider,
          email: session?.oauthEmail,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
      } else {
        setError(data.message || "Failed to send verification email")
      }
    } catch (err) {
      setError("Network error. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  if (status === "loading") {
    return (
      <AuthLayout title="Loading..." type="candidate">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading...</p>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    )
  }

  if (!session?.needsVerification) {
    return (
      <AuthLayout title="Redirecting..." type="candidate">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Redirecting to dashboard...</p>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    )
  }

  if (success) {
    return (
      <AuthLayout title="Verification Email Sent" type="candidate">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Verification Email Sent!
              </h3>
              <p className="text-gray-600 mb-4">
                We've sent a verification email to your account. Please check your email 
                and click the verification link to complete the account linking process.
              </p>
              
              <Alert className="mb-6 text-left">
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  <strong>Security Note:</strong> This verification step ensures that only you 
                  can link OAuth accounts to your Sourceflex account.
                </AlertDescription>
              </Alert>
              
              <Button onClick={() => router.push("/auth/signin")} variant="outline">
                Return to Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout title="Account Verification Required" type="candidate">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
            <Shield className="h-6 w-6 text-amber-600" />
          </div>
          <CardTitle className="text-xl">Account Verification Required</CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Security Check:</strong> We detected that you're trying to sign in with 
                {" "}{session.oauthProvider} using an email that's already associated with a Sourceflex account.
              </AlertDescription>
            </Alert>
            
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <h4 className="font-medium text-gray-900">What's happening?</h4>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li>You have an existing Sourceflex account with email: {session.oauthEmail}</li>
                <li>You're trying to sign in with {session.oauthProvider}</li>
                <li>For security, we need to verify that you own both accounts</li>
              </ul>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg space-y-2">
              <h4 className="font-medium text-gray-900">Next Steps:</h4>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Click "Send Verification Email" below</li>
                <li>Check your email for a verification link</li>
                <li>Click the link to confirm account linking</li>
                <li>Sign in with either method going forward</li>
              </ol>
            </div>
            
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-3">
              <Button 
                onClick={handleCreatePendingLink} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Sending Verification Email...
                  </>
                ) : (
                  <>
                    <Shield className="mr-2 h-4 w-4" />
                    Send Verification Email
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => router.push("/auth/signin")}
                className="w-full"
              >
                Cancel & Return to Sign In
              </Button>
            </div>
            
            <div className="mt-6 pt-4 border-t text-sm text-gray-500">
              <p><strong>Why is this necessary?</strong></p>
              <p className="mt-1">
                This security step prevents unauthorized access to your account through 
                OAuth providers. It ensures that only you can link external accounts 
                to your Sourceflex profile.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </AuthLayout>
  )
}