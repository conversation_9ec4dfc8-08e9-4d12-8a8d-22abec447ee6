"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, CheckCircle, XCircle, Clock, ArrowRight } from "lucide-react"
import { AuthLayout } from "@/components/auth/auth-layout"

export default function VerifyAccountLinkPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get("token")
  
  const [isLoading, setIsLoading] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [linkInfo, setLinkInfo] = useState<any>(null)
  const [verificationResult, setVerificationResult] = useState<any>(null)
  const [error, setError] = useState("")

  useEffect(() => {
    if (token) {
      fetchLinkInfo()
    }
  }, [token])

  const fetchLinkInfo = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/auth/oauth/link-info?token=${token}`)
      const data = await response.json()
      
      if (response.ok) {
        setLinkInfo(data)
      } else {
        setError(data.message || "Invalid verification link")
      }
    } catch (err) {
      setError("Failed to load verification information")
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyLink = async () => {
    setIsVerifying(true)
    setError("")
    
    try {
      const response = await fetch("/api/auth/oauth/verify-link", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ token }),
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setVerificationResult(data)
      } else {
        setError(data.message || "Verification failed")
      }
    } catch (err) {
      setError("Failed to verify account link")
    } finally {
      setIsVerifying(false)
    }
  }

  const handleContinueToSignIn = () => {
    router.push("/auth/signin")
  }

  if (!token) {
    return (
      <AuthLayout title="Invalid Link" type="candidate">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Invalid Verification Link
              </h3>
              <p className="text-gray-600 mb-4">
                The verification link is missing or malformed.
              </p>
              <Button onClick={() => router.push("/auth/signin")} variant="outline">
                Go to Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    )
  }

  if (isLoading) {
    return (
      <AuthLayout title="Loading..." type="candidate">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading verification details...</p>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    )
  }

  if (error && !linkInfo) {
    return (
      <AuthLayout title="Verification Error" type="candidate">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Verification Failed
              </h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => router.push("/auth/signin")} variant="outline">
                Go to Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    )
  }

  if (verificationResult?.success) {
    return (
      <AuthLayout title="Account Linked Successfully" type="candidate">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Account Successfully Linked!
              </h3>
              <p className="text-gray-600 mb-4">
                Your {linkInfo?.provider} account has been securely linked to your Sourceflex account.
              </p>
              
              <Alert className="mb-6 text-left">
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  You can now sign in using either your email/password or your {linkInfo?.provider} account.
                </AlertDescription>
              </Alert>
              
              <Button onClick={handleContinueToSignIn} className="w-full">
                Continue to Sign In
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout title="Verify Account Linking" type="candidate">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
            <Shield className="h-6 w-6 text-blue-600" />
          </div>
          <CardTitle className="text-xl">Account Linking Verification</CardTitle>
        </CardHeader>
        
        <CardContent>
          {linkInfo && (
            <div className="space-y-4">
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  <strong>Security Verification Required:</strong> Someone attempted to link 
                  a {linkInfo.provider} account to your Sourceflex account.
                </AlertDescription>
              </Alert>
              
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <h4 className="font-medium text-gray-900">Account to be linked:</h4>
                <div className="text-sm text-gray-600">
                  <p><strong>Provider:</strong> {linkInfo.provider}</p>
                  <p><strong>Email:</strong> {linkInfo.oauthEmail}</p>
                  {linkInfo.oauthName && (
                    <p><strong>Name:</strong> {linkInfo.oauthName}</p>
                  )}
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <h4 className="font-medium text-gray-900">Your existing account:</h4>
                <div className="text-sm text-gray-600">
                  <p><strong>Email:</strong> {linkInfo.existingUserEmail}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-3 rounded">
                <Clock className="h-4 w-4" />
                <span>This verification expires at {new Date(linkInfo.expiresAt).toLocaleString()}</span>
              </div>
              
              {error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <div className="space-y-3">
                <Button 
                  onClick={handleVerifyLink} 
                  disabled={isVerifying}
                  className="w-full"
                >
                  {isVerifying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Verifying...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Yes, Link This Account
                    </>
                  )}
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={() => router.push("/auth/signin")}
                  className="w-full"
                >
                  Cancel & Go to Sign In
                </Button>
              </div>
              
              <div className="mt-6 pt-4 border-t text-sm text-gray-500">
                <p><strong>Security Note:</strong></p>
                <ul className="list-disc list-inside space-y-1 mt-2">
                  <li>Only click "Link Account" if you initiated this request</li>
                  <li>If you didn't request this linking, you can safely ignore it</li>
                  <li>Your account remains secure until you verify</li>
                </ul>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </AuthLayout>
  )
}