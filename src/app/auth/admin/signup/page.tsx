"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AuthLayout } from "@/components/auth/auth-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Shield, AlertCircle, CheckCircle, Lock } from "lucide-react";
import { PasswordInput } from "@/components/ui/password-input";

export default function SecureAdminSignUp() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const invitationToken = searchParams.get("invitation");
  
  const [invitationData, setInvitationData] = useState<any>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  });

  useEffect(() => {
    if (invitationToken) {
      verifyInvitation();
    }
  }, [invitationToken]);

  const verifyInvitation = async () => {
    if (!invitationToken) {
      setError("No invitation token provided");
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch("/api/admin/invitations/verify", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ invitationToken }),
      });

      const result = await response.json();

      if (response.ok) {
        setInvitationData(result.invitation);
      } else {
        setError(result.message || "Invalid invitation");
      }
    } catch (err) {
      setError("Failed to verify invitation");
    } finally {
      setIsVerifying(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Basic validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 12) {
      setError("Admin password must be at least 12 characters");
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/signup/admin", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          invitationToken,
          password: formData.password,
          confirmPassword: formData.confirmPassword,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSuccess(true);
        setTimeout(() => {
          router.push("/auth/admin/signin");
        }, 3000);
      } else {
        setError(result.message || "Failed to create admin account");
      }
    } catch (err) {
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // No invitation token provided
  if (!invitationToken) {
    return (
      <AuthLayout title="Invalid Access" type="admin">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Admin Invitation Required
              </h3>
              <p className="text-gray-600 mb-4">
                Super admin accounts can only be created through secure invitations.
              </p>
              <Button onClick={() => router.push("/auth/admin/signin")} variant="outline">
                Go to Admin Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    );
  }

  // Loading invitation verification
  if (isVerifying) {
    return (
      <AuthLayout title="Verifying Invitation..." type="admin">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Verifying your admin invitation...</p>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    );
  }

  // Invalid invitation
  if (error && !invitationData) {
    return (
      <AuthLayout title="Invalid Invitation" type="admin">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Invalid Invitation
              </h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => router.push("/auth/admin/signin")} variant="outline">
                Go to Admin Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    );
  }

  // Success state
  if (success) {
    return (
      <AuthLayout title="Admin Account Created" type="admin">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Admin Account Created Successfully!
              </h3>
              <p className="text-gray-600 mb-4">
                Your SUPER_ADMIN account has been created and secured.
              </p>
              
              <Alert className="mb-6 text-left">
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  Your account has been logged and you now have full administrative access.
                </AlertDescription>
              </Alert>
              
              <p className="text-sm text-gray-500 mb-4">
                Redirecting to admin sign in...
              </p>
            </div>
          </CardContent>
        </Card>
      </AuthLayout>
    );
  }

  // Main signup form (with valid invitation)
  return (
    <AuthLayout title="Complete Admin Account Setup" type="admin">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <Shield className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Complete Admin Setup</CardTitle>
          <p className="text-sm text-gray-600 mt-2">
            Create your secure administrator password
          </p>
        </CardHeader>
        
        <CardContent>
          {invitationData && (
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <h4 className="font-medium text-gray-900 mb-2">Invitation Details:</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>Name:</strong> {invitationData.name}</p>
                <p><strong>Email:</strong> {invitationData.email}</p>
                <p><strong>Invited by:</strong> {invitationData.invitedBy}</p>
              </div>
            </div>
          )}
          
          <Alert className="mb-6">
            <Lock className="h-4 w-4" />
            <AlertDescription>
              <strong>Security Notice:</strong> You are creating a SUPER_ADMIN account with full system access.
            </AlertDescription>
          </Alert>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Admin Password
              </label>
              <PasswordInput
                id="password"
                name="password"
                required
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Create a strong admin password"
              />
              <p className="text-xs text-gray-500 mt-1">
                Must be at least 12 characters with mixed case, numbers, and symbols
              </p>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                Confirm Password
              </label>
              <PasswordInput
                id="confirmPassword"
                name="confirmPassword"
                required
                value={formData.confirmPassword}
                onChange={handleInputChange}
                placeholder="Confirm your admin password"
              />
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Creating Admin Account...
                </>
              ) : (
                <>
                  <Shield className="mr-2 h-4 w-4" />
                  Create Admin Account
                </>
              )}
            </Button>
          </form>

          <div className="mt-6 pt-4 border-t text-sm text-gray-500">
            <p><strong>Security Information:</strong></p>
            <ul className="list-disc list-inside space-y-1 mt-2">
              <li>Your admin account will be logged and monitored</li>
              <li>Enable 2FA immediately after account creation</li>
              <li>Use a unique, strong password</li>
              <li>This invitation will be marked as used</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </AuthLayout>
  );
}