"use client"

import { useState, useEffect, Suspense } from "react"
import { signIn } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"

import { AuthLayout } from "@/components/auth/auth-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { PasswordInput } from "@/components/ui/password-input"
import { api } from "@/lib/api-client"

function AdminSignInForm() {
  const [step, setStep] = useState<"credentials" | "2fa" | "email2fa">("credentials")
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  })
  const [twoFactorCode, setTwoFactorCode] = useState("")
  const [email2FACode, setEmail2FACode] = useState("")
  const [twoFactorMethod, setTwoFactorMethod] = useState<"totp" | "email">("totp")
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [error, setError] = useState("")
  const [resendCooldown, setResendCooldown] = useState(0)
  const [userId, setUserId] = useState<string | null>(null)
  const [email2FASession, setEmail2FASession] = useState<{ sessionId: string; sessionToken: string } | null>(null)

  const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams.get("callbackUrl") || "/admin/dashboard"

  // Cooldown timer for resend button
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [resendCooldown])

  const handleCredentialsSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // First, verify credentials
      const response = await fetch("/api/auth/admin/verify-credentials", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(credentials),
      })

      const result = await response.json()

      if (response.ok) {
        if (result.requires2FA) {
          setUserId(result.userId || null)
          setStep("2fa")
        } else {
          // No 2FA required, proceed with sign in
          const signInResult = await signIn("credentials", {
            email: credentials.email,
            password: credentials.password,
            userType: "admin",
            redirect: false,
          })

          if (signInResult?.error) {
            setError("Invalid credentials")
          } else {
            router.push(callbackUrl)
          }
        }
      } else {
        setError(result.message || "Invalid credentials")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleTwoFactorSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/auth/admin/verify-2fa", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          email: credentials.email,
          password: credentials.password,
          twoFactorCode,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        // Sign in with NextAuth
        const signInResult = await signIn("credentials", {
          email: credentials.email,
          password: credentials.password,
          userType: "admin",
          twoFactorCode,
          redirect: false,
        })

        if (signInResult?.error) {
          setError("Authentication failed")
        } else {
          router.push(callbackUrl)
        }
      } else {
        setError(result.message || "Invalid 2FA code")
        setTwoFactorCode("")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendEmail2FA = async () => {
    if (!userId) return
    
    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/auth/2fa/email/send", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId,
          deviceInfo: `Admin sign-in from ${navigator.userAgent.split(' ').slice(-2).join(' ')}`
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        setError(result.message || "Failed to send security code")
        if (result.waitMinutes) {
          setResendCooldown(result.waitMinutes * 60)
        }
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendEmail2FA = async () => {
    if (!userId) return
    
    setIsResending(true)
    setError("")

    try {
      const response = await fetch("/api/auth/2fa/email/send", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId,
          deviceInfo: `Admin sign-in from ${navigator.userAgent.split(' ').slice(-2).join(' ')}`
        }),
      })

      const result = await response.json()

      if (response.ok) {
        setResendCooldown(120) // 2 minute cooldown
      } else {
        setError(result.message || "Failed to resend code")
        if (result.waitMinutes) {
          setResendCooldown(result.waitMinutes * 60)
        }
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsResending(false)
    }
  }

  const handleEmail2FASubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!userId) return
    
    setIsLoading(true)
    setError("")

    try {
      // First verify the email 2FA code with CSRF protection
      const verifyResponse = await api.post("/api/auth/2fa/email/verify", {
        userId,
        code: email2FACode,
      })

      const verifyResult = await verifyResponse.json()

      if (!verifyResponse.ok) {
        setError(verifyResult.message || "Invalid security code")
        setEmail2FACode("")
        return
      }

      // Store the session credentials
      setEmail2FASession({
        sessionId: verifyResult.sessionId,
        sessionToken: verifyResult.sessionToken
      })

      // If verification successful, sign in with NextAuth using the secure session
      const signInResult = await signIn("credentials", {
        email: credentials.email,
        password: credentials.password,
        userType: "admin",
        email2FASessionId: verifyResult.sessionId,
        email2FASessionToken: verifyResult.sessionToken,
        redirect: false,
      })

      if (signInResult?.error) {
        setError("Authentication failed")
      } else {
        router.push(callbackUrl)
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout
      title={step === "credentials" ? "Admin Sign In" : "Enter 2FA Code"}
      subtitle={
        step === "credentials" 
          ? "Secure access to platform administration"
          : "Enter the 6-digit code from your authenticator app"
      }
      type="admin"
      backHref="/"
    >
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Administrator Access
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  This is a secure area for platform administrators only. 
                  Unauthorized access attempts are logged and monitored.
                </p>
              </div>
            </div>
          </div>
        </div>

        {step === "credentials" ? (
          <form onSubmit={handleCredentialsSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium text-gray-700">
                Admin Email
              </label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={credentials.email}
                onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium text-gray-700">
                Password
              </label>
              <PasswordInput
                id="password"
                placeholder="Enter your admin password"
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                required
                disabled={isLoading}
              />
            </div>

            <Button type="submit" className="w-full bg-red-600 hover:bg-red-700" disabled={isLoading}>
              {isLoading ? "Verifying..." : "Continue"}
            </Button>
          </form>
        ) : step === "2fa" ? (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800">
                    Choose your preferred 2FA method to complete sign-in.
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-3">
              <Button
                type="button"
                variant={twoFactorMethod === "totp" ? "default" : "outline"}
                onClick={() => setTwoFactorMethod("totp")}
                className="w-full justify-start h-auto p-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Authenticator App</p>
                    <p className="text-sm text-gray-500">Use your TOTP authenticator app</p>
                  </div>
                </div>
              </Button>

              <Button
                type="button"
                variant={twoFactorMethod === "email" ? "default" : "outline"}
                onClick={() => {
                  setTwoFactorMethod("email")
                  setStep("email2fa")
                  handleSendEmail2FA()
                }}
                className="w-full justify-start h-auto p-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Email Code</p>
                    <p className="text-sm text-gray-500">Get a code sent to your email</p>
                  </div>
                </div>
              </Button>
            </div>

            {twoFactorMethod === "totp" && (
              <form onSubmit={handleTwoFactorSubmit} className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800">
                    Open your authenticator app and enter the 6-digit code, or use one of your backup codes.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="twoFactorCode" className="text-sm font-medium text-gray-700">
                Authentication Code
              </label>
              <Input
                id="twoFactorCode"
                type="text"
                placeholder="Enter 6-digit code or backup code"
                value={twoFactorCode}
                onChange={(e) => setTwoFactorCode(e.target.value.replace(/\s/g, ""))}
                required
                disabled={isLoading}
                maxLength={9} // Allow for backup codes like XXXX-XXXX
                className="text-center text-lg tracking-wider"
                autoComplete="one-time-code"
              />
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setStep("credentials")
                  setError("")
                  setTwoFactorCode("")
                }}
                disabled={isLoading}
                className="flex-1"
              >
                Back
              </Button>
              <Button 
                type="submit" 
                className="flex-1 bg-red-600 hover:bg-red-700" 
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Sign In"}
              </Button>
            </div>
          </form>
        )}
          </div>
        ) : (
          <form onSubmit={handleEmail2FASubmit} className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800">
                    We've sent a 6-digit security code to your email. Enter it below to complete sign-in.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="email2FACode" className="text-sm font-medium text-gray-700">
                Email Security Code
              </label>
              <Input
                id="email2FACode"
                type="text"
                placeholder="Enter 6-digit code"
                value={email2FACode}
                onChange={(e) => setEmail2FACode(e.target.value.replace(/\s/g, ""))}
                required
                disabled={isLoading}
                maxLength={6}
                className="text-center text-lg tracking-wider"
                autoComplete="one-time-code"
              />
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setStep("2fa")
                  setError("")
                  setEmail2FACode("")
                }}
                disabled={isLoading}
                className="flex-1"
              >
                Back to 2FA Options
              </Button>
              <Button 
                type="submit" 
                className="flex-1 bg-red-600 hover:bg-red-700" 
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Sign In"}
              </Button>
            </div>

            <div className="text-center">
              <Button
                type="button"
                variant="ghost"
                onClick={handleResendEmail2FA}
                disabled={isResending || resendCooldown > 0}
                className="text-sm"
              >
                {isResending 
                  ? "Sending..." 
                  : resendCooldown > 0 
                    ? `Resend in ${Math.floor(resendCooldown / 60)}:${String(resendCooldown % 60).padStart(2, '0')}`
                    : "Resend Code"
                }
              </Button>
            </div>
          </form>
        )}

        <div className="text-center text-sm text-gray-600">
          <p>Need help? Contact your system administrator.</p>
        </div>
      </div>
    </AuthLayout>
  )
}

export default function AdminSignIn() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AdminSignInForm />
    </Suspense>
  )
}