import Link from "next/link"
import { auth } from "@/lib/auth"
import { SignOutButton } from "@/components/auth/sign-out-button"

export default async function Unauthorized() {
  const session = await auth()

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-6 shadow rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Access Denied
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              You don't have permission to access this page.
            </p>
          </div>

          <div className="mt-6">
            {session?.user ? (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Signed in as: <span className="font-medium">{session.user.email}</span>
                  </p>
                  <p className="text-sm text-gray-600">
                    Role: <span className="font-medium">{session.user.role}</span>
                  </p>
                </div>

                <div className="space-y-2">
                  {session.user.role === "SUPER_ADMIN" && (
                    <Link
                      href="/admin/dashboard"
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Go to Admin Dashboard
                    </Link>
                  )}

                  {["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP"].includes(session.user.role) && (
                    <Link
                      href="/business/dashboard"
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Go to Business Dashboard
                    </Link>
                  )}

                  {session.user.role === "CANDIDATE" && (
                    <Link
                      href="/candidate/dashboard"
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      Go to Candidate Dashboard
                    </Link>
                  )}

                  <SignOutButton className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" />
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Link
                  href="/auth/candidate/signin"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  Sign In as Candidate
                </Link>
                <Link
                  href="/auth/business/signin"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Sign In as Business User
                </Link>
                <Link
                  href="/auth/admin/signin"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Sign In as Admin
                </Link>
              </div>
            )}
          </div>

          <div className="mt-6 text-center">
            <Link
              href="/"
              className="text-sm text-indigo-600 hover:text-indigo-500"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}