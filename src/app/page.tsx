import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <h1 className="text-2xl font-bold text-gray-900">Sourceflex</h1>
          </div>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/jobs" className="text-gray-600 hover:text-gray-900">
              Browse Jobs
            </Link>
            <Link href="/business" className="text-gray-600 hover:text-gray-900">
              For Employers
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/auth/candidate/signin">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/auth/candidate/signin">
                <Button>Get Started</Button>
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Connect Top Talent with
            <span className="text-blue-600"> Leading Companies</span>
          </h2>
          <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
            The comprehensive recruitment platform that combines job boards, CRM, ATS, 
            and social media integration to streamline your hiring process.
          </p>

          {/* Dual CTA Section */}
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Candidate Section */}
            <div className="bg-white rounded-xl shadow-lg p-8 border border-blue-200">
              <div className="text-blue-600 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6.294a2 2 0 01-1.382 1.905l-2.236.745A2 2 0 0114 17.382V17a2 2 0 00-2-2h-4a2 2 0 00-2 2v.382a2 2 0 01-.618 1.449l-2.236-.745A2 2 0 012 16.294V8a2 2 0 012-2V6z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Find Your Next Job</h3>
              <p className="text-gray-600 mb-6">
                Discover opportunities at top companies. Apply with one click using 
                your social profiles or create a detailed application.
              </p>
              <div className="space-y-3">
                <Link href="/auth/candidate/signup" className="block">
                  <Button className="w-full" size="lg">
                    Start Your Job Search
                  </Button>
                </Link>
                <Link href="/auth/candidate/signin" className="block">
                  <Button variant="outline" className="w-full">
                    I Already Have an Account
                  </Button>
                </Link>
              </div>
            </div>

            {/* Business Section */}
            <div className="bg-white rounded-xl shadow-lg p-8 border border-slate-200">
              <div className="text-slate-600 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Hire Top Talent</h3>
              <p className="text-gray-600 mb-6">
                Access our comprehensive ATS, CRM, and vendor management system. 
                Post jobs and manage your entire hiring pipeline.
              </p>
              <div className="space-y-3">
                <Link href="/auth/business/signup" className="block">
                  <Button className="w-full bg-slate-900 hover:bg-slate-800" size="lg">
                    Start Hiring Today
                  </Button>
                </Link>
                <Link href="/auth/business/signin" className="block">
                  <Button variant="outline" className="w-full">
                    Business Sign In
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="mt-20">
            <h3 className="text-2xl font-bold text-gray-900 mb-12">
              Everything You Need in One Platform
            </h3>
            <div className="grid md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6.294a2 2 0 01-1.382 1.905l-2.236.745A2 2 0 0114 17.382V17a2 2 0 00-2-2h-4a2 2 0 00-2 2v.382a2 2 0 01-.618 1.449l-2.236-.745A2 2 0 012 16.294V8a2 2 0 012-2V6z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Job Board</h4>
                <p className="text-sm text-gray-600">Public job listings with advanced search</p>
              </div>
              
              <div className="text-center">
                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">ATS System</h4>
                <p className="text-sm text-gray-600">Complete applicant tracking system</p>
              </div>
              
              <div className="text-center">
                <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">CRM & Analytics</h4>
                <p className="text-sm text-gray-600">Sales pipeline and performance tracking</p>
              </div>
              
              <div className="text-center">
                <div className="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-5 8l-2-2m0 0l-2-2m2 2l2-2m-4 4v6a1 1 0 001 1h2a1 1 0 001-1v-6m-4 0h4" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Social Integration</h4>
                <p className="text-sm text-gray-600">Automated posting to social platforms</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
