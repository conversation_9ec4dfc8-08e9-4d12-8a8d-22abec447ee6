"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function TwoFactorSetup() {
  const [step, setStep] = useState<"status" | "setup" | "verify" | "backup" | "complete">("status")
  const [qrCode, setQrCode] = useState("")
  const [secret, setSecret] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [backupCodes, setBackupCodes] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [twoFactorStatus, setTwoFactorStatus] = useState({
    enabled: false,
    backupCodesCount: 0,
  })

  const router = useRouter()

  useEffect(() => {
    fetchTwoFactorStatus()
  }, [])

  const fetchTwoFactorStatus = async () => {
    try {
      const response = await fetch("/api/admin/2fa/status")
      if (response.ok) {
        const data = await response.json()
        setTwoFactorStatus(data)
      }
    } catch (error) {
      console.error("Error fetching 2FA status:", error)
    }
  }

  const handleSetupStart = async () => {
    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/admin/2fa/generate", {
        method: "POST",
      })

      if (response.ok) {
        const data = await response.json()
        setQrCode(data.qrCode)
        setSecret(data.secret)
        setStep("setup")
      } else {
        const result = await response.json()
        setError(result.message || "Failed to generate 2FA setup")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyAndEnable = async () => {
    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/admin/2fa/enable", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          secret,
          code: verificationCode,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        setBackupCodes(data.backupCodes)
        setStep("backup")
      } else {
        const result = await response.json()
        setError(result.message || "Invalid verification code")
        setVerificationCode("")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleDisable2FA = async () => {
    if (!confirm("Are you sure you want to disable 2FA? This will reduce your account security.")) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      const response = await fetch("/api/admin/2fa/disable", {
        method: "POST",
      })

      if (response.ok) {
        setTwoFactorStatus({ enabled: false, backupCodesCount: 0 })
        setStep("status")
      } else {
        const result = await response.json()
        setError(result.message || "Failed to disable 2FA")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Two-Factor Authentication</h1>
              <p className="text-sm text-gray-600">Secure your admin account with 2FA</p>
            </div>
            <Link href="/admin/dashboard">
              <Button variant="outline">Back to Dashboard</Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-6">
            {error}
          </div>
        )}

        {step === "status" && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center">
              <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                twoFactorStatus.enabled ? "bg-green-100" : "bg-yellow-100"
              }`}>
                <svg className={`w-8 h-8 ${
                  twoFactorStatus.enabled ? "text-green-600" : "text-yellow-600"
                }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>

              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Two-Factor Authentication {twoFactorStatus.enabled ? "Enabled" : "Disabled"}
              </h2>

              {twoFactorStatus.enabled ? (
                <div className="space-y-4">
                  <p className="text-gray-600">
                    Your account is protected with two-factor authentication.
                  </p>
                  <p className="text-sm text-gray-500">
                    Backup codes remaining: {twoFactorStatus.backupCodesCount}
                  </p>
                  <div className="space-x-4">
                    <Button 
                      onClick={handleDisable2FA}
                      variant="outline"
                      disabled={isLoading}
                    >
                      {isLoading ? "Disabling..." : "Disable 2FA"}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-gray-600">
                    Add an extra layer of security to your admin account by enabling two-factor authentication.
                  </p>
                  <Button 
                    onClick={handleSetupStart}
                    disabled={isLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isLoading ? "Setting up..." : "Enable 2FA"}
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}

        {step === "setup" && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Set Up Authenticator App</h2>
              
              <div className="space-y-4">
                <p className="text-gray-600">
                  Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
                </p>
                
                {qrCode && (
                  <div className="flex justify-center">
                    <div className="p-4 bg-white border rounded-lg">
                      <Image
                        src={qrCode}
                        alt="2FA QR Code"
                        width={200}
                        height={200}
                        className="mx-auto"
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <p className="text-sm text-gray-600">Or enter this secret manually:</p>
                  <div className="flex items-center justify-center space-x-2">
                    <code className="bg-gray-100 px-3 py-2 rounded text-sm font-mono">
                      {secret}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(secret)}
                    >
                      Copy
                    </Button>
                  </div>
                </div>

                <Button onClick={() => setStep("verify")} className="w-full">
                  I've Added the Account
                </Button>
              </div>
            </div>
          </div>
        )}

        {step === "verify" && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Verify Setup</h2>
              
              <div className="space-y-4">
                <p className="text-gray-600">
                  Enter the 6-digit code from your authenticator app to verify the setup.
                </p>
                
                <div className="max-w-xs mx-auto">
                  <Input
                    type="text"
                    placeholder="Enter 6-digit code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, "").slice(0, 6))}
                    className="text-center text-lg tracking-wider"
                    maxLength={6}
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setStep("setup")}
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handleVerifyAndEnable}
                    disabled={isLoading || verificationCode.length !== 6}
                    className="flex-1"
                  >
                    {isLoading ? "Verifying..." : "Verify & Enable"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === "backup" && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="space-y-6">
              <div className="text-center">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900">2FA Enabled Successfully!</h2>
                <p className="text-gray-600 mt-2">Save these backup codes in a secure location</p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-800">
                      <strong>Important:</strong> Store these backup codes safely. Each code can only be used once 
                      and they're your only way to access your account if you lose your authenticator device.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                {backupCodes.map((code, index) => (
                  <div key={index} className="bg-gray-100 p-3 rounded text-center">
                    <code className="text-sm font-mono">{code}</code>
                  </div>
                ))}
              </div>

              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(backupCodes.join('\n'))}
                  className="flex-1"
                >
                  Copy All Codes
                </Button>
                <Button
                  onClick={() => {
                    setStep("complete")
                    setTimeout(() => router.push("/admin/dashboard"), 2000)
                  }}
                  className="flex-1"
                >
                  I've Saved the Codes
                </Button>
              </div>
            </div>
          </div>
        )}

        {step === "complete" && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Setup Complete!</h2>
              <p className="text-gray-600">
                Your account is now protected with two-factor authentication.
                You'll be redirected to the dashboard shortly.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}