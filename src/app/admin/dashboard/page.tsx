import { requireAdmin } from "@/lib/auth-utils"
import { db } from "@/lib/db"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { SignOutButton } from "@/components/auth/sign-out-button"
import { DashboardStats } from "@/components/admin/dashboard-stats"
import { RecentActivity } from "@/components/admin/recent-activity"
import { startOfMonth, subDays } from "date-fns"

export default async function AdminDashboard() {
  // Ensure user is admin
  const user = await requireAdmin()

  // Get comprehensive platform statistics
  const [
    totalUsers,
    candidateCount,
    businessUserCount,
    adminCount,
    totalOrgs,
    activeOrgs,
    totalJobs,
    publishedJobs,
    draftJobs,
    closedJobs,
    totalApplications,
    totalAuditLogs,
    failedOperations,
    recentUsers,
    recentJobs,
    recentActivity,
    auditLogs
  ] = await Promise.all([
    // User statistics
    db.user.count(),
    db.user.count({ where: { role: "CANDIDATE" } }),
    db.user.count({ where: { role: { in: ["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP"] } } }),
    db.user.count({ where: { role: "SUPER_ADMIN" } }),
    
    // Organization statistics
    db.organization.count(),
    db.organization.count({ where: { users: { some: {} } } }),
    
    // Job statistics
    db.job.count(),
    db.job.count({ where: { status: "PUBLISHED" } }),
    db.job.count({ where: { status: "DRAFT" } }),
    db.job.count({ where: { status: "CLOSED" } }),
    db.application.count(),
    
    // Audit statistics
    db.auditLog.count(),
    db.auditLog.count({ where: { success: false } }),
    
    // Recent activity counts
    db.user.count({ where: { createdAt: { gte: startOfMonth(new Date()) } } }),
    db.job.count({ where: { createdAt: { gte: startOfMonth(new Date()) } } }),
    db.auditLog.count({ where: { createdAt: { gte: subDays(new Date(), 1) } } }),
    
    // Recent audit logs for activity feed
    db.auditLog.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            role: true,
          }
        }
      }
    })
  ])

  const userStats = {
    totalUsers,
    candidateCount,
    businessUserCount,
    adminCount,
    recentUsers,
    activeOrganizations: activeOrgs,
  }

  const jobStats = {
    totalJobs,
    publishedJobs,
    draftJobs,
    closedJobs,
    totalApplications,
    recentJobs,
  }

  const auditStats = {
    totalLogs: totalAuditLogs,
    failedOperations,
    recentActivity,
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-sm text-gray-600">Platform Administration Portal</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user.name || user.email}</span>
              <SignOutButton variant="outline" size="sm" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Dashboard Statistics */}
        <DashboardStats 
          userStats={userStats}
          jobStats={jobStats}
          auditStats={auditStats}
        />

        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Activity - Takes 2 columns */}
          <div className="lg:col-span-2">
            <RecentActivity activities={auditLogs as any} />
          </div>

          {/* Quick Actions - Takes 1 column */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <Link href="/admin/users">
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                    <div className="flex items-center gap-3">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Manage Users</p>
                        <p className="text-xs text-gray-500">View and manage all platform users</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/admin/organizations">
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                    <div className="flex items-center gap-3">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Organizations</p>
                        <p className="text-xs text-gray-500">Manage client organizations</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/admin/audit">
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                    <div className="flex items-center gap-3">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Audit Logs</p>
                        <p className="text-xs text-gray-500">View system activity logs</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/admin/2fa-setup">
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                    <div className="flex items-center gap-3">
                      <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Setup 2FA</p>
                        <p className="text-xs text-gray-500">Configure two-factor authentication</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/admin/system-health">
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                    <div className="flex items-center gap-3">
                      <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-gray-900">System Health</p>
                        <p className="text-xs text-gray-500">Monitor platform status</p>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}