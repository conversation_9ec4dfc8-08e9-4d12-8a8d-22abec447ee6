"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { api } from "@/lib/core/trpc";
import { formatDistanceToNow, format } from "date-fns";
import { 
  Search, 
  Filter, 
  Download,
  Shield,
  User,
  Building2,
  Briefcase,
  FileText,
  Settings,
  UserPlus,
  Trash2,
  Edit,
  Eye,
  AlertCircle,
  Activity,
  Calendar,
  Clock
} from "lucide-react";

export function AdminAuditClient() {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [actionFilter, setActionFilter] = useState<string | undefined>(undefined);
  const [resourceFilter, setResourceFilter] = useState<string | undefined>(undefined);
  const [successFilter, setSuccessFilter] = useState<boolean | null>(null);
  const limit = 50;

  // Fetch audit logs with filters
  const { data: auditData, isLoading } = api.audit.getLogs.useQuery({
    page: currentPage,
    limit,
    search: searchTerm || undefined,
    action: actionFilter as any,
    resourceType: resourceFilter as any,
  });

  // Apply client-side success filter if needed
  const rawAuditLogs = auditData?.logs ?? [];
  const auditLogs = successFilter !== null 
    ? rawAuditLogs.filter(log => log.success === successFilter)
    : rawAuditLogs;
  const totalCount = auditData?.pagination.total ?? 0;
  const totalPages = Math.ceil(totalCount / limit);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
    setCurrentPage(1);
  };

  const handleActionFilter = (action: string) => {
    const newAction = actionFilter === action ? undefined : action;
    setActionFilter(newAction);
    setCurrentPage(1);
  };

  const handleResourceFilter = (resource: string) => {
    const newResource = resourceFilter === resource ? undefined : resource;
    setResourceFilter(newResource);
    setCurrentPage(1);
  };

  const handleSuccessFilter = (success: boolean) => {
    const newSuccess = successFilter === success ? null : success;
    setSuccessFilter(newSuccess);
    setCurrentPage(1);
  };

  const handleExport = () => {
    // TODO: Implement audit log export functionality
    console.log("Export audit logs");
  };

  const getActionIcon = (action: string) => {
    if (action.includes("CREATED")) return UserPlus;
    if (action.includes("UPDATED")) return Edit;
    if (action.includes("DELETED")) return Trash2;
    if (action.includes("LOGIN")) return Shield;
    if (action.includes("SETTINGS")) return Settings;
    if (action.includes("VIEW")) return Eye;
    return AlertCircle;
  };

  const getResourceIcon = (resourceType: string) => {
    switch (resourceType) {
      case "USER": return User;
      case "ORGANIZATION": return Building2;
      case "JOB": return Briefcase;
      case "APPLICATION": return FileText;
      case "AUTHENTICATION": return Shield;
      default: return Settings;
    }
  };

  const getActionColor = (action: string, success: boolean) => {
    if (!success) return "bg-red-100 text-red-800";
    if (action.includes("CREATED")) return "bg-green-100 text-green-800";
    if (action.includes("UPDATED")) return "bg-blue-100 text-blue-800";
    if (action.includes("DELETED")) return "bg-red-100 text-red-800";
    if (action.includes("LOGIN")) return "bg-purple-100 text-purple-800";
    return "bg-gray-100 text-gray-800";
  };

  const formatAction = (action: string) => {
    return action
      .toLowerCase()
      .split("_")
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4 animate-pulse">
                  <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Audit Logs ({totalCount})
            </CardTitle>
            <Button onClick={handleExport} variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
          
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by user email, action, or resource..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-2">
              {/* Action Filters */}
              <div className="flex gap-1">
                {["USER_CREATED", "USER_UPDATED", "USER_DELETED", "USER_LOGIN", "USER_LOGIN_FAILED"].map((action) => (
                  <Button
                    key={action}
                    variant={actionFilter === action ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleActionFilter(action)}
                    className="text-xs"
                  >
                    {formatAction(action)}
                  </Button>
                ))}
              </div>

              {/* Resource Filters */}
              <div className="flex gap-1 border-l pl-2 ml-2">
                {["USER", "ORGANIZATION", "JOB", "APPLICATION", "AUTHENTICATION"].map((resource) => (
                  <Button
                    key={resource}
                    variant={resourceFilter === resource ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleResourceFilter(resource)}
                    className="text-xs"
                  >
                    {resource}
                  </Button>
                ))}
              </div>

              {/* Success Filters */}
              <div className="flex gap-1 border-l pl-2 ml-2">
                <Button
                  variant={successFilter === true ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleSuccessFilter(true)}
                  className="text-xs text-green-600"
                >
                  Success
                </Button>
                <Button
                  variant={successFilter === false ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleSuccessFilter(false)}
                  className="text-xs text-red-600"
                >
                  Failed
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Audit Logs */}
      <Card>
        <CardContent className="p-0">
          {auditLogs.length === 0 ? (
            <div className="text-center py-12">
              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">No audit logs found</p>
              <p className="text-gray-400 text-sm">
                {searchTerm || actionFilter || resourceFilter || successFilter !== null
                  ? "Try adjusting your search filters"
                  : "No activity recorded yet"}
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {auditLogs.map((log) => {
                const ActionIcon = getActionIcon(log.action);
                const ResourceIcon = getResourceIcon(log.resourceType);
                const userName = log.user?.name || log.userName || "System";
                const userEmail = log.user?.email || log.userEmail || "";

                return (
                  <div key={log.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start gap-4">
                      {/* Icon Stack */}
                      <div className="flex-shrink-0">
                        <div className="relative">
                          <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                            <ResourceIcon className="h-5 w-5 text-gray-600" />
                          </div>
                          <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-white rounded-full flex items-center justify-center border border-gray-200">
                            <ActionIcon className="h-2.5 w-2.5 text-gray-500" />
                          </div>
                        </div>
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <p className="text-sm font-medium text-gray-900">
                            {userName}
                          </p>
                          <Badge className={getActionColor(log.action, log.success)}>
                            {formatAction(log.action)}
                          </Badge>
                          {!log.success && (
                            <Badge variant="outline" className="text-red-600 border-red-200">
                              Failed
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">
                          {formatAction(log.action)} {log.resourceType.toLowerCase()}
                          {log.resourceId && (
                            <span className="text-gray-400"> • {log.resourceId.slice(0, 8)}...</span>
                          )}
                        </p>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <div className="flex items-center gap-4">
                            {userEmail && (
                              <span className="truncate max-w-xs">{userEmail}</span>
                            )}
                            {log.ipAddress && (
                              <span>IP: {log.ipAddress}</span>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-3 w-3" />
                            <span>{format(new Date(log.createdAt), "MMM d, yyyy 'at' h:mm a")}</span>
                            <span className="text-gray-400">
                              ({formatDistanceToNow(new Date(log.createdAt), { addSuffix: true })})
                            </span>
                          </div>
                        </div>

                        {/* Additional Details */}
                        {log.metadata && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <p className="text-xs text-gray-600">
                              <strong>Details:</strong> {JSON.stringify(log.metadata, null, 2)}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="flex items-center justify-between py-4">
            <div className="text-sm text-gray-500">
              Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, totalCount)} of {totalCount} logs
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = Math.max(1, Math.min(currentPage - 2 + i, totalPages - 4 + i));
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                );
              })}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}