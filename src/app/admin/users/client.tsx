"use client";

import { useState } from "react";
import { UsersTable } from "@/components/admin/users-table";
import { api } from "@/lib/core/trpc";

export function AdminUsersClient() {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string | undefined>(undefined);
  const limit = 20;

  // Fetch users with filters
  const { data: usersData, isLoading } = api.users.getAll.useQuery({
    page: currentPage,
    limit,
    search: searchTerm || undefined,
    role: roleFilter as any,
  });

  const users = usersData?.users ?? [];
  const totalCount = usersData?.pagination?.total ?? 0;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleRoleFilter = (role: string | null) => {
    setRoleFilter(role || undefined);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleUserAction = (action: string, userId: string) => {
    switch (action) {
      case "invite":
        // TODO: Open invite user modal
        console.log("Invite user");
        break;
      case "edit":
        // TODO: Open edit user modal
        console.log("Edit user:", userId);
        break;
      case "email":
        // TODO: Send email to user
        console.log("Email user:", userId);
        break;
      case "delete":
        // TODO: Delete user with confirmation
        console.log("Delete user:", userId);
        break;
      default:
        console.log("Unknown action:", action);
    }
  };

  return (
    <UsersTable
      users={users as any}
      totalCount={totalCount}
      currentPage={currentPage}
      limit={limit}
      isLoading={isLoading}
      onPageChange={handlePageChange}
      onSearch={handleSearch}
      onRoleFilter={handleRoleFilter}
      onUserAction={handleUserAction}
    />
  );
}