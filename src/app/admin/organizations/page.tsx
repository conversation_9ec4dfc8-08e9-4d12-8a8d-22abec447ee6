import { requireAdmin } from "@/lib/auth-utils";
import { AdminOrganizationsClient } from "./client";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default async function AdminOrganizationsPage() {
  // Ensure user is admin
  const user = await requireAdmin();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center gap-4">
              <Link href="/admin/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Organization Management</h1>
                <p className="text-sm text-gray-600">Manage all client organizations and their settings</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <AdminOrganizationsClient />
      </div>
    </div>
  );
}