"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { api } from "@/lib/core/trpc";
import { formatDistanceToNow } from "date-fns";
import { 
  Search, 
  Building2, 
  Users, 
  Briefcase, 
  MoreHorizontal,
  Plus,
  Edit,
  Trash2,
  Settings,
  Calendar
} from "lucide-react";

export function AdminOrganizationsClient() {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const limit = 12;

  // Fetch organizations with filters
  const { data: orgsData, isLoading } = api.organizations.getAll.useQuery({
    page: currentPage,
    limit,
    search: searchTerm || undefined,
  });

  const organizations = orgsData?.organizations ?? [];
  const totalCount = orgsData?.pagination.total ?? 0;
  const totalPages = Math.ceil(totalCount / limit);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleOrgAction = (action: string, orgId: string) => {
    switch (action) {
      case "create":
        // TODO: Open create organization modal
        console.log("Create organization");
        break;
      case "edit":
        // TODO: Open edit organization modal
        console.log("Edit organization:", orgId);
        break;
      case "settings":
        // TODO: Open organization settings
        console.log("Organization settings:", orgId);
        break;
      case "delete":
        // TODO: Delete organization with confirmation
        console.log("Delete organization:", orgId);
        break;
      default:
        console.log("Unknown action:", action);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-4 animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="flex gap-4">
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Organizations ({totalCount})
            </CardTitle>
            <Button onClick={() => handleOrgAction("create", "")} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Organization
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search organizations by name or slug..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Organizations Grid */}
      {organizations.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">No organizations found</p>
            <p className="text-gray-400 text-sm">
              {searchTerm ? "Try adjusting your search terms" : "Create your first organization to get started"}
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {organizations.map((org) => (
              <Card key={org.id} className="hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{org.name}</CardTitle>
                      <p className="text-sm text-gray-500">/{org.slug}</p>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleOrgAction("edit", org.id)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleOrgAction("settings", org.id)}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleOrgAction("delete", org.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Organization Stats */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center gap-2 text-sm">
                        <div className="p-1.5 bg-blue-100 rounded">
                          <Users className="h-3 w-3 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{org._count.users}</p>
                          <p className="text-xs text-gray-500">Users</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm">
                        <div className="p-1.5 bg-green-100 rounded">
                          <Briefcase className="h-3 w-3 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{org._count.jobs}</p>
                          <p className="text-xs text-gray-500">Jobs</p>
                        </div>
                      </div>
                    </div>

                    {/* Created Date */}
                    <div className="flex items-center gap-2 text-xs text-gray-500 pt-2 border-t">
                      <Calendar className="h-3 w-3" />
                      Created {formatDistanceToNow(new Date(org.createdAt), { addSuffix: true })}
                    </div>

                    {/* Settings Badge */}
                    {org.settings && Object.keys(org.settings as object).length > 0 && (
                      <Badge variant="outline" className="text-xs">
                        Custom Settings
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <Card>
              <CardContent className="flex items-center justify-between py-4">
                <div className="text-sm text-gray-500">
                  Showing {(currentPage - 1) * limit + 1} to {Math.min(currentPage * limit, totalCount)} of {totalCount} organizations
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = Math.max(1, Math.min(currentPage - 2 + i, totalPages - 4 + i));
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    );
                  })}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}