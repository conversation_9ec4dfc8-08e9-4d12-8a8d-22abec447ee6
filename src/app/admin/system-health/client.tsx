"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { api } from "@/lib/core/trpc";
import { 
  Activity, 
  Database, 
  Server, 
  Globe, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Wifi,
  HardDrive,
  Cpu,
  RefreshCw,
  TrendingUp,
  TrendingDown
} from "lucide-react";

interface SystemMetric {
  name: string;
  status: "healthy" | "warning" | "error";
  value: string;
  description: string;
  icon: any;
  trend?: "up" | "down" | "stable";
}

export function AdminSystemHealthClient() {
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // In a real implementation, these would come from actual system monitoring APIs
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([
    {
      name: "Database Connection",
      status: "healthy",
      value: "Connected",
      description: "PostgreSQL database is responding normally",
      icon: Database,
      trend: "stable"
    },
    {
      name: "Server Response Time",
      status: "healthy",
      value: "45ms",
      description: "Average response time across all endpoints",
      icon: Server,
      trend: "down"
    },
    {
      name: "API Availability",
      status: "healthy",
      value: "99.9%",
      description: "Uptime over the last 24 hours",
      icon: Globe,
      trend: "stable"
    },
    {
      name: "Authentication Service",
      status: "healthy",
      value: "Operational",
      description: "NextAuth.js service is functioning normally",
      icon: CheckCircle,
      trend: "stable"
    },
    {
      name: "Email Service",
      status: "healthy",
      value: "Active",
      description: "Email delivery system is operational",
      icon: Wifi,
      trend: "stable"
    },
    {
      name: "Storage Usage",
      status: "warning",
      value: "78%",
      description: "Disk space usage is approaching limits",
      icon: HardDrive,
      trend: "up"
    }
  ]);

  // Get some real data from the system
  const { data: auditStats } = api.audit.getStats.useQuery({});

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setLastUpdated(new Date());
      setIsRefreshing(false);
    }, 1000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy": return "bg-green-100 text-green-800";
      case "warning": return "bg-yellow-100 text-yellow-800";
      case "error": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy": return CheckCircle;
      case "warning": return AlertTriangle;
      case "error": return XCircle;
      default: return Activity;
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case "up": return TrendingUp;
      case "down": return TrendingDown;
      default: return null;
    }
  };

  const getTrendColor = (trend?: string, isGood?: boolean) => {
    if (!trend || trend === "stable") return "text-gray-400";
    if (trend === "up") return isGood ? "text-green-500" : "text-red-500";
    if (trend === "down") return isGood ? "text-red-500" : "text-green-500";
    return "text-gray-400";
  };

  const healthyCount = systemMetrics.filter(m => m.status === "healthy").length;
  const warningCount = systemMetrics.filter(m => m.status === "warning").length;
  const errorCount = systemMetrics.filter(m => m.status === "error").length;

  return (
    <div className="space-y-6">
      {/* Overall Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Status Overview
            </CardTitle>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
              <Button 
                onClick={handleRefresh} 
                variant="outline" 
                size="sm"
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{healthyCount}</div>
              <div className="text-sm text-gray-500">Healthy</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600">{warningCount}</div>
              <div className="text-sm text-gray-500">Warnings</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">{errorCount}</div>
              <div className="text-sm text-gray-500">Errors</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {systemMetrics.map((metric) => {
          const MetricIcon = metric.icon;
          const StatusIcon = getStatusIcon(metric.status);
          const TrendIcon = getTrendIcon(metric.trend);
          
          return (
            <Card key={metric.name}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <MetricIcon className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {TrendIcon && (
                      <TrendIcon className={`h-4 w-4 ${getTrendColor(metric.trend, metric.name.includes("Response Time"))}`} />
                    )}
                    <StatusIcon className={`h-4 w-4 ${
                      metric.status === "healthy" ? "text-green-500" :
                      metric.status === "warning" ? "text-yellow-500" : "text-red-500"
                    }`} />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
                    <Badge className={getStatusColor(metric.status)} variant="secondary">
                      {metric.status.toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{metric.description}</p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {auditStats && (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total Operations (24h)</span>
                    <span className="font-semibold">{auditStats.recentActivity}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Failed Operations</span>
                    <span className="font-semibold text-red-600">{auditStats.failedOperations}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Success Rate</span>
                    <span className="font-semibold text-green-600">
                      {auditStats.recentActivity > 0 
                        ? ((auditStats.recentActivity - auditStats.failedOperations) / auditStats.recentActivity * 100).toFixed(1)
                        : 100}%
                    </span>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* System Resources */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              System Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Memory Usage</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: "65%" }}></div>
                  </div>
                  <span className="text-sm font-semibold">65%</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">CPU Usage</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: "32%" }}></div>
                  </div>
                  <span className="text-sm font-semibold">32%</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Network I/O</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-purple-600 h-2 rounded-full" style={{ width: "18%" }}></div>
                  </div>
                  <span className="text-sm font-semibold">18%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts and Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Active Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {warningCount > 0 || errorCount > 0 ? (
            <div className="space-y-3">
              {systemMetrics
                .filter(m => m.status === "warning" || m.status === "error")
                .map((metric) => (
                  <div key={metric.name} className="flex items-center gap-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    <div className="flex-1">
                      <div className="font-medium text-yellow-800">{metric.name}</div>
                      <div className="text-sm text-yellow-700">{metric.description}</div>
                    </div>
                    <Badge className={getStatusColor(metric.status)}>
                      {metric.status.toUpperCase()}
                    </Badge>
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-gray-500">No active alerts</p>
              <p className="text-sm text-gray-400">All systems are operating normally</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}