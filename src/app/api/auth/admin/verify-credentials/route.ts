import { NextRequest, NextResponse } from "next/server"
import { compare } from "bcryptjs"
import { z } from "zod"

import { db } from "@/lib/db"

const verifyCredentialsSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = verifyCredentialsSchema.parse(body)

    // Find admin user
    const user = await db.user.findUnique({
      where: { 
        email: validatedData.email,
        role: "SUPER_ADMIN"
      },
      select: {
        id: true,
        email: true,
        password: true,
        twoFactorEnabled: true,
      },
    })

    if (!user || !user.password) {
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      )
    }

    // Verify password
    const isPasswordValid = await compare(validatedData.password, user.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      )
    }

    // Return whether 2FA is required
    return NextResponse.json({
      message: "Credentials verified",
      requires2FA: user.twoFactorEnabled,
      userId: user.id,
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input", errors: error.errors },
        { status: 400 }
      )
    }

    console.error("Admin credentials verification error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}