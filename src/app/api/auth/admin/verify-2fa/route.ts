import { NextRequest, NextResponse } from "next/server"
import { compare } from "bcryptjs"
import { z } from "zod"

import { db } from "@/lib/db"
import { verifyTwoFactorToken } from "@/lib/two-factor"

const verify2FASchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
  twoFactorCode: z.string().min(1),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = verify2FASchema.parse(body)

    // Find admin user
    const user = await db.user.findUnique({
      where: { 
        email: validatedData.email,
        role: "SUPER_ADMIN"
      },
      select: {
        id: true,
        email: true,
        password: true,
        twoFactorEnabled: true,
        twoFactorSecret: true,
      },
    })

    if (!user || !user.password) {
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      )
    }

    // Verify password again
    const isPasswordValid = await compare(validatedData.password, user.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      )
    }

    // Verify 2FA if enabled
    if (user.twoFactorEnabled) {
      const twoFactorResult = await verifyTwoFactorToken(
        user.id, 
        validatedData.twoFactorCode
      )

      if (!twoFactorResult.valid) {
        return NextResponse.json(
          { message: "Invalid 2FA code" },
          { status: 401 }
        )
      }

      // Log the authentication method used
      console.log(`Admin ${user.email} authenticated with ${twoFactorResult.method}`)
    }

    return NextResponse.json({
      message: "Authentication successful",
      user: {
        id: user.id,
        email: user.email,
      },
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input", errors: error.errors },
        { status: 400 }
      )
    }

    console.error("Admin 2FA verification error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}