import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

import { 
  resetPasswordSchema, 
  resetPasswordWithToken 
} from "@/lib/auth-utils"
import { withCSRFProtection } from "@/lib/security/protection/csrf-middleware"

async function handleResetPassword(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = resetPasswordSchema.parse(body)

    try {
      // Reset password with token
      const user = await resetPasswordWithToken(
        validatedData.token,
        validatedData.password
      )

      return NextResponse.json(
        { 
          message: "Password has been reset successfully. You can now sign in with your new password.",
          success: true,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
          }
        },
        { status: 200 }
      )
    } catch (error) {
      console.error("Password reset error:", error)
      console.error("Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        token: validatedData.token.substring(0, 10) + '...', // Log partial token for debugging
      })
      
      if (error instanceof Error) {
        if (error.message.includes("Invalid or expired") || error.message.includes("expired")) {
          return NextResponse.json(
            { message: "Invalid or expired reset token. Please request a new password reset." },
            { status: 400 }
          )
        }
        
        if (error.message.includes("User not found")) {
          return NextResponse.json(
            { message: "User account not found. Please contact support." },
            { status: 404 }
          )
        }
      }
      
      return NextResponse.json(
        { message: "Failed to reset password. Please try again." },
        { status: 500 }
      )
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Validation failed", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Reset password API error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export const POST = withCSRFProtection(handleResetPassword)