import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

import { createBusinessUser, businessSignupSchema } from "@/lib/auth-utils"
import { db } from "@/lib/core/db"
import { createVerificationCode } from "@/lib/core/verification"
import { sendEmailVerification } from "@/lib/core/email"
import { withCSRFProtection } from "@/lib/security/protection/csrf-middleware"

async function handleSignup(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = businessSignupSchema.parse(body)

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        role: true,
        createdAt: true,
      }
    })

    if (existingUser) {
      // Provide helpful error message based on user status
      if (!existingUser.emailVerified) {
        return NextResponse.json(
          { 
            message: "An account with this email exists but is not verified. Please check your email for the verification code.",
            accountExists: true,
            needsVerification: true,
            email: existingUser.email
          },
          { status: 409 } // Conflict
        )
      } else {
        // Account exists and is verified
        const accountType = existingUser.role === "CANDIDATE" ? "candidate" : "business"
        return NextResponse.json(
          { 
            message: `An account with this email already exists. Please sign in instead.`,
            accountExists: true,
            accountType,
            signInUrl: existingUser.role === "CANDIDATE" 
              ? "/auth/candidate/signin" 
              : "/auth/business/signin"
          },
          { status: 409 } // Conflict
        )
      }
    }

    // Create business user with organization
    const user = await createBusinessUser(validatedData)

    // Generate verification code and send email
    try {
      const verificationCode = await createVerificationCode(user.email)
      await sendEmailVerification(user.email, verificationCode, user.name || undefined)
    } catch (error) {
      console.error("Failed to send verification email:", error)
      // Don't fail the signup if email fails
    }

    return NextResponse.json(
      { 
        message: "Business account created successfully! Please check your email for a verification code.",
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organization: user.organization ? {
            id: user.organization.id,
            name: user.organization.name,
            slug: user.organization.slug,
          } : null,
        },
        requiresVerification: true
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Validation failed", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Business signup error:", error)
    
    // Handle specific business logic errors
    if (error instanceof Error) {
      if (error.message.includes("business email")) {
        return NextResponse.json(
          { 
            message: "Invalid business email",
            errors: { email: "Please use a valid business email address" }
          },
          { status: 400 }
        )
      }
      
      if (error.message.includes("organization")) {
        return NextResponse.json(
          { message: "Failed to create organization. Please try again." },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export const POST = withCSRFProtection(handleSignup)