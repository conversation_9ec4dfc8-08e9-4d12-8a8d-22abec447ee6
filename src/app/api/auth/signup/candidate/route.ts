import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

import { createCandidateUser, candidateSignupSchema } from "@/lib/auth-utils"
import { db } from "@/lib/core/db"
import { createVerificationCode } from "@/lib/core/verification"
import { sendEmailVerification } from "@/lib/core/email"
import { withCSRFProtection } from "@/lib/security/protection/csrf-middleware"
import { validatePasswordEnhanced } from "@/lib/validations/auth"
import { EnhancedPasswordValidator } from "@/lib/security/validation/enhanced-password-validator"

async function handleSignup(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = candidateSignupSchema.parse(body)

    // Enhanced password validation
    const passwordValidation = await validatePasswordEnhanced(
      validatedData.password,
      undefined, // No organization for candidates
      {
        email: validatedData.email,
        firstName: validatedData.name.split(' ')[0],
        lastName: validatedData.name.split(' ').slice(1).join(' ')
      }
    )

    if (!passwordValidation.isValid) {
      // Log password validation failure
      const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined
      const userAgent = request.headers.get('user-agent') || undefined
      
      await EnhancedPasswordValidator.logPasswordValidation({
        email: validatedData.email,
        result: passwordValidation
      }, ipAddress, userAgent)

      return NextResponse.json(
        { 
          message: "Password does not meet security requirements",
          errors: { password: passwordValidation.errors },
          warnings: passwordValidation.warnings,
          suggestions: passwordValidation.suggestions,
          strength: {
            score: passwordValidation.score,
            level: passwordValidation.strength
          }
        },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        role: true,
        createdAt: true,
      }
    })

    if (existingUser) {
      // Provide helpful error message based on user status
      if (!existingUser.emailVerified) {
        return NextResponse.json(
          { 
            message: "An account with this email exists but is not verified. Please check your email for the verification code.",
            accountExists: true,
            needsVerification: true,
            email: existingUser.email
          },
          { status: 409 } // Conflict
        )
      } else {
        // Account exists and is verified
        const accountType = existingUser.role === "CANDIDATE" ? "candidate" : "business"
        return NextResponse.json(
          { 
            message: `An account with this email already exists. Please sign in instead.`,
            accountExists: true,
            accountType,
            signInUrl: existingUser.role === "CANDIDATE" 
              ? "/auth/candidate/signin" 
              : "/auth/business/signin"
          },
          { status: 409 } // Conflict
        )
      }
    }

    // Create user
    const user = await createCandidateUser(validatedData)

    // Generate verification code and send email
    try {
      const verificationCode = await createVerificationCode(user.email)
      await sendEmailVerification(user.email, verificationCode, user.name || undefined)
    } catch (error) {
      console.error("Failed to send verification email:", error)
      // Don't fail the signup if email fails
    }

    return NextResponse.json(
      { 
        message: "Account created successfully! Please check your email for a verification code.",
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        },
        requiresVerification: true
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Validation failed", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Candidate signup error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export const POST = withCSRFProtection(handleSignup)