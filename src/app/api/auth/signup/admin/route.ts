import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { AdminSecurityManager } from "@/lib/admin-security"

// Secure admin signup schema (no more plaintext admin key)
const secureAdminSignupSchema = z.object({
  invitationToken: z.string().min(1, "Invitation token is required"),
  password: z.string().min(12, "Admin password must be at least 12 characters"),
  confirmPassword: z.string().min(1, "Password confirmation is required"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = secureAdminSignupSchema.parse(body)
    
    // Security context
    const securityContext = {
      ipAddress: request.headers.get("x-forwarded-for") || 
                 request.headers.get("x-real-ip") || 
                 "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
    }
    
    // Create admin user with secure invitation system
    const result = await AdminSecurityManager.createAdminWithInvitation(
      validatedData.invitationToken,
      {
        password: validatedData.password,
        confirmPassword: validatedData.confirmPassword,
      },
      securityContext
    )
    
    if (!result.success) {
      return NextResponse.json(
        { message: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        message: "Super admin account created successfully",
        userId: result.user?.id,
      },
      { status: 201 }
    )
    
  } catch (error) {
    console.error("Secure admin signup error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          message: "Validation error",
          errors: error.errors.map(e => e.message)
        },
        { status: 400 }
      )
    }
    
    if (error instanceof Error) {
      return NextResponse.json(
        { message: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}