import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyEmail2FA } from "@/lib/core/two-factor"
import { createEmail2FASession } from "@/lib/security/authentication/two-factor-session"
import { withCSRFProtection } from "@/lib/security/protection/csrf-middleware"
import { check2FARateLimit, get2FARateLimitIdentifier } from "@/lib/two-factor-rate-limiter"
import { AuditLogger } from "@/lib/security/monitoring/audit"

const verifyEmail2FASchema = z.object({
  userId: z.string(),
  code: z.string().length(6, "Verification code must be 6 digits"),
})

async function handleVerify2FA(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, code } = verifyEmail2FASchema.parse(body)

    // Get request context
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined
    const userAgent = request.headers.get('user-agent') || undefined
    
    // Check rate limit
    const identifier = get2FARateLimitIdentifier(userId, undefined, ipAddress)
    const rateLimit = await check2FARateLimit("emailVerify", identifier, false, {
      userId,
      ipAddress,
      userAgent,
    })

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          message: "Too many verification attempts. Please try again later.",
          retryAfter: rateLimit.retryAfter
        },
        { status: 429 }
      )
    }

    // Verify the 2FA code
    const result = await verifyEmail2FA(userId, code)

    if (!result.valid) {
      // Log failed attempt
      const auditor = new AuditLogger({
        userId,
        ipAddress,
        userAgent,
      })
      await auditor.log({
        action: "TWO_FACTOR_EMAIL_FAILED",
        resourceType: "AUTHENTICATION",
        resourceId: userId,
        metadata: { error: result.error, severity: "medium" },
      })

      return NextResponse.json(
        { message: result.error || "Invalid verification code" },
        { status: 400 }
      )
    }

    // Create a secure session token for this verification
    const session = await createEmail2FASession(userId, ipAddress, userAgent)

    // Log successful verification
    const auditor = new AuditLogger({
      userId,
      ipAddress,
      userAgent,
    })
    await auditor.log({
      action: "TWO_FACTOR_EMAIL_VERIFIED",
      resourceType: "AUTHENTICATION",
      resourceId: userId,
      metadata: { sessionId: session.sessionId, severity: "low" },
    })

    // Update rate limit for successful request
    await check2FARateLimit("emailVerify", identifier, true)

    return NextResponse.json({
      message: "2FA verification successful!",
      valid: true,
      sessionId: session.sessionId,
      sessionToken: session.sessionToken
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Invalid input", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Verify email 2FA error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export const POST = withCSRFProtection(handleVerify2FA)