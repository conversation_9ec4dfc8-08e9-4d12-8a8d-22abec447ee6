import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { sendEmail2FA, canResendEmail2FA } from "@/lib/core/two-factor"
import { withCSRFProtection } from "@/lib/security/protection/csrf-middleware"
import { check2FARateLimit, get2FARateLimitIdentifier } from "@/lib/two-factor-rate-limiter"
import { AuditLogger } from "@/lib/security/monitoring/audit"

const sendEmail2FASchema = z.object({
  userId: z.string(),
  deviceInfo: z.string().optional(),
})

async function handleSendEmail2FA(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, deviceInfo } = sendEmail2FASchema.parse(body)

    // Get request context for rate limiting and audit
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined
    const userAgent = request.headers.get('user-agent') || undefined
    
    // Check rate limit
    const identifier = get2FARateLimitIdentifier(userId, undefined, ipAddress)
    const rateLimit = await check2FARateLimit("emailSend", identifier, false, {
      userId,
      ipAddress,
      userAgent,
    })

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          message: "Too many requests. Please try again later.",
          retryAfter: rateLimit.retryAfter
        },
        { status: 429 }
      )
    }

    // Check if we can send (built-in cooldown)
    const resendCheck = await canResendEmail2FA(userId)
    if (!resendCheck.canResend) {
      return NextResponse.json(
        { 
          message: `Please wait ${resendCheck.waitMinutes} minute(s) before requesting another code`,
          waitMinutes: resendCheck.waitMinutes
        },
        { status: 429 } // Too Many Requests
      )
    }

    // Send email 2FA code
    const result = await sendEmail2FA(userId, deviceInfo)

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || "Failed to send 2FA code" },
        { status: 500 }
      )
    }

    // Log successful 2FA email send
    const auditor = new AuditLogger({
      userId,
      ipAddress,
      userAgent,
    })
    await auditor.log({
      action: "TWO_FACTOR_EMAIL_SENT",
      resourceType: "AUTHENTICATION",
      resourceId: userId,
      metadata: { deviceInfo, severity: "low" },
    })

    // Update rate limit for successful request
    await check2FARateLimit("emailSend", identifier, true)

    return NextResponse.json({
      message: "2FA code sent to your email successfully!"
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Invalid input", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Send email 2FA error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export const POST = withCSRFProtection(handleSendEmail2FA)