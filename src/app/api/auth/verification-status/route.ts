import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { EmailVerificationSecurityManager } from "@/lib/email-verification-security"
import { db } from "@/lib/db"

const verificationStatusSchema = z.object({
  email: z.string().email(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = verificationStatusSchema.parse(body)

    // Check if user exists
    const user = await db.user.findUnique({
      where: { email },
      select: { emailVerified: true }
    })

    if (!user) {
      return NextResponse.json(
        { message: "No account found with this email address" },
        { status: 404 }
      )
    }

    // If email is already verified, return success
    if (user.emailVerified) {
      return NextResponse.json({
        verified: true,
        message: "Email is already verified"
      })
    }

    // Get verification status from security manager
    const status = await EmailVerificationSecurityManager.getVerificationStatus(email)

    return NextResponse.json({
      verified: false,
      hasActiveToken: status.hasActiveToken,
      isLocked: status.isLocked,
      expiresAt: status.expiresAt?.toISOString(),
      attemptsRemaining: status.attemptsRemaining,
      lockoutMinutes: status.lockoutMinutes,
      message: status.isLocked 
        ? `Account locked due to too many failed attempts. Please wait ${status.lockoutMinutes} minutes.`
        : status.hasActiveToken 
          ? `Verification code active. ${status.attemptsRemaining} attempts remaining.`
          : "No active verification code. Please request a new one."
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Invalid input", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Verification status error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}