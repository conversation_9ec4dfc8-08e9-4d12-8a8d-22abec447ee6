import { NextRequest, NextResponse } from "next/server"
import { OAuthSecurityManager } from "@/lib/oauth-security"
import { google } from "googleapis"
import { env } from "@/lib/env"

const oauth2Client = new google.auth.OAuth2(
  env.GOOGLE_CLIENT_ID,
  env.GOOGLE_CLIENT_SECRET,
  `${env.NEXTAUTH_URL}/api/auth/oauth/google/callback`
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get("action") // "signin" or "link"
    
    // Generate OAuth URL
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: ["email", "profile"],
      state: JSON.stringify({ action: action || "signin" }),
      prompt: "select_account" // Force account selection for security
    })
    
    return NextResponse.redirect(authUrl)
    
  } catch (error) {
    console.error("OAuth initiation error:", error)
    return NextResponse.redirect(
      `${env.NEXTAUTH_URL}/auth/error?error=oauth_error`
    )
  }
}