import { NextRequest, NextResponse } from "next/server"
import { OAuthSecurityManager } from "@/lib/oauth-security"
import { google } from "googleapis"
import { env } from "@/lib/env"
import { signIn } from "next-auth/react"

const oauth2Client = new google.auth.OAuth2(
  env.GOOGLE_CLIENT_ID,
  env.GOOGLE_CLIENT_SECRET,
  `${env.NEXTAUTH_URL}/api/auth/oauth/google/callback`
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get("code")
    const state = searchParams.get("state")
    const error = searchParams.get("error")
    
    if (error) {
      return NextResponse.redirect(
        `${env.NEXTAUTH_URL}/auth/error?error=oauth_cancelled`
      )
    }
    
    if (!code) {
      return NextResponse.redirect(
        `${env.NEXTAUTH_URL}/auth/error?error=oauth_no_code`
      )
    }
    
    // Parse state
    let stateData = { action: "signin" }
    try {
      stateData = JSON.parse(state || "{}")
    } catch (e) {
      console.warn("Invalid OAuth state:", state)
    }
    
    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code)
    oauth2Client.setCredentials(tokens)
    
    // Get user info
    const oauth2 = google.oauth2({ version: "v2", auth: oauth2Client })
    const { data: userInfo } = await oauth2.userinfo.get()
    
    if (!userInfo.email) {
      return NextResponse.redirect(
        `${env.NEXTAUTH_URL}/auth/error?error=oauth_no_email`
      )
    }
    
    // Security context
    const securityContext = {
      ipAddress: request.headers.get("x-forwarded-for") || 
                 request.headers.get("x-real-ip") || 
                 "unknown",
      userAgent: request.headers.get("user-agent") || "unknown"
    }
    
    // Use secure OAuth manager
    const oauthAccount = {
      provider: "google",
      providerAccountId: userInfo.id!,
      email: userInfo.email,
      name: userInfo.name || undefined,
      image: userInfo.picture || undefined
    }
    
    const result = await OAuthSecurityManager.handleOAuthSignIn(
      oauthAccount,
      securityContext
    )
    
    if (result.success) {
      // For successful sign-ins, we need to create a NextAuth session
      // This is a simplified approach - in production you'd want to 
      // create a proper session token
      const redirectUrl = `/auth/oauth-success?userId=${result.user?.id}&action=${result.action}`
      return NextResponse.redirect(`${env.NEXTAUTH_URL}${redirectUrl}`)
    } else {
      // Verification required
      if (result.action === "verification_required") {
        const redirectUrl = `/auth/oauth-verification?token=${result.verificationToken}`
        return NextResponse.redirect(`${env.NEXTAUTH_URL}${redirectUrl}`)
      }
      
      return NextResponse.redirect(
        `${env.NEXTAUTH_URL}/auth/error?error=oauth_failed&message=${encodeURIComponent(result.message || "OAuth failed")}`
      )
    }
    
  } catch (error) {
    console.error("OAuth callback error:", error)
    return NextResponse.redirect(
      `${env.NEXTAUTH_URL}/auth/error?error=oauth_callback_error`
    )
  }
}