import { NextRequest, NextResponse } from "next/server"
import { OAuthSecurityManager } from "@/lib/oauth-security"

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json()
    
    if (!token) {
      return NextResponse.json(
        { message: "Verification token is required" },
        { status: 400 }
      )
    }
    
    // Get security context
    const securityContext = {
      ipAddress: request.headers.get("x-forwarded-for") || 
                 request.headers.get("x-real-ip") || 
                 "unknown",
      userAgent: request.headers.get("user-agent") || "unknown"
    }
    
    const result = await OAuthSecurityManager.verifyAccountLink(token, securityContext)
    
    if (!result.success) {
      return NextResponse.json(
        { message: result.error },
        { status: 400 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: result.message,
      user: result.user ? {
        id: result.user.id,
        email: result.user.email,
        name: result.user.name
      } : null
    })
    
  } catch (error) {
    console.error("Account link verification error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}