import { NextRequest, NextResponse } from "next/server"
import { OAuthSecurityManager } from "@/lib/oauth-security"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get("token")
    
    if (!token) {
      return NextResponse.json(
        { message: "Verification token is required" },
        { status: 400 }
      )
    }
    
    const linkInfo = await OAuthSecurityManager.getPendingLinkInfo(token)
    
    if (!linkInfo) {
      return NextResponse.json(
        { message: "Invalid or expired verification token" },
        { status: 404 }
      )
    }
    
    return NextResponse.json(linkInfo)
    
  } catch (error) {
    console.error("Link info fetch error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}