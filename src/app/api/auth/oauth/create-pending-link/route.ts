import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { OAuthSecurityManager } from "@/lib/oauth-security"
import { db } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.needsVerification) {
      return NextResponse.json(
        { message: "No verification required" },
        { status: 400 }
      )
    }
    
    const { provider, email } = await request.json()
    
    if (!provider || !email) {
      return NextResponse.json(
        { message: "Provider and email are required" },
        { status: 400 }
      )
    }
    
    // Find the existing user
    const existingUser = await db.user.findUnique({
      where: { email }
    })
    
    if (!existingUser) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      )
    }
    
    // Security context
    const securityContext = {
      ipAddress: request.headers.get("x-forwarded-for") || 
                 request.headers.get("x-real-ip") || 
                 "unknown",
      userAgent: request.headers.get("user-agent") || "unknown"
    }
    
    // Validate required fields
    if (!session.oauthProvider || !session.oauthEmail) {
      return NextResponse.json(
        { message: "Missing OAuth information" },
        { status: 400 }
      )
    }

    // Create OAuth account info (we'll get this from the session in a real implementation)
    const oauthAccount = {
      provider: session.oauthProvider,
      providerAccountId: "temp_" + Date.now(), // This should come from OAuth
      email: session.oauthEmail,
      name: session.user?.name || undefined,
      image: session.user?.image || undefined
    }
    
    // Use the OAuth security manager to handle this
    const result = await OAuthSecurityManager.handleOAuthSignIn(
      oauthAccount,
      securityContext
    )
    
    if (result.action === "verification_required") {
      return NextResponse.json({
        success: true,
        message: "Verification email sent successfully",
        verificationToken: result.verificationToken
      })
    }
    
    return NextResponse.json(
      { message: "Unexpected result from OAuth security manager" },
      { status: 500 }
    )
    
  } catch (error) {
    console.error("Create pending link error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}