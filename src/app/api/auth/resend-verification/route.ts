import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { EmailVerificationSecurityManager } from "@/lib/security/utilities/email-verification-security"
import { EmailVerificationRateLimiter } from "@/lib/email-verification-rate-limiter"
import { sendEmailVerification } from "@/lib/core/email"
import { db } from "@/lib/core/db"

const resendVerificationSchema = z.object({
  email: z.string().email(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = resendVerificationSchema.parse(body)

    // Apply rate limiting first
    const rateLimitResult = await EmailVerificationRateLimiter.applyVerificationRateLimit(
      request,
      email
    )

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { message: rateLimitResult.error },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      )
    }

    // Check if user exists
    const user = await db.user.findUnique({
      where: { email },
      select: { id: true, name: true, emailVerified: true }
    })

    if (!user) {
      return NextResponse.json(
        { message: "No account found with this email address" },
        { status: 404, headers: rateLimitResult.headers }
      )
    }

    // Check if email is already verified
    if (user.emailVerified) {
      return NextResponse.json(
        { message: "Email is already verified" },
        { status: 400, headers: rateLimitResult.headers }
      )
    }

    // Create secure verification context
    const context = {
      ipAddress: request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      userAgent: request.headers.get('user-agent') || undefined,
      userId: user.id
    }

    // Check for suspicious activity
    const suspiciousActivity = await EmailVerificationSecurityManager.detectSuspiciousActivity(
      email,
      context
    )

    if (suspiciousActivity.suspicious) {
      // Log but don't block - just add security headers
      rateLimitResult.headers['X-Security-Warning'] = suspiciousActivity.reasons.join(',')
    }

    // Create secure verification token
    const tokenResult = await EmailVerificationSecurityManager.createVerificationToken(
      email,
      context
    )

    if (!tokenResult.success) {
      return NextResponse.json(
        { 
          message: tokenResult.error,
          waitMinutes: tokenResult.waitMinutes
        },
        { 
          status: tokenResult.waitMinutes ? 429 : 500,
          headers: rateLimitResult.headers
        }
      )
    }

    // Send verification email with secure token
    const emailSent = await sendEmailVerification(
      email, 
      tokenResult.token!, 
      user.name || undefined
    )

    if (!emailSent) {
      return NextResponse.json(
        { message: "Failed to send verification email. Please try again." },
        { status: 500, headers: rateLimitResult.headers }
      )
    }

    return NextResponse.json(
      {
        message: "Verification code sent successfully! Check your email.",
        expiresInMinutes: 15
      },
      { headers: rateLimitResult.headers }
    )

  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Invalid input", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Resend verification error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}