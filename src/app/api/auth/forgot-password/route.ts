import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

import { 
  forgotPasswordSchema, 
  generatePasswordResetToken
} from "@/lib/auth-utils"
import { sendPasswordResetEmail } from "@/lib/core/email"
import { withCSRFProtection } from "@/lib/security/protection/csrf-middleware"

async function handleForgotPassword(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = forgotPasswordSchema.parse(body)

    try {
      // Generate reset token
      const resetData = await generatePasswordResetToken(validatedData.email)
      
      // Send password reset email using our new email system
      await sendPasswordResetEmail(
        resetData.user.email,
        resetData.token
      )

      // Always return success to prevent email enumeration attacks
      return NextResponse.json(
        { 
          message: "If an account with that email exists, we've sent a password reset link.",
          success: true
        },
        { status: 200 }
      )
    } catch (error) {
      // Don't reveal if user exists or not for security
      console.error("Password reset error:", error)
      console.error("Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        email: validatedData.email, // Safe to log email for debugging
      })
      
      return NextResponse.json(
        { 
          message: "If an account with that email exists, we've sent a password reset link.",
          success: true
        },
        { status: 200 }
      )
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Validation failed", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Forgot password API error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export const POST = withCSRFProtection(handleForgotPassword)