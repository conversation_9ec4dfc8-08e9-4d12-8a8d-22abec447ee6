import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { EmailVerificationSecurityManager } from "@/lib/security/utilities/email-verification-security"
import { EmailVerificationRateLimiter } from "@/lib/email-verification-rate-limiter"
import { sendWelcomeEmail } from "@/lib/core/email"
import { db } from "@/lib/core/db"

const verifyEmailSchema = z.object({
  email: z.string().email(),
  code: z.string().length(6, "Verification code must be 6 digits"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, code } = verifyEmailSchema.parse(body)

    // Apply rate limiting for verification attempts
    const rateLimitResult = await EmailVerificationRateLimiter.applyVerificationAttemptLimit(
      request,
      email
    )

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { message: rateLimitResult.error },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      )
    }

    // Get user info for context
    const user = await db.user.findUnique({
      where: { email },
      select: { id: true, name: true, emailVerified: true }
    })

    if (!user) {
      return NextResponse.json(
        { message: "No account found with this email address" },
        { status: 404, headers: rateLimitResult.headers }
      )
    }

    // Check if email is already verified
    if (user.emailVerified) {
      return NextResponse.json(
        { message: "Email is already verified" },
        { status: 400, headers: rateLimitResult.headers }
      )
    }

    // Create verification context
    const context = {
      ipAddress: request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      userAgent: request.headers.get('user-agent') || undefined,
      userId: user.id
    }

    // Verify the token using secure verification
    const verificationResult = await EmailVerificationSecurityManager.verifyToken(
      email,
      code,
      context
    )

    if (!verificationResult.success) {
      const statusCode = verificationResult.requiresNewToken ? 410 : 400 // 410 Gone for expired tokens
      
      return NextResponse.json(
        { 
          message: verificationResult.error,
          remainingAttempts: verificationResult.remainingAttempts,
          lockoutMinutes: verificationResult.lockoutMinutes,
          requiresNewToken: verificationResult.requiresNewToken
        },
        { 
          status: statusCode,
          headers: rateLimitResult.headers 
        }
      )
    }

    // Send welcome email after successful verification
    try {
      await sendWelcomeEmail(email, user.name || "there")
    } catch (error) {
      console.log("Welcome email failed to send, but verification was successful:", error)
      // Don't fail the verification if welcome email fails
    }

    return NextResponse.json(
      {
        message: "Email verified successfully! Welcome to Sourceflex.",
        verified: true
      },
      { headers: rateLimitResult.headers }
    )

  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {}
      error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as string] = err.message
        }
      })
      
      return NextResponse.json(
        { message: "Invalid input", errors: fieldErrors },
        { status: 400 }
      )
    }

    console.error("Email verification error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}