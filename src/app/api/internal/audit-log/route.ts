import { NextRequest, NextResponse } from "next/server";
import { create<PERSON><PERSON>tLog, type AuditContext, type AuditLogData } from "@/lib/security/monitoring/audit";

/**
 * Internal API endpoint for processing audit logs from edge runtime
 * This runs in Node.js runtime and can use Prisma Client
 */
export async function POST(request: NextRequest) {
  try {
    // Verify this is an internal request
    const internalHeader = request.headers.get("X-Internal-Request");
    if (internalHeader !== "audit-log") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { context, data } = await request.json();

    // Validate required fields
    if (!data.action || !data.resourceType) {
      return NextResponse.json(
        { error: "Invalid audit log data" },
        { status: 400 }
      );
    }

    // Convert edge audit data to standard audit log format
    const auditContext: AuditContext = {
      userId: context.userId,
      userEmail: context.userEmail,
      userName: context.userName,
      organizationId: context.organizationId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      sessionId: context.sessionId,
    };

    const auditData: AuditLogData = {
      action: data.action,
      resourceType: data.resourceType,
      resourceId: data.resourceId,
      oldValues: data.oldValues,
      newValues: data.newValues,
      metadata: {
        ...data.metadata,
        severity: data.severity,
        edgeProcessed: true,
        originalTimestamp: data.timestamp,
      },
      success: data.success,
      errorMessage: data.errorMessage,
      details: data.details,
    };

    // Create the audit log using the standard function
    await createAuditLog(auditContext, auditData);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to process audit log:", error);
    // Return success to prevent edge runtime from retrying
    // Log the error internally but don't fail the request
    return NextResponse.json({ success: true });
  }
}