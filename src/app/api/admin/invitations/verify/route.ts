import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { AdminSecurityManager } from "@/lib/admin-security"

const verifyInvitationSchema = z.object({
  invitationToken: z.string().min(1, "Invitation token is required"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = verifyInvitationSchema.parse(body)
    
    // Security context
    const securityContext = {
      ipAddress: request.headers.get("x-forwarded-for") || 
                 request.headers.get("x-real-ip") || 
                 "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
    }
    
    // Verify invitation
    const result = await AdminSecurityManager.verifyAdminInvitation(
      validatedData.invitationToken,
      securityContext
    )
    
    if (!result.valid) {
      return NextResponse.json(
        { message: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        valid: true,
        invitation: result.invitation,
      },
      { status: 200 }
    )
    
  } catch (error) {
    console.error("Admin invitation verification error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          message: "Validation error",
          errors: error.errors.map(e => e.message)
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}