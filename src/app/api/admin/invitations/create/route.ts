import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { AdminSecurityManager } from "@/lib/admin-security"
import { auth } from "@/lib/auth"

const createInvitationSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(2, "Name must be at least 2 characters"),
  masterKey: z.string().optional(), // For initial setup only
})

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    const body = await request.json()
    
    // Validate input
    const validatedData = createInvitationSchema.parse(body)
    
    // Security context
    const securityContext = {
      ipAddress: request.headers.get("x-forwarded-for") || 
                 request.headers.get("x-real-ip") || 
                 "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
      sessionId: session?.user?.id,
    }
    
    // Determine who is creating the invitation
    let invitedBy = "system"
    if (session?.user) {
      // Verify existing admin is making the request
      if (session.user.role !== "SUPER_ADMIN") {
        return NextResponse.json(
          { message: "Only super admins can create admin invitations" },
          { status: 403 }
        )
      }
      invitedBy = session.user.email || session.user.id
    } else if (!validatedData.masterKey) {
      return NextResponse.json(
        { message: "Master key required for initial admin setup" },
        { status: 400 }
      )
    }
    
    // Create secure invitation
    const result = await AdminSecurityManager.createAdminInvitation(
      {
        email: validatedData.email,
        name: validatedData.name,
        invitedBy,
      },
      securityContext,
      validatedData.masterKey
    )
    
    if (!result.success) {
      return NextResponse.json(
        { message: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        message: "Admin invitation created successfully",
        invitationId: result.invitationToken?.split(':')[0], // Return only ID for reference
      },
      { status: 201 }
    )
    
  } catch (error) {
    console.error("Admin invitation creation error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          message: "Validation error",
          errors: error.errors.map(e => e.message)
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}