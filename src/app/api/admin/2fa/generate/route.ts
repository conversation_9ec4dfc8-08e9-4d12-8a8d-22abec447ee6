import { NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth-utils"
import { generateTOTPSecret, generateQRCode } from "@/lib/two-factor"

export async function POST() {
  try {
    const user = await requireAdmin()
    
    // Generate TOTP secret and QR code
    const { secret, uri } = generateTOTPSecret(user.email || "<EMAIL>")
    const qrCode = await generateQRCode(uri)

    return NextResponse.json({
      secret,
      qrCode,
    })
  } catch (error) {
    console.error("Error generating 2FA setup:", error)
    return NextResponse.json(
      { message: "Failed to generate 2FA setup" },
      { status: 500 }
    )
  }
}