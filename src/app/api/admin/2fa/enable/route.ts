import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { requireAdmin } from "@/lib/auth-utils"
import { verifyTOTP, generateBackupCodes, enableTwoFactor } from "@/lib/two-factor"

const enableTwoFactorSchema = z.object({
  secret: z.string().min(1),
  code: z.string().min(6).max(6),
})

export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin()
    const body = await request.json()
    const validatedData = enableTwoFactorSchema.parse(body)

    // Verify the TOTP code
    const isValidCode = verifyTOTP(validatedData.secret, validatedData.code)
    if (!isValidCode) {
      return NextResponse.json(
        { message: "Invalid verification code. Please try again." },
        { status: 400 }
      )
    }

    // Generate backup codes
    const backupCodes = generateBackupCodes(10)

    // Enable 2FA for the user
    await enableTwoFactor(user.id, validatedData.secret, backupCodes)

    return NextResponse.json({
      message: "Two-factor authentication enabled successfully",
      backupCodes,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input", errors: error.errors },
        { status: 400 }
      )
    }

    console.error("Error enabling 2FA:", error)
    return NextResponse.json(
      { message: "Failed to enable two-factor authentication" },
      { status: 500 }
    )
  }
}