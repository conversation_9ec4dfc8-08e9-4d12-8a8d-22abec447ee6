import { NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth-utils"
import { getUserTwoFactorStatus } from "@/lib/two-factor"

export async function GET() {
  try {
    const user = await requireAdmin()
    const status = await getUserTwoFactorStatus(user.id)

    return NextResponse.json(status)
  } catch (error) {
    console.error("Error fetching 2FA status:", error)
    return NextResponse.json(
      { message: "Unauthorized" },
      { status: 401 }
    )
  }
}