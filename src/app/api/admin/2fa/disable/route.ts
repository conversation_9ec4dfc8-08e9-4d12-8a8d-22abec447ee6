import { NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth-utils"
import { disableTwoFactor } from "@/lib/two-factor"

export async function POST() {
  try {
    const user = await requireAdmin()
    
    // Disable 2FA for the user
    await disableTwoFactor(user.id)

    return NextResponse.json({
      message: "Two-factor authentication disabled successfully",
    })
  } catch (error) {
    console.error("Error disabling 2FA:", error)
    return NextResponse.json(
      { message: "Failed to disable two-factor authentication" },
      { status: 500 }
    )
  }
}