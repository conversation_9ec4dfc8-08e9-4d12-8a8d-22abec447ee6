import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getToken } from "next-auth/jwt"
import { env } from "@/lib/core/env"
import { applyEdgeRateLimit } from "@/lib/edge-rate-limiter"
import { CSRFSecurityManager } from "@/lib/security/protection/csrf-security"
import { validateSessionSecurity } from "@/lib/security/protection/session-middleware"
import { createPublicRateLimit, createAPIRateLimit, createAuthRateLimit } from "@/lib/security/protection/rate-limit-middleware"

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  // const clientIp = getClientIP(request) // Available for future use

  // Apply CSRF protection for API routes (excluding NextAuth endpoints which have built-in CSRF)
  if (pathname.startsWith("/api") && !pathname.startsWith("/api/auth/")) {
    if (["POST", "PUT", "DELETE", "PATCH"].includes(request.method)) {
      // Validate CSRF for state-changing operations
      const allowedOrigins = [env.NEXTAUTH_URL]
      if (!CSRFSecurityManager.isOriginAllowed(request, allowedOrigins)) {
        return new NextResponse(
          JSON.stringify({ error: "Forbidden: Invalid origin" }),
          { 
            status: 403,
            headers: { "Content-Type": "application/json" }
          }
        )
      }
    }
  }

  // Apply enhanced rate limiting based on route type
  let rateLimitResult
  if (pathname.startsWith("/api/auth")) {
    const authRateLimit = createAuthRateLimit()
    rateLimitResult = await authRateLimit.applyRateLimit(request, pathname)
  } else if (pathname.startsWith("/api")) {
    const apiRateLimit = createAPIRateLimit()  
    rateLimitResult = await apiRateLimit.applyRateLimit(request, pathname)
  } else if (pathname.startsWith("/admin") || pathname.startsWith("/business") || pathname.startsWith("/candidate")) {
    // Skip rate limiting for authenticated dashboard routes - they're protected by auth instead
    rateLimitResult = null
  } else if (pathname.startsWith("/")) {
    const publicRateLimit = createPublicRateLimit()
    rateLimitResult = await publicRateLimit.applyRateLimit(request, pathname)
  }

  // Return rate limit response if exceeded
  if (rateLimitResult && !rateLimitResult.allowed) {
    return new NextResponse(
      JSON.stringify(rateLimitResult.body || {
        error: "Rate limit exceeded",
        retryAfter: rateLimitResult.headers['Retry-After']
      }),
      {
        status: rateLimitResult.status || 429,
        headers: {
          "Content-Type": "application/json",
          ...rateLimitResult.headers
        },
      }
    )
  }

  const token = await getToken({ 
    req: request,
    secret: env.NEXTAUTH_SECRET
  })

  // Note: Session security validation with database audit logging
  // is handled at the API route level to avoid Edge Runtime Prisma issues
  // Edge middleware focuses on basic authentication and authorization

  // Note: IP restrictions are handled in API routes that have database access
  // Edge middleware can't access the database directly

  // Public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/jobs",
    "/about",
    "/contact",
    "/auth",
    "/api/auth",
    "/api/auth/signup",
    "/api/auth/forgot-password",
    "/api/auth/reset-password",
    "/api/auth/admin/verify-credentials",
    "/api/auth/admin/verify-2fa",
  ]
  
  const isPublicRoute = publicRoutes.some(route => 
    pathname.startsWith(route) || pathname === route
  )
  
  // Allow public routes
  if (isPublicRoute) {
    return NextResponse.next()
  }

  // Admin routes protection
  if (pathname.startsWith("/admin")) {
    if (!token || token.role !== "SUPER_ADMIN") {
      return NextResponse.redirect(new URL("/auth/admin/signin", request.url))
    }
    return NextResponse.next()
  }

  // Business routes protection
  if (pathname.startsWith("/business") || pathname.startsWith("/dashboard")) {
    if (!token) {
      return NextResponse.redirect(new URL("/auth/business/signin", request.url))
    }
    
    const businessRoles = ["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP", "SUPER_ADMIN"]
    if (!businessRoles.includes(token.role as string)) {
      return NextResponse.redirect(new URL("/unauthorized", request.url))
    }
    return NextResponse.next()
  }

  // Candidate routes protection
  if (pathname.startsWith("/candidate")) {
    if (!token) {
      return NextResponse.redirect(new URL("/auth/candidate/signin", request.url))
    }
    
    if (token.role !== "CANDIDATE" && token.role !== "SUPER_ADMIN") {
      return NextResponse.redirect(new URL("/unauthorized", request.url))
    }
    return NextResponse.next()
  }

  // Role-based redirects after authentication
  if (pathname === "/dashboard" && token) {
    switch (token.role) {
      case "SUPER_ADMIN":
        return NextResponse.redirect(new URL("/admin/dashboard", request.url))
      case "CANDIDATE":
        return NextResponse.redirect(new URL("/candidate/dashboard", request.url))
      case "ORG_ADMIN":
      case "HIRING_MANAGER":
      case "RECRUITER":
      case "SALES_REP":
        return NextResponse.redirect(new URL("/business/dashboard", request.url))
      default:
        return NextResponse.redirect(new URL("/", request.url))
    }
  }

  // For all other protected routes, require authentication
  if (!token) {
    return NextResponse.redirect(new URL("/auth/candidate/signin", request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public).*)",
  ],
}

/**
 * Get client IP address from request headers
 */
function getClientIP(request: NextRequest): string {
  const forwardedFor = request.headers.get("x-forwarded-for")
  const realIp = request.headers.get("x-real-ip")
  const cfConnectingIp = request.headers.get("cf-connecting-ip")
  
  return forwardedFor?.split(",")[0]?.trim() || 
         realIp || 
         cfConnectingIp || 
         "unknown"
}