"use client"

import { signIn } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"

interface OAuthButtonProps {
  provider: "google" | "linkedin"
  children: React.ReactNode
  callbackUrl?: string
  disabled?: boolean
}

export function OAuthButton({
  provider,
  children,
  callbackUrl = "/dashboard",
  disabled = false,
}: OAuthButtonProps) {
  const handleSignIn = async () => {
    try {
      await signIn(provider, { callbackUrl })
    } catch (error) {
      console.error(`Error signing in with ${provider}:`, error)
    }
  }

  return (
    <Button
      type="button"
      variant="outline"
      className="w-full"
      onClick={handleSignIn}
      disabled={disabled}
    >
      {children}
    </Button>
  )
}