"use client"

import { useState } from "react"
import { signOut } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { LogOut } from "lucide-react"
import { api } from "@/lib/core/trpc"

interface SignOutButtonProps {
  className?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  children?: React.ReactNode
}

export function SignOutButton({ 
  className = "text-gray-600 hover:text-gray-900",
  variant,
  size,
  children 
}: SignOutButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const logLogout = api.audit.logLogout.useMutation()

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      // Log the logout action first
      await logLogout.mutateAsync()
      
      // Then perform the actual sign out
      await signOut({
        callbackUrl: "/",
        redirect: true,
      })
    } catch (error) {
      console.error("Sign out error:", error)
      setIsLoading(false)
    }
  }

  // If variant or size is provided, render as Button component
  if (variant || size) {
    return (
      <Button
        variant={variant}
        size={size}
        onClick={handleSignOut}
        disabled={isLoading}
        className={className}
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
            Signing out...
          </>
        ) : (
          children || (
            <>
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </>
          )
        )}
      </Button>
    )
  }

  // Default simple button
  return (
    <button
      onClick={handleSignOut}
      disabled={isLoading}
      className={className}
    >
      {isLoading ? "Signing out..." : children || "Sign Out"}
    </button>
  )
}