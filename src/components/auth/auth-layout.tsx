import Link from "next/link"
import { ArrowLeft } from "lucide-react"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle?: string
  showBackLink?: boolean
  backHref?: string
  type?: "candidate" | "business" | "admin"
}

export function AuthLayout({
  children,
  title,
  subtitle,
  showBackLink = true,
  backHref = "/",
  type = "candidate",
}: AuthLayoutProps) {
  const getThemeClasses = () => {
    switch (type) {
      case "business":
        return "bg-slate-50 border-slate-200"
      case "admin":
        return "bg-red-50 border-red-200"
      default:
        return "bg-blue-50 border-blue-200"
    }
  }

  const getLogoText = () => {
    switch (type) {
      case "business":
        return "Sourceflex Business"
      case "admin":
        return "Sourceflex Admin"
      default:
        return "Sourceflex"
    }
  }

  return (
    <div className={`min-h-screen ${getThemeClasses()}`}>
      <div className="container mx-auto px-4 py-8">
        {showBackLink && (
          <Link
            href={backHref}
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to {type === "business" ? "Business" : "Home"}
          </Link>
        )}

        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <Link href="/" className="inline-block">
              <h1 className="text-2xl font-bold text-gray-900">
                {getLogoText()}
              </h1>
            </Link>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-8">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
              {subtitle && (
                <p className="text-sm text-gray-600 mt-2">{subtitle}</p>
              )}
            </div>

            {children}
          </div>

          <div className="mt-8 text-center text-xs text-gray-500">
            <p>
              By continuing, you agree to our{" "}
              <Link href="/terms" className="underline hover:text-gray-700">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link href="/privacy" className="underline hover:text-gray-700">
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}