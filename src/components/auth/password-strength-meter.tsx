"use client"

import { useState, useEffect } from "react"
import { EnhancedPasswordValidator, type EnhancedPasswordValidationResult } from "@/lib/security/validation/enhanced-password-validator"

interface PasswordStrengthMeterProps {
  password: string
  organizationId?: string
  context?: {
    username?: string
    email?: string
    organizationName?: string
    firstName?: string
    lastName?: string
  }
  onValidationChange?: (result: EnhancedPasswordValidationResult) => void
  className?: string
}

export function PasswordStrengthMeter({ 
  password, 
  organizationId,
  context = {},
  onValidationChange,
  className = ""
}: PasswordStrengthMeterProps) {
  const [validation, setValidation] = useState<EnhancedPasswordValidationResult | null>(null)
  const [isChecking, setIsChecking] = useState(false)

  useEffect(() => {
    if (!password) {
      setValidation(null)
      onValidationChange?.(null as any)
      return
    }

    const checkPassword = async () => {
      setIsChecking(true)
      
      try {
        // Get organization-specific policy if available
        let policy = {}
        if (organizationId) {
          policy = await EnhancedPasswordValidator.getOrganizationPasswordPolicy(organizationId)
        }

        const result = await EnhancedPasswordValidator.validatePassword(
          password,
          policy,
          context
        )
        
        setValidation(result)
        onValidationChange?.(result)
      } catch (error) {
        console.error("Password validation error:", error)
      } finally {
        setIsChecking(false)
      }
    }

    // Debounce password checking
    const debounceTimer = setTimeout(checkPassword, 300)
    return () => clearTimeout(debounceTimer)
  }, [password, organizationId, context, onValidationChange])

  if (!password || !validation) {
    return null
  }

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case "weak": return "bg-red-500"
      case "fair": return "bg-orange-500"
      case "good": return "bg-yellow-500"
      case "strong": return "bg-green-500"
      case "very-strong": return "bg-emerald-600"
      default: return "bg-gray-300"
    }
  }

  const getStrengthText = (strength: string) => {
    switch (strength) {
      case "weak": return "Weak"
      case "fair": return "Fair"
      case "good": return "Good"
      case "strong": return "Strong"
      case "very-strong": return "Very Strong"
      default: return "Unknown"
    }
  }

  const getStrengthWidth = (score: number) => {
    return `${Math.min(score, 100)}%`
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Strength Bar */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700">
            Password Strength
          </span>
          <span className={`text-xs font-medium ${validation.isValid ? 'text-green-600' : 'text-red-600'}`}>
            {getStrengthText(validation.strength)} ({validation.score}/100)
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div 
            className={`h-full transition-all duration-300 ${getStrengthColor(validation.strength)} ${isChecking ? 'animate-pulse' : ''}`}
            style={{ width: getStrengthWidth(validation.score) }}
          />
        </div>
      </div>

      {/* Security Indicators */}
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${validation.entropy >= 50 ? 'bg-green-500' : 'bg-red-500'}`} />
          <span>Entropy: {Math.round(validation.entropy)}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${validation.breachInfo && !validation.breachInfo.isBreached ? 'bg-green-500' : 'bg-red-500'}`} />
          <span>Breach Check</span>
        </div>
      </div>

      {/* Crack Time Estimate */}
      <div className="text-xs text-gray-600">
        <span>Estimated crack time: </span>
        <span className="font-medium">{validation.estimatedCrackTime}</span>
      </div>

      {/* Errors */}
      {validation.errors.length > 0 && (
        <div className="space-y-1">
          {validation.errors.map((error, index) => (
            <div key={index} className="flex items-start space-x-1 text-xs text-red-600">
              <span className="text-red-500 mt-0.5">✗</span>
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}

      {/* Warnings */}
      {validation.warnings.length > 0 && (
        <div className="space-y-1">
          {validation.warnings.map((warning, index) => (
            <div key={index} className="flex items-start space-x-1 text-xs text-yellow-600">
              <span className="text-yellow-500 mt-0.5">⚠</span>
              <span>{warning}</span>
            </div>
          ))}
        </div>
      )}

      {/* Suggestions */}
      {validation.suggestions.length > 0 && (
        <div className="space-y-1">
          <div className="text-xs font-medium text-gray-700">Suggestions:</div>
          {validation.suggestions.map((suggestion, index) => (
            <div key={index} className="flex items-start space-x-1 text-xs text-blue-600">
              <span className="text-blue-500 mt-0.5">💡</span>
              <span>{suggestion}</span>
            </div>
          ))}
        </div>
      )}

      {/* Valid password indicator */}
      {validation.isValid && (
        <div className="flex items-center space-x-1 text-xs text-green-600">
          <span className="text-green-500">✓</span>
          <span className="font-medium">Password meets all requirements</span>
        </div>
      )}
    </div>
  )
}

/**
 * Simplified password strength indicator for minimal UI
 */
export function SimplePasswordStrengthIndicator({ 
  password,
  className = ""
}: { 
  password: string
  className?: string 
}) {
  const [strength, setStrength] = useState<"weak" | "fair" | "good" | "strong" | "very-strong">("weak")

  useEffect(() => {
    if (!password) return

    const checkStrength = async () => {
      try {
        const result = await EnhancedPasswordValidator.validatePassword(password, {})
        setStrength(result.strength)
      } catch (error) {
        console.error("Strength check error:", error)
      }
    }

    const debounceTimer = setTimeout(checkStrength, 200)
    return () => clearTimeout(debounceTimer)
  }, [password])

  if (!password) return null

  const getIndicatorColor = () => {
    switch (strength) {
      case "weak": return "border-red-500 bg-red-50"
      case "fair": return "border-orange-500 bg-orange-50"
      case "good": return "border-yellow-500 bg-yellow-50"
      case "strong": return "border-green-500 bg-green-50"
      case "very-strong": return "border-emerald-600 bg-emerald-50"
      default: return "border-gray-300 bg-gray-50"
    }
  }

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium border ${getIndicatorColor()} ${className}`}>
      {strength.charAt(0).toUpperCase() + strength.slice(1).replace('-', ' ')}
    </div>
  )
}