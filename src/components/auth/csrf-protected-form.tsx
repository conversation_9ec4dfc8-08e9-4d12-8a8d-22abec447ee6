"use client"

import { <PERSON><PERSON><PERSON><PERSON>ttributes, PropsWithChildren } from "react"
import { useCSRFToken } from "@/hooks/use-csrf-token"

interface CSRFProtectedFormProps extends FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode
}

export function CSRFProtectedForm({ children, ...props }: CSRFProtectedFormProps) {
  const { csrfToken } = useCSRFToken()

  return (
    <form {...props}>
      {csrfToken && <input type="hidden" name="csrfToken" value={csrfToken} />}
      {children}
    </form>
  )
}