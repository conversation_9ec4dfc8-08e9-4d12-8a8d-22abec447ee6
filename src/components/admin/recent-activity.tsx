"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import { 
  User, 
  Building2, 
  Briefcase, 
  FileText, 
  Shield, 
  Settings,
  UserPlus,
  Trash2,
  Edit,
  Eye,
  AlertCircle,
  Activity
} from "lucide-react";

interface AuditLog {
  id: string;
  action: string;
  resourceType: string;
  resourceId?: string | null;
  success: boolean;
  createdAt: Date;
  user?: {
    name?: string | null;
    email: string;
    role: string;
  };
  userEmail?: string | null;
  userName?: string | null;
}

interface RecentActivityProps {
  activities: AuditLog[];
  isLoading?: boolean;
}

export function RecentActivity({ activities, isLoading }: RecentActivityProps) {
  const getActionIcon = (action: string) => {
    if (action.includes("CREATED")) return UserPlus;
    if (action.includes("UPDATED")) return Edit;
    if (action.includes("DELETED")) return Trash2;
    if (action.includes("LOGIN")) return Shield;
    if (action.includes("SETTINGS")) return Settings;
    if (action.includes("VIEW")) return Eye;
    return AlertCircle;
  };

  const getResourceIcon = (resourceType: string) => {
    switch (resourceType) {
      case "USER": return User;
      case "ORGANIZATION": return Building2;
      case "JOB": return Briefcase;
      case "APPLICATION": return FileText;
      case "AUTHENTICATION": return Shield;
      default: return Settings;
    }
  };

  const getActionColor = (action: string, success: boolean) => {
    if (!success) return "bg-red-100 text-red-800";
    if (action.includes("CREATED")) return "bg-green-100 text-green-800";
    if (action.includes("UPDATED")) return "bg-blue-100 text-blue-800";
    if (action.includes("DELETED")) return "bg-red-100 text-red-800";
    if (action.includes("LOGIN")) return "bg-purple-100 text-purple-800";
    return "bg-gray-100 text-gray-800";
  };

  const formatAction = (action: string) => {
    return action
      .toLowerCase()
      .split("_")
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No recent activity found
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity) => {
              const ActionIcon = getActionIcon(activity.action);
              const ResourceIcon = getResourceIcon(activity.resourceType);
              const userName = activity.user?.name || activity.userName || "Unknown User";
              const userEmail = activity.user?.email || activity.userEmail || "";

              return (
                <div key={activity.id} className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex-shrink-0">
                    <div className="relative">
                      <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                        <ResourceIcon className="h-5 w-5 text-gray-600" />
                      </div>
                      <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-white rounded-full flex items-center justify-center border border-gray-200">
                        <ActionIcon className="h-2.5 w-2.5 text-gray-500" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {userName}
                      </p>
                      <Badge className={getActionColor(activity.action, activity.success)}>
                        {formatAction(activity.action)}
                      </Badge>
                      {!activity.success && (
                        <Badge variant="outline" className="text-red-600 border-red-200">
                          Failed
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-1">
                      {formatAction(activity.action)} {activity.resourceType.toLowerCase()}
                      {activity.resourceId && (
                        <span className="text-gray-400"> • {activity.resourceId.slice(0, 8)}...</span>
                      )}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-gray-500 truncate">{userEmail}</p>
                      <p className="text-xs text-gray-400">
                        {formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
        
        {activities.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View all activity →
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}