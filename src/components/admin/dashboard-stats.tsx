"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Users, Building2, Briefcase, FileText, Activity, AlertTriangle } from "lucide-react";

interface DashboardStatsProps {
  userStats: {
    totalUsers: number;
    candidateCount: number;
    businessUserCount: number;
    adminCount: number;
    recentUsers: number;
    activeOrganizations: number;
  };
  jobStats: {
    totalJobs: number;
    publishedJobs: number;
    draftJobs: number;
    closedJobs: number;
    totalApplications: number;
    recentJobs: number;
  };
  auditStats: {
    totalLogs: number;
    failedOperations: number;
    recentActivity: number;
  };
}

export function DashboardStats({ userStats, jobStats, auditStats }: DashboardStatsProps) {
  const stats = [
    {
      title: "Total Users",
      value: userStats.totalUsers.toLocaleString(),
      change: `+${userStats.recentUsers} this month`,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Active Organizations",
      value: userStats.activeOrganizations.toLocaleString(),
      change: "Organizations",
      icon: Building2,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Published Jobs",
      value: jobStats.publishedJobs.toLocaleString(),
      change: `${jobStats.draftJobs} drafts`,
      icon: Briefcase,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Total Applications",
      value: jobStats.totalApplications.toLocaleString(),
      change: "All time",
      icon: FileText,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Recent Activity",
      value: auditStats.recentActivity.toLocaleString(),
      change: "Last 24 hours",
      icon: Activity,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
    },
    {
      title: "Failed Operations",
      value: auditStats.failedOperations.toLocaleString(),
      change: "Requires attention",
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {stats.map((stat) => {
        const Icon = stat.icon;
        return (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}