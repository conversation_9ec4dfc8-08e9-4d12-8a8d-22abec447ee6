"use client"

import { useEffect, useState } from "react"
import { getCsrfToken } from "next-auth/react"

export function useCSRFToken() {
  const [csrfToken, setCSRFToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchToken() {
      try {
        const token = await getCsrfToken()
        setCSRFToken(token || null)
      } catch (error) {
        console.error("Failed to fetch CSRF token:", error)
        setCSRFToken(null)
      } finally {
        setIsLoading(false)
      }
    }

    fetchToken()
  }, [])

  return { csrfToken, isLoading }
}

/**
 * Helper to add CSRF token to fetch requests
 */
export function csrfFetch(url: string, options: RequestInit = {}): Promise<Response> {
  return getCsrfToken().then(token => {
    const headers = new Headers(options.headers)
    
    // Add CSRF token to headers
    if (token) {
      headers.set("x-csrf-token", token)
    }

    return fetch(url, {
      ...options,
      headers,
    })
  })
}