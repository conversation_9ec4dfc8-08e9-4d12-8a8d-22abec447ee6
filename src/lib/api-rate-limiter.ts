import { type NextRequest } from "next/server"
import { db } from "@/lib/core/db"

export interface ApiRateLimitConfig {
  windowMs: number
  maxRequests: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

export interface ApiRateLimitResult {
  allowed: boolean
  limit: number
  remaining: number
  resetTime: Date
  retryAfter?: number
}

export class ApiRateLimiter {
  private config: ApiRateLimitConfig

  constructor(config: ApiRateLimitConfig) {
    this.config = {
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config
    }
  }

  /**
   * Check rate limit using database storage (for API routes)
   */
  async checkLimit(
    identifier: string,
    route: string,
    success?: boolean
  ): Promise<ApiRateLimitResult> {
    const windowStart = new Date(Math.floor(Date.now() / this.config.windowMs) * this.config.windowMs)
    
    // Skip counting if configured
    if (success !== undefined) {
      if (success && this.config.skipSuccessfulRequests) {
        return this.getAllowedResult()
      }
      if (!success && this.config.skipFailedRequests) {
        return this.getAllowedResult()
      }
    }

    // Get or create rate limit entry
    const entry = await db.rateLimitEntry.upsert({
      where: {
        identifier_route_windowStart: {
          identifier,
          route,
          windowStart
        }
      },
      update: {
        count: { increment: 1 }
      },
      create: {
        identifier,
        route,
        windowStart,
        count: 1
      }
    })

    const resetTime = new Date(windowStart.getTime() + this.config.windowMs)
    const remaining = Math.max(0, this.config.maxRequests - entry.count)
    const allowed = entry.count <= this.config.maxRequests

    const result: ApiRateLimitResult = {
      allowed,
      limit: this.config.maxRequests,
      remaining,
      resetTime
    }

    if (!allowed) {
      result.retryAfter = Math.ceil((resetTime.getTime() - Date.now()) / 1000)
      
      // Log rate limit violations
      await this.logRateLimitViolation(identifier, route, entry.count)
    }

    return result
  }

  /**
   * Create allowed result for skipped requests
   */
  private getAllowedResult(): ApiRateLimitResult {
    return {
      allowed: true,
      limit: this.config.maxRequests,
      remaining: this.config.maxRequests,
      resetTime: new Date(Date.now() + this.config.windowMs)
    }
  }

  /**
   * Log rate limit violations for monitoring
   */
  private async logRateLimitViolation(
    identifier: string,
    route: string,
    attemptCount: number
  ): Promise<void> {
    try {
      await db.auditLog.create({
        data: {
          action: "API_RATE_LIMIT_EXCEEDED",
          resourceType: "SYSTEM",
          resourceId: route,
          ipAddress: identifier,
          metadata: {
            route,
            attemptCount,
            limit: this.config.maxRequests,
            windowMs: this.config.windowMs
          },
          success: false,
          errorMessage: `Rate limit exceeded: ${attemptCount}/${this.config.maxRequests} requests`
        }
      })
    } catch (error) {
      console.error("Failed to log rate limit violation:", error)
    }
  }

  /**
   * Cleanup old rate limit entries
   */
  static async cleanup(): Promise<number> {
    // Remove entries older than 24 hours
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000)
    
    const result = await db.rateLimitEntry.deleteMany({
      where: {
        windowStart: { lt: cutoff }
      }
    })

    return result.count
  }
}

// Predefined API rate limiters (for server-side use)
export const apiRateLimiters = {
  // Authentication endpoints - stricter limits
  auth: new ApiRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    skipSuccessfulRequests: true // Only count failures
  }),

  // Password reset requests
  passwordReset: new ApiRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 password reset requests per hour
    skipSuccessfulRequests: false
  }),

  // 2FA attempts
  twoFactor: new ApiRateLimiter({
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 5, // 5 attempts per 5 minutes
    skipSuccessfulRequests: true
  }),

  // File upload endpoints
  upload: new ApiRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10 // 10 uploads per hour
  })
}

// Helper function to get client identifier from various sources
export function getClientIdentifier(req?: NextRequest, ip?: string, userId?: string): string {
  // Prefer user ID if available (for authenticated requests)
  if (userId) {
    return `user:${userId}`
  }

  // Fall back to IP address
  if (req) {
    const forwardedFor = req.headers.get("x-forwarded-for")
    const realIp = req.headers.get("x-real-ip")
    const cfConnectingIp = req.headers.get("cf-connecting-ip")
    
    return forwardedFor?.split(",")[0]?.trim() || 
           realIp || 
           cfConnectingIp || 
           "unknown"
  }

  return ip || "unknown"
}