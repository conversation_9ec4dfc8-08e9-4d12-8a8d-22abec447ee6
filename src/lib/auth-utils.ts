import { auth } from "@/lib/auth"
import { redirect } from "next/navigation"
import { hash } from "bcryptjs"
import { z } from "zod"
import { randomBytes } from "crypto"

import { db } from "@/lib/core/db"
import { UserRole } from "@prisma/client"

export async function getSession() {
  return await auth()
}

export async function getCurrentUser() {
  const session = await getSession()
  return session?.user
}

export async function requireAuth() {
  const session = await getSession()
  if (!session?.user) {
    redirect("/auth/signin")
  }
  return session.user
}

export async function requireRole(role: UserRole) {
  const user = await requireAuth()
  if (user.role !== role) {
    redirect("/unauthorized")
  }
  return user
}

export async function requireBusinessUser() {
  const user = await requireAuth()
  const businessRoles: UserRole[] = ["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP"]
  if (!businessRoles.includes(user.role)) {
    redirect("/unauthorized")
  }
  return user
}

export async function requireAdmin() {
  return await requireRole("SUPER_ADMIN")
}

// Password utilities
export async function hashPassword(password: string): Promise<string> {
  return await hash(password, 12)
}

// Email validation utilities
const personalEmailDomains = [
  "gmail.com",
  "yahoo.com", 
  "hotmail.com",
  "outlook.com",
  "icloud.com",
  "aol.com",
  "protonmail.com",
  "mail.com"
]

export function isPersonalEmail(email: string): boolean {
  const domain = email.split("@")[1]?.toLowerCase()
  return personalEmailDomains.includes(domain || "")
}

export function isBusinessEmail(email: string): boolean {
  return !isPersonalEmail(email)
}

// Organization utilities
export async function getOrCreateOrganization(email: string, organizationName?: string) {
  const domain = email.split("@")[1]?.toLowerCase()
  
  if (!domain || isPersonalEmail(email)) {
    return null
  }

  // Try to find existing organization by domain
  const existingOrg = await db.organization.findFirst({
    where: {
      users: {
        some: {
          email: {
            endsWith: `@${domain}`,
          },
        },
      },
    },
  })

  if (existingOrg) {
    return existingOrg
  }

  // Create new organization if provided name
  if (organizationName) {
    const slug = organizationName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "")

    return await db.organization.create({
      data: {
        name: organizationName,
        slug: `${slug}-${Date.now()}`, // Ensure uniqueness
        domain,
      },
    })
  }

  return null
}

// Password validation regex for strong passwords
const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
const strongPasswordMessage = "Password must contain at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character (@$!%*?&)"

// User registration schemas
export const candidateSignupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string()
    .min(10, "Password must be at least 10 characters")
    .regex(strongPasswordRegex, strongPasswordMessage),
})

export const businessSignupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address").refine(
    (email) => isBusinessEmail(email),
    "Please use a business email address"
  ),
  password: z.string()
    .min(10, "Password must be at least 10 characters")
    .regex(strongPasswordRegex, strongPasswordMessage),
  organizationName: z.string().min(2, "Organization name is required"),
  role: z.enum(["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP"]).default("ORG_ADMIN"),
})

export const adminSignupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(12, "Admin password must be at least 12 characters"),
  adminKey: z.string().min(1, "Admin key is required"),
})

// User creation functions
export async function createCandidateUser(data: z.infer<typeof candidateSignupSchema>) {
  const hashedPassword = await hashPassword(data.password)

  return await db.user.create({
    data: {
      name: data.name,
      email: data.email,
      password: hashedPassword,
      role: "CANDIDATE",
    },
  })
}

export async function createBusinessUser(data: z.infer<typeof businessSignupSchema>) {
  const hashedPassword = await hashPassword(data.password)
  
  // Get or create organization
  const organization = await getOrCreateOrganization(data.email, data.organizationName)
  
  if (!organization) {
    throw new Error("Could not create organization for business email")
  }

  return await db.user.create({
    data: {
      name: data.name,
      email: data.email,
      password: hashedPassword,
      role: data.role,
      organizationId: organization.id,
    },
    include: {
      organization: true,
    },
  })
}

export async function createAdminUser(data: z.infer<typeof adminSignupSchema>) {
  // Verify admin key (you should set this in environment variables)
  const adminKey = process.env.ADMIN_CREATION_KEY
  if (!adminKey || data.adminKey !== adminKey) {
    throw new Error("Invalid admin key")
  }

  const hashedPassword = await hashPassword(data.password)

  return await db.user.create({
    data: {
      name: data.name,
      email: data.email,
      password: hashedPassword,
      role: "SUPER_ADMIN",
    },
  })
}

// Password Reset Functionality

export const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
})

export const resetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: z.string()
    .min(10, "Password must be at least 10 characters")
    .regex(strongPasswordRegex, strongPasswordMessage),
})

// Generate password reset token
export async function generatePasswordResetToken(email: string) {
  const user = await db.user.findUnique({
    where: { email },
  })

  if (!user) {
    throw new Error("User not found")
  }

  // Generate secure random token
  const token = randomBytes(32).toString("hex")
  const expires = new Date(Date.now() + 3600000) // 1 hour from now

  // Update user with reset token (using User model resetToken fields)
  const updatedUser = await db.user.update({
    where: { id: user.id },
    data: {
      resetToken: token,
      resetTokenExpiry: expires,
    },
  })

  return {
    token: token,
    expires: expires,
    user: {
      id: updatedUser.id,
      email: updatedUser.email,
      name: updatedUser.name,
    },
  }
}

// Verify and use password reset token
export async function resetPasswordWithToken(token: string, newPassword: string) {
  // Find user by reset token
  const user = await db.user.findFirst({
    where: { resetToken: token },
  })

  if (!user || !user.resetToken || !user.resetTokenExpiry) {
    throw new Error("Invalid or expired reset token")
  }

  if (user.resetTokenExpiry < new Date()) {
    // Clean up expired token
    await db.user.update({
      where: { id: user.id },
      data: {
        resetToken: null,
        resetTokenExpiry: null,
      },
    })
    throw new Error("Reset token has expired")
  }

  // Hash new password and update user
  const hashedPassword = await hashPassword(newPassword)
  
  const updatedUser = await db.user.update({
    where: { id: user.id },
    data: { 
      password: hashedPassword,
      resetToken: null,
      resetTokenExpiry: null,
    },
  })

  return {
    id: updatedUser.id,
    email: updatedUser.email,
    name: updatedUser.name,
  }
}

// Email service functions

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

// Console email transport for development
export async function sendConsoleEmail(options: EmailOptions) {
  console.log("\n" + "=".repeat(60))
  console.log("📧 EMAIL SENT (Console Transport)")
  console.log("=".repeat(60))
  console.log(`To: ${options.to}`)
  console.log(`Subject: ${options.subject}`)
  console.log("Content:")
  console.log("-".repeat(40))
  console.log(options.text || stripHtmlTags(options.html))
  console.log("-".repeat(40))
  if (options.html !== options.text) {
    console.log("HTML Preview:")
    console.log(options.html)
  }
  console.log("=".repeat(60) + "\n")
}

// Send password reset email
export async function sendPasswordResetEmail(email: string, token: string, userName?: string) {
  const resetUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/auth/reset-password?token=${token}`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Reset Your Password - Sourceflex</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2563eb;">Password Reset Request</h1>
            
            <p>Hello${userName ? ` ${userName}` : ''},</p>
            
            <p>We received a request to reset your password for your Sourceflex account. If you didn't make this request, you can safely ignore this email.</p>
            
            <p>To reset your password, click the button below:</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}" 
                   style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                   Reset My Password
                </a>
            </div>
            
            <p>Or copy and paste this link in your browser:</p>
            <p style="background-color: #f3f4f6; padding: 10px; border-radius: 4px; word-break: break-all;">
                <a href="${resetUrl}">${resetUrl}</a>
            </p>
            
            <p style="color: #6b7280; font-size: 14px;">
                This link will expire in 1 hour for security reasons.
            </p>
            
            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
            
            <p style="color: #6b7280; font-size: 12px;">
                If you're having trouble clicking the button, you can copy and paste the URL above into your web browser.
            </p>
            
            <p style="color: #6b7280; font-size: 12px;">
                Best regards,<br>
                The Sourceflex Team
            </p>
        </div>
    </body>
    </html>
  `

  const text = `
Password Reset Request - Sourceflex

Hello${userName ? ` ${userName}` : ''},

We received a request to reset your password for your Sourceflex account. If you didn't make this request, you can safely ignore this email.

To reset your password, visit this link:
${resetUrl}

This link will expire in 1 hour for security reasons.

Best regards,
The Sourceflex Team
  `

  await sendConsoleEmail({
    to: email,
    subject: "Reset Your Password - Sourceflex",
    html,
    text,
  })
}

// Utility function to strip HTML tags for text preview
function stripHtmlTags(html: string): string {
  return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
}