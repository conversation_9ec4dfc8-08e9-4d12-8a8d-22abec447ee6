import { db } from "@/lib/core/db"
import { type User, type User<PERSON>ole as PrismaUserRole } from "@prisma/client"

// Define permission categories and actions
export const PERMISSION_CATEGORIES = {
  ORGANIZATION: "organization",
  USERS: "users", 
  JOBS: "jobs",
  APPLICATIONS: "applications",
  VENDORS: "vendors",
  CLIENTS: "clients",
  REPORTS: "reports",
  SYSTEM: "system",
} as const

export const PERMISSION_ACTIONS = {
  CREATE: "create",
  READ: "read", 
  UPDATE: "update",
  DELETE: "delete",
  MANAGE: "manage", // Full control
  EXPORT: "export",
  IMPORT: "import",
} as const

// Define system permissions
export const SYSTEM_PERMISSIONS = {
  // Organization management
  "organization.read": "View organization details and settings",
  "organization.update": "Update organization details and settings", 
  "organization.manage": "Full organization management including billing",
  
  // User management
  "users.create": "Create new users and invite team members",
  "users.read": "View user profiles and information",
  "users.update": "Update user profiles and roles",
  "users.delete": "Delete users and deactivate accounts",
  "users.manage": "Full user management including role assignments",
  
  // Job management
  "jobs.create": "Create and post new jobs",
  "jobs.read": "View jobs and job details",
  "jobs.update": "Edit job postings and requirements",
  "jobs.delete": "Delete job postings",
  "jobs.manage": "Full job management including publishing",
  
  // Application management
  "applications.create": "Create applications (typically for candidates)",
  "applications.read": "View applications and candidate information", 
  "applications.update": "Update application status and notes",
  "applications.delete": "Delete applications",
  "applications.manage": "Full application management including bulk operations",
  
  // Vendor management (VMS)
  "vendors.create": "Add new vendors to the system",
  "vendors.read": "View vendor profiles and performance",
  "vendors.update": "Update vendor information and settings",
  "vendors.delete": "Remove vendors from the system",
  "vendors.manage": "Full vendor management including contracts",
  
  // Client management (CRM)
  "clients.create": "Add new clients and companies",
  "clients.read": "View client information and history",
  "clients.update": "Update client details and preferences",
  "clients.delete": "Remove clients from the system",
  "clients.manage": "Full client management including deals",
  
  // Reporting and analytics
  "reports.read": "View reports and analytics dashboards",
  "reports.export": "Export reports and data",
  "reports.manage": "Create custom reports and manage analytics",
  
  // System administration
  "system.read": "View system health and basic settings",
  "system.update": "Update system configurations",
  "system.manage": "Full system administration",
  "system.audit": "Access audit logs and security reports",
} as const

// Define default role permissions
export const DEFAULT_ROLE_PERMISSIONS = {
  SUPER_ADMIN: [
    "organization.manage",
    "users.manage", 
    "jobs.manage",
    "applications.manage",
    "vendors.manage",
    "clients.manage",
    "reports.manage",
    "system.manage",
    "system.audit",
  ],
  ORG_ADMIN: [
    "organization.update",
    "users.manage",
    "jobs.manage", 
    "applications.manage",
    "vendors.manage",
    "clients.manage",
    "reports.manage",
  ],
  HIRING_MANAGER: [
    "jobs.manage",
    "applications.manage",
    "vendors.read",
    "reports.read",
    "users.read",
  ],
  RECRUITER: [
    "jobs.read",
    "applications.manage",
    "vendors.read",
    "clients.read",
    "reports.read",
  ],
  SALES_REP: [
    "clients.manage",
    "jobs.read",
    "reports.read",
    "users.read",
  ],
  VENDOR: [
    "jobs.read",
    "applications.create",
    "applications.read",
  ],
  CANDIDATE: [
    "jobs.read",
    "applications.create",
    "applications.read", // Only own applications
  ],
  USER: [
    "jobs.read",
  ],
} as const

export type PermissionKey = keyof typeof SYSTEM_PERMISSIONS
export type RoleName = keyof typeof DEFAULT_ROLE_PERMISSIONS

export interface PermissionCheck {
  allowed: boolean
  reason?: string
}

export interface ResourcePermissionCheck extends PermissionCheck {
  resourceId?: string
  resourceType?: string
}

export class PermissionService {
  /**
   * Initialize system permissions in database
   */
  static async initializeSystemPermissions(): Promise<void> {
    const permissions = Object.entries(SYSTEM_PERMISSIONS)
    
    for (const [name, description] of permissions) {
      const category = name.split('.')[0]
      
      await db.permission.upsert({
        where: { name },
        update: { description },
        create: {
          name,
          description,
          category,
        },
      })
    }
  }

  /**
   * Initialize default roles and their permissions
   */
  static async initializeDefaultRoles(): Promise<void> {
    for (const [roleName, permissionNames] of Object.entries(DEFAULT_ROLE_PERMISSIONS)) {
      // Create or update the role
      const role = await db.role.upsert({
        where: { 
          name_organizationId: {
            name: roleName,
            organizationId: null as any // System role
          }
        },
        update: {
          description: this.getRoleDescription(roleName as RoleName),
        },
        create: {
          name: roleName,
          description: this.getRoleDescription(roleName as RoleName),
          isSystemRole: true,
          organizationId: null as any,
        },
      })

      // Clear existing permissions
      await db.rolePermission.deleteMany({
        where: { roleId: role.id },
      })

      // Add permissions
      for (const permissionName of [...permissionNames]) {
        const permission = await db.permission.findUnique({
          where: { name: permissionName },
        })

        if (permission) {
          await db.rolePermission.create({
            data: {
              roleId: role.id,
              permissionId: permission.id,
            },
          })
        }
      }
    }
  }

  /**
   * Check if user has a specific permission
   */
  static async hasPermission(
    userId: string,
    permission: PermissionKey,
    resourceId?: string
  ): Promise<PermissionCheck> {
    const user = await db.user.findUnique({
      where: { id: userId },
      include: {
        roleAssignments: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!user) {
      return { allowed: false, reason: "User not found" }
    }

    // Super admins have all permissions
    if (user.role === "SUPER_ADMIN") {
      return { allowed: true }
    }

    // Check legacy role system first (for backward compatibility)
    const legacyPermissions = DEFAULT_ROLE_PERMISSIONS[user.role as RoleName] || []
    if ((legacyPermissions as readonly PermissionKey[]).includes(permission)) {
      // Additional resource-level checks for certain permissions
      if (this.requiresResourceCheck(permission, user, resourceId)) {
        return await this.checkResourcePermission(user, permission, resourceId)
      }
      return { allowed: true }
    }

    // Check new role-based permissions
    const userPermissions = new Set<string>()
    for (const assignment of user.roleAssignments) {
      for (const rolePermission of assignment.role.permissions) {
        userPermissions.add(rolePermission.permission.name)
      }
    }

    if (userPermissions.has(permission)) {
      // Additional resource-level checks for certain permissions
      if (this.requiresResourceCheck(permission, user, resourceId)) {
        return await this.checkResourcePermission(user, permission, resourceId)
      }
      return { allowed: true }
    }

    return { 
      allowed: false, 
      reason: `Missing permission: ${permission}` 
    }
  }

  /**
   * Check multiple permissions at once
   */
  static async hasPermissions(
    userId: string,
    permissions: PermissionKey[]
  ): Promise<{ [key: string]: PermissionCheck }> {
    const results: { [key: string]: PermissionCheck } = {}
    
    for (const permission of permissions) {
      results[permission] = await this.hasPermission(userId, permission)
    }
    
    return results
  }

  /**
   * Get all permissions for a user
   */
  static async getUserPermissions(userId: string): Promise<PermissionKey[]> {
    const user = await db.user.findUnique({
      where: { id: userId },
      include: {
        roleAssignments: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!user) return []

    // Super admins have all permissions
    if (user.role === "SUPER_ADMIN") {
      return Object.keys(SYSTEM_PERMISSIONS) as PermissionKey[]
    }

    const permissions = new Set<PermissionKey>()

    // Add legacy role permissions
    const legacyPermissions = DEFAULT_ROLE_PERMISSIONS[user.role as RoleName] || []
    for (const p of legacyPermissions) {
      permissions.add(p as PermissionKey)
    }

    // Add new role-based permissions
    for (const assignment of user.roleAssignments) {
      for (const rolePermission of assignment.role.permissions) {
        permissions.add(rolePermission.permission.name as PermissionKey)
      }
    }

    return Array.from(permissions)
  }

  /**
   * Create a custom role for an organization
   */
  static async createCustomRole(
    organizationId: string,
    name: string,
    description: string,
    permissions: PermissionKey[],
    createdBy: string
  ): Promise<{ success: boolean; roleId?: string; error?: string }> {
    try {
      // Check if role name already exists in organization
      const existing = await db.role.findFirst({
        where: {
          name,
          organizationId,
        },
      })

      if (existing) {
        return {
          success: false,
          error: "Role name already exists in this organization",
        }
      }

      // Create the role
      const role = await db.role.create({
        data: {
          name,
          description,
          organizationId,
          isSystemRole: false,
        },
      })

      // Add permissions
      const permissionRecords = await db.permission.findMany({
        where: {
          name: { in: permissions },
        },
      })

      const rolePermissions = permissionRecords.map(permission => ({
        roleId: role.id,
        permissionId: permission.id,
      }))

      await db.rolePermission.createMany({
        data: rolePermissions,
      })

      // Log the role creation
      await db.auditLog.create({
        data: {
          userId: createdBy,
          organizationId,
          action: "USER_ROLE_CHANGED",
          resourceType: "ORGANIZATION",
          resourceId: role.id,
          metadata: {
            action: "custom_role_created",
            roleName: name,
            permissions,
          },
          success: true,
        },
      })

      return {
        success: true,
        roleId: role.id,
      }
    } catch (error) {
      console.error("Error creating custom role:", error)
      return {
        success: false,
        error: "Failed to create role",
      }
    }
  }

  /**
   * Assign role to user
   */
  static async assignRole(
    userId: string,
    roleId: string,
    grantedBy: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Check if assignment already exists
      const existing = await db.userRoleAssignment.findFirst({
        where: { userId, roleId },
      })

      if (existing) {
        return {
          success: false,
          error: "User already has this role",
        }
      }

      // Create assignment
      await db.userRoleAssignment.create({
        data: {
          userId,
          roleId,
          grantedBy,
        },
      })

      // Log the assignment
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { email: true, organizationId: true },
      })

      const role = await db.role.findUnique({
        where: { id: roleId },
        select: { name: true },
      })

      if (user && role) {
        await db.auditLog.create({
          data: {
            userId: grantedBy,
            organizationId: user.organizationId,
            action: "USER_ROLE_CHANGED",
            resourceType: "USER",
            resourceId: userId,
            metadata: {
              action: "role_assigned",
              roleName: role.name,
              targetUser: user.email,
            },
            success: true,
          },
        })
      }

      return { success: true }
    } catch (error) {
      console.error("Error assigning role:", error)
      return {
        success: false,
        error: "Failed to assign role",
      }
    }
  }

  /**
   * Helper: Check if permission requires resource-level validation
   */
  private static requiresResourceCheck(
    permission: PermissionKey,
    user: any,
    resourceId?: string
  ): boolean {
    // Candidates can only access their own applications
    if (user.role === "CANDIDATE" && permission === "applications.read") {
      return true
    }

    // Add more resource-level checks as needed
    return false
  }

  /**
   * Helper: Check resource-level permissions
   */
  private static async checkResourcePermission(
    user: any,
    permission: PermissionKey,
    resourceId?: string
  ): Promise<PermissionCheck> {
    if (user.role === "CANDIDATE" && permission === "applications.read") {
      if (!resourceId) {
        return { allowed: false, reason: "Resource ID required" }
      }

      // Check if application belongs to user
      const application = await db.application.findFirst({
        where: {
          id: resourceId,
          email: user.email, // Applications are linked by email
        },
      })

      return {
        allowed: !!application,
        reason: application ? undefined : "Access denied to this resource",
      }
    }

    return { allowed: true }
  }

  /**
   * Helper: Get role description
   */
  private static getRoleDescription(roleName: RoleName): string {
    const descriptions = {
      SUPER_ADMIN: "Full system administration and access to all features",
      ORG_ADMIN: "Organization administration and management",
      HIRING_MANAGER: "Job posting and candidate management",
      RECRUITER: "Candidate sourcing and application management",
      SALES_REP: "Client relationship and sales management",
      VENDOR: "External vendor access for job applications",
      CANDIDATE: "Job seeker access for applications and profile",
      USER: "Basic user access to public job listings",
    }
    
    return descriptions[roleName] || "Custom role"
  }
}