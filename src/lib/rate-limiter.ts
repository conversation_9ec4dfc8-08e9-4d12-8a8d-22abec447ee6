// This file now re-exports the API rate limiter for backward compatibility
// The actual implementation is split between edge-rate-limiter.ts and api-rate-limiter.ts

export { 
  ApiRateLimiter as RateLimiter, 
  apiRateLimiters as rateLimiters,
  getClientIdentifier,
  type ApiRateLimitConfig as RateLimitConfig,
  type ApiRateLimitResult as RateLimitResult
} from "./api-rate-limiter"

export {
  EdgeRateLimiter,
  edgeRateLimiters,
  applyEdgeRateLimit as applyRateLimit,
  type EdgeRateLimitConfig,
  type EdgeRateLimitResult
} from "./edge-rate-limiter"