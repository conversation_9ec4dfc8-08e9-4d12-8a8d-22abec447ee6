import { ApiRateLimiter } from "./api-rate-limiter"
import { <PERSON>tLog<PERSON> } from "@/lib/security/monitoring/audit"

// Rate limit configurations for different 2FA operations
export const twoFactorRateLimits = {
  // Email 2FA code sending - strict limit to prevent spam
  emailSend: new ApiRateLimiter({
    windowMs: 2 * 60 * 1000, // 2 minutes
    maxRequests: 1, // 1 request per 2 minutes
    skipSuccessfulRequests: false,
  }),

  // Email 2FA verification - moderate limit
  emailVerify: new ApiRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    skipSuccessfulRequests: true, // Don't count successful verifications
  }),

  // TOTP verification - moderate limit
  totpVerify: new ApiRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 attempts per 15 minutes
    skipSuccessfulRequests: true,
  }),

  // Overall 2FA attempts per user - strict daily limit
  dailyAttempts: new ApiRateLimiter({
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    maxRequests: 50, // 50 attempts per day
    skipSuccessfulRequests: true,
  }),

  // Admin 2FA attempts - extra strict
  adminAttempts: new ApiRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5, // 5 attempts per hour
    skipSuccessfulRequests: true,
  }),
}

/**
 * Check 2FA rate limits and log violations
 */
export async function check2FARateLimit(
  type: keyof typeof twoFactorRateLimits,
  identifier: string,
  success: boolean = false,
  auditContext?: {
    userId?: string
    userEmail?: string
    ipAddress?: string
    userAgent?: string
  }
): Promise<{ allowed: boolean; retryAfter?: number }> {
  const limiter = twoFactorRateLimits[type]
  const result = await limiter.checkLimit(identifier, `2fa-${type}`, success)

  // Log rate limit violations
  if (!result.allowed && auditContext) {
    const auditor = new AuditLogger(auditContext)
    await auditor.log({
      action: "API_RATE_LIMIT_EXCEEDED",
      resourceType: "AUTHENTICATION",
      details: {
        limitType: `2fa-${type}`,
        identifier,
        limit: result.limit,
        windowMs: limiter["config"].windowMs,
      },
      severity: "medium",
    })
  }

  return {
    allowed: result.allowed,
    retryAfter: result.retryAfter,
  }
}

/**
 * Get identifier for rate limiting based on context
 */
export function get2FARateLimitIdentifier(
  userId?: string,
  email?: string,
  ipAddress?: string
): string {
  // Prefer user ID if available (authenticated requests)
  if (userId) {
    return `user:${userId}`
  }
  
  // Fall back to email for unauthenticated requests
  if (email) {
    return `email:${email}`
  }
  
  // Last resort: IP address
  return `ip:${ipAddress || "unknown"}`
}