import { z } from "zod";
import { AuditAction, AuditResourceType } from "@prisma/client";
import { paginationSchema } from "./users";

// Audit log query schemas
export const getAuditLogsSchema = paginationSchema.extend({
  organizationId: z.string().optional(),
  userId: z.string().optional(),
  action: z.nativeEnum(AuditAction).optional(),
  resourceType: z.nativeEnum(AuditResourceType).optional(),
  resourceId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  includeSystem: z.boolean().default(false),
  search: z.string().optional(), // Search in user names, emails, etc.
});

export const getAuditLogByIdSchema = z.object({
  logId: z.string().min(1, "Log ID is required"),
});

export const getAuditStatsSchema = z.object({
  organizationId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export const exportAuditLogsSchema = z.object({
  organizationId: z.string().optional(),
  startDate: z.date(),
  endDate: z.date(),
  format: z.enum(["CSV", "JSON"]).default("CSV"),
  actions: z.array(z.nativeEnum(AuditAction)).optional(),
  resourceTypes: z.array(z.nativeEnum(AuditResourceType)).optional(),
});

// Type exports
export type GetAuditLogsInput = z.infer<typeof getAuditLogsSchema>;
export type GetAuditLogByIdInput = z.infer<typeof getAuditLogByIdSchema>;
export type GetAuditStatsInput = z.infer<typeof getAuditStatsSchema>;
export type ExportAuditLogsInput = z.infer<typeof exportAuditLogsSchema>;