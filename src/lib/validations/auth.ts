import { z } from "zod";
import { UserRole } from "@prisma/client";

// Enhanced password validation with custom async validation
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .max(128, "Password cannot exceed 128 characters")
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~])/,
    "Password must contain uppercase, lowercase, number and special character"
  )

// Enhanced password validation function for server-side use
export const validatePasswordEnhanced = async (
  password: string,
  organizationId?: string,
  context?: {
    userId?: string
    username?: string
    email?: string
    organizationName?: string
    firstName?: string
    lastName?: string
  }
) => {
  // Import is done inside function to avoid circular dependency issues
  const { EnhancedPasswordValidator } = await import("@/lib/enhanced-password-validator")
  
  let policy = {}
  if (organizationId) {
    policy = await EnhancedPasswordValidator.getOrganizationPasswordPolicy(organizationId)
  }

  return await EnhancedPasswordValidator.validatePassword(password, policy, context || {})
}

// Email validation schema
export const emailSchema = z.string().email("Invalid email address");

// Name validation schema
export const nameSchema = z.string().min(2, "Name must be at least 2 characters");

// Organization name validation schema
export const organizationNameSchema = z.string().min(2, "Organization name must be at least 2 characters");

// Authentication schemas
export const signUpCandidateSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  name: nameSchema,
});

export const signUpBusinessSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  name: nameSchema,
  organizationName: organizationNameSchema,
});

export const signInSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required"),
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: passwordSchema,
});

// 2FA schemas
export const totpTokenSchema = z.string().length(6, "Token must be 6 digits");

export const enable2FASchema = z.object({
  secret: z.string().min(1, "Secret is required"),
  token: totpTokenSchema,
});

export const verify2FASchema = z.object({
  token: totpTokenSchema,
});

export const disable2FASchema = z.object({
  token: totpTokenSchema,
});

// Profile update schema
export const updateProfileSchema = z.object({
  name: nameSchema.optional(),
});

// Admin verification schemas
export const adminCredentialsSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required"),
});

export const admin2FAVerificationSchema = z.object({
  email: emailSchema,
  token: totpTokenSchema,
});

// Type exports
export type SignUpCandidateInput = z.infer<typeof signUpCandidateSchema>;
export type SignUpBusinessInput = z.infer<typeof signUpBusinessSchema>;
export type SignInInput = z.infer<typeof signInSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type Enable2FAInput = z.infer<typeof enable2FASchema>;
export type Verify2FAInput = z.infer<typeof verify2FASchema>;
export type Disable2FAInput = z.infer<typeof disable2FASchema>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type AdminCredentialsInput = z.infer<typeof adminCredentialsSchema>;
export type Admin2FAVerificationInput = z.infer<typeof admin2FAVerificationSchema>;