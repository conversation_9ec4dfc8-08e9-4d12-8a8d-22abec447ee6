import { z } from "zod";
import { paginationSchema, searchSchema } from "./users";
import { organizationNameSchema } from "./auth";

// Organization management schemas
export const getOrganizationsSchema = paginationSchema.merge(searchSchema);

export const getOrganizationByIdSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
});

export const createOrganizationSchema = z.object({
  name: organizationNameSchema,
  settings: z.record(z.any()).optional(),
});

export const updateOrganizationSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  name: organizationNameSchema.optional(),
  settings: z.record(z.any()).optional(),
});

export const deleteOrganizationSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
});

// Organization settings schemas
export const getOrganizationSettingsSchema = z.object({
  organizationId: z.string().optional(),
});

export const updateOrganizationSettingsSchema = z.object({
  organizationId: z.string().optional(),
  settings: z.record(z.any()),
});

// Organization statistics schema
export const getOrganizationStatsSchema = z.object({
  organizationId: z.string().optional(),
});

// Organization filtering
export const organizationFilterSchema = z.object({
  userCount: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
  jobCount: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
  createdAfter: z.date().optional(),
  createdBefore: z.date().optional(),
});

// Bulk operations
export const bulkUpdateOrganizationsSchema = z.object({
  organizationIds: z.array(z.string()).min(1, "At least one organization ID is required"),
  updates: z.object({
    settings: z.record(z.any()).optional(),
  }),
});

export const bulkDeleteOrganizationsSchema = z.object({
  organizationIds: z.array(z.string()).min(1, "At least one organization ID is required"),
});

// Type exports
export type GetOrganizationsInput = z.infer<typeof getOrganizationsSchema>;
export type GetOrganizationByIdInput = z.infer<typeof getOrganizationByIdSchema>;
export type CreateOrganizationInput = z.infer<typeof createOrganizationSchema>;
export type UpdateOrganizationInput = z.infer<typeof updateOrganizationSchema>;
export type DeleteOrganizationInput = z.infer<typeof deleteOrganizationSchema>;
export type GetOrganizationSettingsInput = z.infer<typeof getOrganizationSettingsSchema>;
export type UpdateOrganizationSettingsInput = z.infer<typeof updateOrganizationSettingsSchema>;
export type GetOrganizationStatsInput = z.infer<typeof getOrganizationStatsSchema>;
export type OrganizationFilterInput = z.infer<typeof organizationFilterSchema>;
export type BulkUpdateOrganizationsInput = z.infer<typeof bulkUpdateOrganizationsSchema>;
export type BulkDeleteOrganizationsInput = z.infer<typeof bulkDeleteOrganizationsSchema>;