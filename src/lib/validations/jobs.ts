import { z } from "zod";
import { JobStatus, LocationType, EmploymentType, ExperienceLevel, SalaryPeriod } from "@prisma/client";
import { paginationSchema, searchSchema } from "./users";

// Job creation and update schemas
export const createJobSchema = z.object({
  title: z.string().min(3, "Job title must be at least 3 characters").max(100, "Job title must be less than 100 characters"),
  description: z.string().min(50, "Job description must be at least 50 characters"),
  requirements: z.array(z.string().min(1, "Requirement cannot be empty")).min(1, "At least one requirement is needed").max(20, "Maximum 20 requirements allowed"),
  benefits: z.array(z.string().min(1, "Benefit cannot be empty")).max(15, "Maximum 15 benefits allowed").optional(),
  
  // Location
  locationType: z.nativeEnum(LocationType),
  city: z.string().optional().nullable(),
  country: z.string().default("US"),
  
  // Job details
  employmentType: z.nativeEnum(EmploymentType).default("FULL_TIME"),
  experience: z.nativeEnum(ExperienceLevel).default("MID_LEVEL"),
  
  // Salary (optional)
  salaryMin: z.number().positive().optional().nullable(),
  salaryMax: z.number().positive().optional().nullable(),
  salaryCurrency: z.string().length(3, "Currency must be 3 characters (e.g., USD)").optional().nullable(),
  salaryPeriod: z.nativeEnum(SalaryPeriod).optional().nullable(),
  
  // Publishing
  publishedAt: z.date().optional().nullable(),
  expiresAt: z.date().optional().nullable(),
}).refine(
  (data) => {
    if (data.salaryMin && data.salaryMax) {
      return data.salaryMax >= data.salaryMin;
    }
    return true;
  },
  {
    message: "Maximum salary must be greater than or equal to minimum salary",
    path: ["salaryMax"],
  }
).refine(
  (data) => {
    if (data.expiresAt && data.publishedAt) {
      return data.expiresAt > data.publishedAt;
    }
    return true;
  },
  {
    message: "Expiration date must be after publication date",
    path: ["expiresAt"],
  }
);

export const updateJobSchema = z.object({
  jobId: z.string().min(1, "Job ID is required"),
  title: z.string().min(3, "Job title must be at least 3 characters").max(100, "Job title must be less than 100 characters").optional(),
  description: z.string().min(50, "Job description must be at least 50 characters").optional(),
  requirements: z.array(z.string().min(1, "Requirement cannot be empty")).min(1, "At least one requirement is needed").max(20, "Maximum 20 requirements allowed").optional(),
  benefits: z.array(z.string().min(1, "Benefit cannot be empty")).max(15, "Maximum 15 benefits allowed").optional(),
  locationType: z.nativeEnum(LocationType).optional(),
  city: z.string().optional().nullable(),
  country: z.string().optional(),
  employmentType: z.nativeEnum(EmploymentType).optional(),
  experience: z.nativeEnum(ExperienceLevel).optional(),
  salaryMin: z.number().positive().optional().nullable(),
  salaryMax: z.number().positive().optional().nullable(),
  salaryCurrency: z.string().length(3, "Currency must be 3 characters (e.g., USD)").optional().nullable(),
  salaryPeriod: z.nativeEnum(SalaryPeriod).optional().nullable(),
  publishedAt: z.date().optional().nullable(),
  expiresAt: z.date().optional().nullable(),
});

export const getJobsSchema = paginationSchema.merge(searchSchema).extend({
  status: z.nativeEnum(JobStatus).optional(),
  locationType: z.nativeEnum(LocationType).optional(),
  employmentType: z.nativeEnum(EmploymentType).optional(),
  experience: z.nativeEnum(ExperienceLevel).optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  salaryMin: z.number().positive().optional(),
  salaryMax: z.number().positive().optional(),
  organizationId: z.string().optional(), // For admin filtering
});

export const getJobByIdSchema = z.object({
  jobId: z.string().min(1, "Job ID is required"),
});

export const deleteJobSchema = z.object({
  jobId: z.string().min(1, "Job ID is required"),
});

export const publishJobSchema = z.object({
  jobId: z.string().min(1, "Job ID is required"),
  publishedAt: z.date().optional(),
  expiresAt: z.date().optional(),
});

export const bulkUpdateJobsSchema = z.object({
  jobIds: z.array(z.string()).min(1, "At least one job ID is required"),
  updates: z.object({
    status: z.nativeEnum(JobStatus).optional(),
    expiresAt: z.date().optional().nullable(),
  }),
});

// Application schemas
export const createApplicationSchema = z.object({
  jobId: z.string().min(1, "Job ID is required"),
  firstName: z.string().min(1, "First name is required").max(50, "First name must be less than 50 characters"),
  lastName: z.string().min(1, "Last name is required").max(50, "Last name must be less than 50 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional().nullable(),
  resumeUrl: z.string().url("Invalid resume URL").optional().nullable(),
  coverLetter: z.string().max(2000, "Cover letter must be less than 2000 characters").optional().nullable(),
  source: z.enum(["DIRECT", "VENDOR", "REFERRAL", "SOCIAL_MEDIA", "JOB_BOARD"]).default("DIRECT"),
  referrer: z.string().optional().nullable(),
});

export const getApplicationsSchema = paginationSchema.merge(searchSchema).extend({
  jobId: z.string().optional(),
  status: z.enum(["PENDING", "REVIEWING", "INTERVIEWING", "OFFERED", "HIRED", "REJECTED", "WITHDRAWN"]).optional(),
  source: z.enum(["DIRECT", "VENDOR", "REFERRAL", "SOCIAL_MEDIA", "JOB_BOARD"]).optional(),
});

export const updateApplicationSchema = z.object({
  applicationId: z.string().min(1, "Application ID is required"),
  status: z.enum(["PENDING", "REVIEWING", "INTERVIEWING", "OFFERED", "HIRED", "REJECTED", "WITHDRAWN"]).optional(),
  stage: z.string().max(100, "Stage name must be less than 100 characters").optional().nullable(),
});

export const getApplicationByIdSchema = z.object({
  applicationId: z.string().min(1, "Application ID is required"),
});

// Job analytics schemas
export const getJobStatsSchema = z.object({
  jobId: z.string().optional(),
  organizationId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

// Public job board schemas (no authentication required)
export const getPublicJobsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20), // Lower limit for public API
  search: z.string().optional(),
  locationType: z.nativeEnum(LocationType).optional(),
  employmentType: z.nativeEnum(EmploymentType).optional(),
  experience: z.nativeEnum(ExperienceLevel).optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  salaryMin: z.number().positive().optional(),
  organizationSlug: z.string().optional(), // Filter by organization slug
});

export const getPublicJobSchema = z.object({
  jobId: z.string().min(1, "Job ID is required"),
});

// Type exports
export type CreateJobInput = z.infer<typeof createJobSchema>;
export type UpdateJobInput = z.infer<typeof updateJobSchema>;
export type GetJobsInput = z.infer<typeof getJobsSchema>;
export type GetJobByIdInput = z.infer<typeof getJobByIdSchema>;
export type DeleteJobInput = z.infer<typeof deleteJobSchema>;
export type PublishJobInput = z.infer<typeof publishJobSchema>;
export type BulkUpdateJobsInput = z.infer<typeof bulkUpdateJobsSchema>;

export type CreateApplicationInput = z.infer<typeof createApplicationSchema>;
export type GetApplicationsInput = z.infer<typeof getApplicationsSchema>;
export type UpdateApplicationInput = z.infer<typeof updateApplicationSchema>;
export type GetApplicationByIdInput = z.infer<typeof getApplicationByIdSchema>;

export type GetJobStatsInput = z.infer<typeof getJobStatsSchema>;
export type GetPublicJobsInput = z.infer<typeof getPublicJobsSchema>;
export type GetPublicJobInput = z.infer<typeof getPublicJobSchema>;