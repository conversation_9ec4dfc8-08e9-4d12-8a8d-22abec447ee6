import { z } from "zod";
import { UserRole } from "@prisma/client";
import { emailSchema, nameSchema } from "./auth";

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
});

// Search schema
export const searchSchema = z.object({
  search: z.string().optional(),
});

// User management schemas
export const getUsersSchema = paginationSchema.merge(searchSchema).extend({
  role: z.nativeEnum(UserRole).optional(),
});

export const getUserByIdSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
});

export const updateUserSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  name: nameSchema.optional(),
  role: z.nativeEnum(UserRole).optional(),
});

export const deleteUserSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
});

export const inviteUserSchema = z.object({
  email: emailSchema,
  role: z.nativeEnum(UserRole),
  name: nameSchema,
});

export const acceptInvitationSchema = z.object({
  token: z.string().min(1, "Invitation token is required"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain uppercase, lowercase, number and special character"
    ),
});

// Bulk operations
export const bulkUpdateUsersSchema = z.object({
  userIds: z.array(z.string()).min(1, "At least one user ID is required"),
  updates: z.object({
    role: z.nativeEnum(UserRole).optional(),
  }),
});

export const bulkDeleteUsersSchema = z.object({
  userIds: z.array(z.string()).min(1, "At least one user ID is required"),
});

// User filtering
export const userFilterSchema = z.object({
  roles: z.array(z.nativeEnum(UserRole)).optional(),
  organizationId: z.string().optional(),
  emailVerified: z.boolean().optional(),
  twoFactorEnabled: z.boolean().optional(),
  createdAfter: z.date().optional(),
  createdBefore: z.date().optional(),
});

// Type exports
export type GetUsersInput = z.infer<typeof getUsersSchema>;
export type GetUserByIdInput = z.infer<typeof getUserByIdSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type DeleteUserInput = z.infer<typeof deleteUserSchema>;
export type InviteUserInput = z.infer<typeof inviteUserSchema>;
export type AcceptInvitationInput = z.infer<typeof acceptInvitationSchema>;
export type BulkUpdateUsersInput = z.infer<typeof bulkUpdateUsersSchema>;
export type BulkDeleteUsersInput = z.infer<typeof bulkDeleteUsersSchema>;
export type UserFilterInput = z.infer<typeof userFilterSchema>;