import { NextRequest } from "next/server"

interface RateLimitConfig {
  windowMinutes: number
  maxRequests: number
  identifier: string
}

interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: Date
  retryAfter?: number
}

/**
 * In-memory rate limiter for email verification endpoints
 * Tracks requests per email address and IP address separately
 */
export class EmailVerificationRateLimiter {
  private static requestCounts = new Map<string, { count: number; resetTime: Date }>()
  private static suspiciousIPs = new Map<string, { count: number; blockedUntil: Date }>()

  /**
   * Rate limit verification requests by email address
   */
  static checkEmailRateLimit(
    email: string,
    config: { windowMinutes: number; maxRequests: number } = { windowMinutes: 60, maxRequests: 10 }
  ): RateLimitResult {
    return this.checkRateLimit(email, config)
  }

  /**
   * Rate limit verification requests by IP address
   */
  static checkIPRateLimit(
    ipAddress: string,
    config: { windowMinutes: number; maxRequests: number } = { windowMinutes: 60, maxRequests: 50 }
  ): RateLimitResult {
    return this.checkRateLimit(ipAddress, config)
  }

  /**
   * Generic rate limiting implementation
   */
  private static checkRateLimit(
    key: string,
    config: { windowMinutes: number; maxRequests: number }
  ): RateLimitResult {
    const now = new Date()
    const windowMs = config.windowMinutes * 60 * 1000
    const resetTime = new Date(Math.ceil(now.getTime() / windowMs) * windowMs)

    const currentWindow = this.requestCounts.get(key)

    if (!currentWindow || currentWindow.resetTime <= now) {
      // Start new window
      this.requestCounts.set(key, {
        count: 1,
        resetTime
      })

      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime
      }
    }

    // Check if limit exceeded
    if (currentWindow.count >= config.maxRequests) {
      const retryAfter = Math.ceil((currentWindow.resetTime.getTime() - now.getTime()) / 1000)
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: currentWindow.resetTime,
        retryAfter
      }
    }

    // Increment count
    currentWindow.count++
    this.requestCounts.set(key, currentWindow)

    return {
      allowed: true,
      remaining: config.maxRequests - currentWindow.count,
      resetTime: currentWindow.resetTime
    }
  }

  /**
   * Check if IP address is exhibiting suspicious behavior
   */
  static checkSuspiciousActivity(
    ipAddress: string,
    email: string
  ): { suspicious: boolean; blocked: boolean; reason?: string } {
    const now = new Date()

    // Check if IP is currently blocked
    const blocked = this.suspiciousIPs.get(ipAddress)
    if (blocked && blocked.blockedUntil > now) {
      return {
        suspicious: true,
        blocked: true,
        reason: "ip_temporarily_blocked"
      }
    }

    // Clean up expired blocks
    if (blocked && blocked.blockedUntil <= now) {
      this.suspiciousIPs.delete(ipAddress)
    }

    // Check for rapid requests from same IP
    const ipKey = `ip:${ipAddress}`
    const ipData = this.requestCounts.get(ipKey)
    if (ipData && ipData.count > 20) { // More than 20 requests per hour from same IP
      // Block IP for 1 hour
      this.suspiciousIPs.set(ipAddress, {
        count: ipData.count,
        blockedUntil: new Date(now.getTime() + 60 * 60 * 1000)
      })

      return {
        suspicious: true,
        blocked: true,
        reason: "excessive_requests_from_ip"
      }
    }

    // Check for pattern of different emails from same IP (potential abuse)
    const emailKey = `email:${email}`
    const emailData = this.requestCounts.get(emailKey)
    const ipCount = ipData?.count || 0
    const emailCount = emailData?.count || 0

    // Suspicious if IP requests significantly exceed email requests (multiple emails)
    if (ipCount > emailCount * 3 && ipCount > 10) {
      return {
        suspicious: true,
        blocked: false,
        reason: "multiple_emails_from_ip"
      }
    }

    return { suspicious: false, blocked: false }
  }

  /**
   * Apply comprehensive rate limiting for verification requests
   */
  static async applyVerificationRateLimit(
    request: NextRequest,
    email: string
  ): Promise<{
    allowed: boolean
    error?: string
    headers: Record<string, string>
  }> {
    const ipAddress = this.getClientIP(request)
    const headers: Record<string, string> = {}

    // Check IP-based rate limiting first
    const ipLimit = this.checkIPRateLimit(ipAddress)
    if (!ipLimit.allowed) {
      headers["Retry-After"] = (ipLimit.retryAfter || 60).toString()
      headers["X-RateLimit-Limit"] = "50"
      headers["X-RateLimit-Remaining"] = "0"
      headers["X-RateLimit-Reset"] = ipLimit.resetTime.toISOString()

      return {
        allowed: false,
        error: "Too many verification requests from this IP address",
        headers
      }
    }

    // Check email-based rate limiting
    const emailLimit = this.checkEmailRateLimit(email)
    if (!emailLimit.allowed) {
      headers["Retry-After"] = (emailLimit.retryAfter || 60).toString()
      headers["X-RateLimit-Limit"] = "10"
      headers["X-RateLimit-Remaining"] = "0"
      headers["X-RateLimit-Reset"] = emailLimit.resetTime.toISOString()

      return {
        allowed: false,
        error: "Too many verification requests for this email address",
        headers
      }
    }

    // Check for suspicious activity
    const suspiciousCheck = this.checkSuspiciousActivity(ipAddress, email)
    if (suspiciousCheck.blocked) {
      headers["Retry-After"] = "3600" // 1 hour
      
      return {
        allowed: false,
        error: "Request blocked due to suspicious activity",
        headers
      }
    }

    // Set rate limit headers for successful requests
    headers["X-RateLimit-Limit"] = "10"
    headers["X-RateLimit-Remaining"] = emailLimit.remaining.toString()
    headers["X-RateLimit-Reset"] = emailLimit.resetTime.toISOString()

    if (suspiciousCheck.suspicious) {
      headers["X-Security-Warning"] = suspiciousCheck.reason || "suspicious_activity"
    }

    return { allowed: true, headers }
  }

  /**
   * Apply rate limiting for verification attempts
   */
  static async applyVerificationAttemptLimit(
    request: NextRequest,
    email: string
  ): Promise<{
    allowed: boolean
    error?: string
    headers: Record<string, string>
  }> {
    const ipAddress = this.getClientIP(request)
    const headers: Record<string, string> = {}

    // Stricter limits for verification attempts
    const attemptLimit = this.checkRateLimit(`attempt:${email}`, {
      windowMinutes: 15,
      maxRequests: 10
    })

    if (!attemptLimit.allowed) {
      headers["Retry-After"] = (attemptLimit.retryAfter || 60).toString()
      headers["X-RateLimit-Limit"] = "10"
      headers["X-RateLimit-Remaining"] = "0"
      headers["X-RateLimit-Reset"] = attemptLimit.resetTime.toISOString()

      return {
        allowed: false,
        error: "Too many verification attempts for this email",
        headers
      }
    }

    // Check IP-based attempt limiting
    const ipAttemptLimit = this.checkRateLimit(`ip-attempt:${ipAddress}`, {
      windowMinutes: 15,
      maxRequests: 100
    })

    if (!ipAttemptLimit.allowed) {
      headers["Retry-After"] = (ipAttemptLimit.retryAfter || 60).toString()
      
      return {
        allowed: false,
        error: "Too many verification attempts from this IP",
        headers
      }
    }

    // Set success headers
    headers["X-RateLimit-Limit"] = "10"
    headers["X-RateLimit-Remaining"] = attemptLimit.remaining.toString()
    headers["X-RateLimit-Reset"] = attemptLimit.resetTime.toISOString()

    return { allowed: true, headers }
  }

  /**
   * Extract client IP from request
   */
  private static getClientIP(request: NextRequest): string {
    const forwardedFor = request.headers.get("x-forwarded-for")
    const realIp = request.headers.get("x-real-ip")
    const cfConnectingIp = request.headers.get("cf-connecting-ip")
    
    return forwardedFor?.split(",")[0]?.trim() || 
           realIp || 
           cfConnectingIp || 
           "unknown"
  }

  /**
   * Clean up expired rate limit entries (maintenance function)
   */
  static cleanup(): { cleaned: number } {
    const now = new Date()
    let cleaned = 0

    // Clean up request counts
    for (const [key, data] of this.requestCounts.entries()) {
      if (data.resetTime <= now) {
        this.requestCounts.delete(key)
        cleaned++
      }
    }

    // Clean up suspicious IP blocks
    for (const [ip, data] of this.suspiciousIPs.entries()) {
      if (data.blockedUntil <= now) {
        this.suspiciousIPs.delete(ip)
        cleaned++
      }
    }

    return { cleaned }
  }

  /**
   * Get current rate limit statistics
   */
  static getStatistics(): {
    activeKeys: number
    blockedIPs: number
    totalRequests: number
  } {
    const now = new Date()
    let totalRequests = 0
    let activeKeys = 0

    for (const [key, data] of this.requestCounts.entries()) {
      if (data.resetTime > now) {
        activeKeys++
        totalRequests += data.count
      }
    }

    const blockedIPs = Array.from(this.suspiciousIPs.values())
      .filter(block => block.blockedUntil > now).length

    return {
      activeKeys,
      blockedIPs,
      totalRequests
    }
  }

  /**
   * Manually block an IP address (for admin use)
   */
  static blockIP(ipAddress: string, durationMinutes: number = 60): void {
    const blockedUntil = new Date(Date.now() + durationMinutes * 60 * 1000)
    
    this.suspiciousIPs.set(ipAddress, {
      count: 999,
      blockedUntil
    })
  }

  /**
   * Unblock an IP address (for admin use)
   */
  static unblockIP(ipAddress: string): boolean {
    return this.suspiciousIPs.delete(ipAddress)
  }
}