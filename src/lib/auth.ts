import NextAuth from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { type DefaultSession } from "next-auth"
import { type Adapter } from "next-auth/adapters"
import Cred<PERSON>sProvider from "next-auth/providers/credentials"
import GoogleProvider from "next-auth/providers/google"
import { compare } from "bcryptjs"
import { z } from "zod"

import { db } from "@/lib/core/db"
import { env } from "@/lib/core/env"
import { UserRole } from "@prisma/client"
import { verifyTwoFactorToken } from "@/lib/core/two-factor"
import { AuditLogger, createAuditContext } from "@/lib/security/monitoring/audit"
import { OAuthSecurityManager } from "@/lib/security/authentication/oauth-security"
import { CSRFSecurityManager } from "@/lib/security/protection/csrf-security"
import { consumeEmail2FASession } from "@/lib/security/authentication/two-factor-session"
import { SessionSecurityManager } from "@/lib/security/authentication/session-security"

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string
      role: UserRole
      organizationId: string | null
    } & DefaultSession["user"]
    // OAuth verification fields
    needsVerification?: boolean
    verificationReason?: string
    oauthProvider?: string
    oauthEmail?: string
  }

  interface User {
    role: UserRole
    organizationId: string | null
  }
}

const credentialsSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  userType: z.enum(["candidate", "business", "admin"]).optional(),
  twoFactorCode: z.string().optional(),
  email2FASessionId: z.string().optional(),
  email2FASessionToken: z.string().optional(),
})

export const authOptions = {
  adapter: PrismaAdapter(db) as Adapter,
  session: { 
    strategy: "jwt" as const,
    // Configure secure session cookies
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
    generateSessionToken: () => {
      return SessionSecurityManager.generateSecureSessionToken()
    },
  },
  // Enable CSRF protection
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "strict" as const,
        path: "/",
        secure: env.NODE_ENV === "production",
      },
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        sameSite: "strict" as const,
        path: "/",
        secure: env.NODE_ENV === "production",
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "strict" as const,
        path: "/",
        secure: env.NODE_ENV === "production",
      },
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  providers: [
    ...(env.GOOGLE_CLIENT_ID && env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: env.GOOGLE_CLIENT_ID,
        clientSecret: env.GOOGLE_CLIENT_SECRET,
        allowDangerousEmailAccountLinking: true,
      })
    ] : []),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        userType: { label: "User Type", type: "text" },
        twoFactorCode: { label: "2FA Code", type: "text" },
      },
      async authorize(credentials, req) {
        const validatedCredentials = credentialsSchema.safeParse(credentials)
        
        if (!validatedCredentials.success) {
          return null
        }

        const { email, password, userType, twoFactorCode, email2FASessionId, email2FASessionToken } = validatedCredentials.data

        // Create audit context for logging
        const auditContext = {
          userEmail: email,
          ipAddress: req?.headers?.get('x-forwarded-for') || req?.headers?.get('x-real-ip') || undefined,
          userAgent: req?.headers?.get('user-agent') || undefined,
        }
        const auditor = new AuditLogger(auditContext)

        const user = await db.user.findUnique({
          where: { email },
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
            password: true,
            role: true,
            organizationId: true,
            emailVerified: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              }
            },
            twoFactorEnabled: true,
            twoFactorSecret: true,
          },
        })

        if (!user || !user.password) {
          // Log failed login attempt
          await auditor.logLogin(false, "Invalid credentials - user not found or no password")
          return null
        }

        const isPasswordValid = await compare(password, user.password)
        if (!isPasswordValid) {
          // Log failed login attempt
          await auditor.logLogin(false, "Invalid password")
          return null
        }

        // Update audit context with user info
        const updatedAuditContext = {
          ...auditContext,
          userId: user.id,
          userName: user.name || undefined,
          organizationId: user.organizationId || undefined,
        }
        const updatedAuditor = new AuditLogger(updatedAuditContext)

        // Check if email is verified (except for admins who may not need verification)
        if (userType !== "admin" && !user.emailVerified) {
          // Log failed login attempt
          await updatedAuditor.logLogin(false, "Email not verified")
          throw new Error("EMAIL_NOT_VERIFIED")
        }

        // Validate user type access
        if (userType === "admin" && user.role !== "SUPER_ADMIN") {
          await updatedAuditor.logLogin(false, "Unauthorized admin access attempt")
          return null
        }

        if (userType === "business") {
          const businessRoles = ["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP"]
          if (!businessRoles.includes(user.role)) {
            await updatedAuditor.logLogin(false, "Invalid role for business access")
            return null
          }
        }

        // For admin users with 2FA enabled, verify the 2FA code
        if (userType === "admin" && user.twoFactorEnabled) {
          if (!twoFactorCode && !email2FASessionId) {
            await updatedAuditor.logLogin(false, "2FA code required but not provided")
            return null // 2FA code required but not provided
          }

          // Check if email 2FA session is provided
          if (email2FASessionId && email2FASessionToken) {
            // Validate the email 2FA session
            const sessionResult = await consumeEmail2FASession(
              email2FASessionId,
              email2FASessionToken,
              user.id
            )
            
            if (!sessionResult.valid) {
              await updatedAuditor.logLogin(false, `Invalid email 2FA session: ${sessionResult.error}`)
              return null
            }

            await updatedAuditor.log({
              action: "TWO_FACTOR_EMAIL_VERIFIED",
              resourceType: "AUTHENTICATION",
              resourceId: user.id,
              metadata: { method: "email", severity: "low" },
            })
            
            console.log(`Admin ${user.email} authenticated with email 2FA`)
          } else if (twoFactorCode) {
            // Regular TOTP/backup code verification
            const twoFactorResult = await verifyTwoFactorToken(user.id, twoFactorCode)
            if (!twoFactorResult.valid) {
              await updatedAuditor.logLogin(false, "Invalid 2FA code")
              return null // Invalid 2FA code
            }

            await updatedAuditor.log({
              action: "TWO_FACTOR_TOTP_VERIFIED",
              resourceType: "AUTHENTICATION",
              resourceId: user.id,
              metadata: { method: twoFactorResult.method, severity: "low" },
            })

            console.log(`Admin ${user.email} authenticated with ${twoFactorResult.method}`)
          } else {
            await updatedAuditor.logLogin(false, "2FA verification required")
            return null
          }
        }

        // Log successful login
        await updatedAuditor.logLogin(true)

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
          role: user.role,
          organizationId: user.organizationId,
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account, trigger }: any) {
      if (user) {
        // Check if this is a role change that requires session regeneration
        const previousRole = token.role
        token.role = user.role
        token.organizationId = user.organizationId

        // Regenerate session on role change (privilege escalation)
        if (previousRole && previousRole !== user.role && token.sub) {
          await SessionSecurityManager.handleRoleChange(
            token.sub,
            user.role,
            'system',
            {} // Context would be available in a real request
          )
        }
      }

      // Handle OAuth sign-ins with enhanced security
      if (account?.provider && account?.type === "oauth" && user) {
        // Check if this is an existing OAuth link
        const existingAccount = await db.account.findUnique({
          where: {
            provider_providerAccountId: {
              provider: account.provider,
              providerAccountId: account.providerAccountId
            }
          },
          include: { user: true }
        })

        if (existingAccount) {
          // OAuth account already linked - safe to proceed
          token.role = existingAccount.user.role
          token.organizationId = existingAccount.user.organizationId
          return token
        }

        // Check if user with this email exists
        const existingUser = await db.user.findUnique({
          where: { email: user.email! }
        })

        if (existingUser) {
          // SECURITY: Block automatic linking - redirect to verification
          token.needsVerification = true
          token.verificationReason = "account_linking_required"
          token.oauthProvider = account.provider
          token.oauthEmail = user.email
          return token
        }

        // New user - safe to create
        const newUser = await db.user.create({
          data: {
            email: user.email!,
            name: user.name,
            image: user.image,
            role: "CANDIDATE",
            emailVerified: new Date(),
          },
        })

        token.role = newUser.role
        token.organizationId = newUser.organizationId
      }

      return token
    },
    async session({ session, token }: any) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.organizationId = token.organizationId as string | null
        
        // Pass verification requirements to session
        if (token.needsVerification) {
          session.needsVerification = true
          session.verificationReason = token.verificationReason
          session.oauthProvider = token.oauthProvider
          session.oauthEmail = token.oauthEmail
        }
      }
      return session
    },
    async signIn({ user, account, profile }: any) {
      // Allow credentials sign-ins (role validated in authorize)
      if (account?.provider === "credentials") {
        return true
      }

      // Handle OAuth sign-ins with security checks
      if (account?.provider === "google") {
        // This is handled in the JWT callback for OAuth
        // We'll implement a custom OAuth flow
        return true
      }

      return false
    },
    async redirect({ url, baseUrl }: any) {
      // Handle role-based redirects after sign-in
      if (url.startsWith("/")) return `${baseUrl}${url}`
      if (new URL(url).origin === baseUrl) return url
      return baseUrl
    },
  },
}

export const { handlers, auth, signIn, signOut } = NextAuth(authOptions)

// Helper functions for role-based access control
export const isAdmin = (user: { role: UserRole }) => user.role === "SUPER_ADMIN"

export const isBusinessUser = (user: { role: UserRole }) => {
  const businessRoles: UserRole[] = ["ORG_ADMIN", "HIRING_MANAGER", "RECRUITER", "SALES_REP"]
  return businessRoles.includes(user.role)
}

export const isCandidate = (user: { role: UserRole }) => user.role === "CANDIDATE"

export const canAccessOrganization = (user: { organizationId: string | null }, orgId: string) => {
  return user.organizationId === orgId || isAdmin(user as any)
}