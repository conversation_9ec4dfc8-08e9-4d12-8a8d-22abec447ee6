import { type User } from "@prisma/client"
import { RBACManager } from "./rbac"

/**
 * Policy Engine for Complex Permission Rules
 * 
 * Handles advanced permission scenarios that go beyond simple RBAC,
 * including context-aware permissions, time-based access, and custom policies
 */

export interface PolicyContext {
  user: User
  resource?: any
  action: string
  module: string
  organizationId?: string
  additionalData?: Record<string, any>
}

export interface PolicyRule {
  id: string
  name: string
  description: string
  condition: (context: PolicyContext) => boolean | Promise<boolean>
  priority: number
  effect: "allow" | "deny"
}

export class PolicyEngine {
  private static policies: PolicyRule[] = []

  /**
   * Add a custom policy rule
   */
  static addPolicy(policy: PolicyRule): void {
    this.policies.push(policy)
    // Sort by priority (higher numbers first)
    this.policies.sort((a, b) => b.priority - a.priority)
  }

  /**
   * Remove a policy by ID
   */
  static removePolicy(policyId: string): boolean {
    const index = this.policies.findIndex(p => p.id === policyId)
    if (index !== -1) {
      this.policies.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * Evaluate all policies for a given context
   */
  static async evaluatePermission(context: PolicyContext): Promise<boolean> {
    // First check basic RBAC permissions
    const basicPermission = `${context.module}.${context.action}`
    const hasBasicPermission = await RBACManager.hasPermission(
      context.user,
      basicPermission,
      {
        organizationId: context.organizationId,
        resourceId: context.resource?.id,
        ownerId: context.resource?.ownerId || context.resource?.userId
      }
    )

    // If basic permission is denied and no policies exist, deny
    if (!hasBasicPermission && this.policies.length === 0) {
      return false
    }

    // Evaluate custom policies
    let finalDecision = hasBasicPermission

    for (const policy of this.policies) {
      try {
        const conditionMet = await policy.condition(context)
        
        if (conditionMet) {
          if (policy.effect === "deny") {
            return false // Explicit deny always wins
          } else if (policy.effect === "allow") {
            finalDecision = true
          }
        }
      } catch (error) {
        console.error(`Policy ${policy.id} evaluation error:`, error)
        // Continue with other policies
      }
    }

    return finalDecision
  }

  /**
   * Get all applicable policies for a context
   */
  static getApplicablePolicies(context: PolicyContext): PolicyRule[] {
    return this.policies.filter(policy => {
      try {
        return policy.condition(context)
      } catch {
        return false
      }
    })
  }
}

/**
 * Built-in policy rules for common scenarios
 */
export const BuiltInPolicies = {
  /**
   * Organization isolation - users can only access resources within their org
   */
  ORGANIZATION_ISOLATION: {
    id: "org-isolation",
    name: "Organization Isolation",
    description: "Restrict access to resources within user's organization",
    priority: 1000,
    effect: "deny" as const,
    condition: (context: PolicyContext) => {
      if (!context.user.organizationId || !context.resource?.organizationId) {
        return false // No organization context, allow other policies to decide
      }
      return context.user.organizationId !== context.resource.organizationId
    }
  },

  /**
   * Resource ownership - users can access their own resources
   */
  RESOURCE_OWNERSHIP: {
    id: "resource-ownership",
    name: "Resource Ownership",
    description: "Allow users to access resources they own",
    priority: 900,
    effect: "allow" as const,
    condition: (context: PolicyContext) => {
      if (!context.resource) return false
      const ownerId = context.resource.ownerId || context.resource.userId || context.resource.createdBy
      return ownerId === context.user.id
    }
  },

  /**
   * Time-based access - restrict access during certain hours
   */
  BUSINESS_HOURS_ONLY: {
    id: "business-hours",
    name: "Business Hours Access",
    description: "Restrict certain actions to business hours only",
    priority: 500,
    effect: "deny" as const,
    condition: (context: PolicyContext) => {
      const now = new Date()
      const hour = now.getHours()
      const isWeekend = [0, 6].includes(now.getDay())
      
      // Apply to sensitive actions only
      const sensitiveActions = ["delete", "export", "bulk-update"]
      if (!sensitiveActions.includes(context.action)) {
        return false
      }
      
      // Deny if outside business hours (9 AM - 6 PM) or on weekends
      return isWeekend || hour < 9 || hour >= 18
    }
  },

  /**
   * Module-specific restrictions
   */
  CRM_CLIENT_ASSIGNMENT: {
    id: "crm-client-assignment", 
    name: "CRM Client Assignment",
    description: "Sales reps can only access clients assigned to them",
    priority: 800,
    effect: "deny" as const,
    condition: (context: PolicyContext) => {
      if (context.module !== "crm" || context.user.role !== "SALES_REP") {
        return false
      }
      
      if (!context.resource?.assignedTo) {
        return false
      }
      
      return context.resource.assignedTo !== context.user.id
    }
  },

  /**
   * ATS interview scheduling restrictions
   */
  ATS_INTERVIEW_SCHEDULING: {
    id: "ats-interview-scheduling",
    name: "ATS Interview Scheduling Restrictions", 
    description: "Only hiring managers and assigned recruiters can schedule interviews",
    priority: 700,
    effect: "deny" as const,
    condition: (context: PolicyContext) => {
      if (context.module !== "ats" || context.action !== "schedule-interview") {
        return false
      }
      
      const allowedRoles = ["HIRING_MANAGER", "ORG_ADMIN", "SUPER_ADMIN"]
      if (allowedRoles.includes(context.user.role)) {
        return false // Allow these roles
      }
      
      // For recruiters, check if they're assigned to the application
      if (context.user.role === "RECRUITER") {
        return context.resource?.assignedRecruiter !== context.user.id
      }
      
      return true // Deny all others
    }
  }
}

/**
 * Initialize built-in policies
 */
export function initializeBuiltInPolicies(): void {
  Object.values(BuiltInPolicies).forEach(policy => {
    PolicyEngine.addPolicy(policy)
  })
}

/**
 * Helper function to check permissions with policy evaluation
 */
export async function checkPermissionWithPolicies(
  user: User,
  module: string,
  action: string,
  resource?: any,
  additionalData?: Record<string, any>
): Promise<boolean> {
  const context: PolicyContext = {
    user,
    resource,
    action,
    module,
    organizationId: user.organizationId || undefined,
    additionalData
  }

  return PolicyEngine.evaluatePermission(context)
}

/**
 * Decorator for protecting functions with policy-based permissions
 */
export function requirePolicyPermission(module: string, action: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const user = this.user || args.find((arg: any) => arg?.user)?.user
      const resource = this.resource || args.find((arg: any) => arg?.resource)?.resource
      
      if (!user) {
        throw new Error("Authentication required")
      }
      
      const hasPermission = await checkPermissionWithPolicies(user, module, action, resource)
      if (!hasPermission) {
        throw new Error(`Permission denied: ${module}.${action}`)
      }
      
      return method.apply(this, args)
    }
    
    return descriptor
  }
}