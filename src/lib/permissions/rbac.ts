import { type User, type User<PERSON>ole } from "@prisma/client"
import { db } from "@/lib/core/db"

/**
 * Enhanced Role-Based Access Control (RBAC) System
 * 
 * Provides granular permission management for multi-module system
 * with support for CRM, ATS, JOBS, BENCH, and SOCIAL modules
 */

export interface Permission {
  id: string
  name: string
  description: string
  module: string
  action: string
  resource: string
}

export interface RolePermissions {
  role: UserRole
  permissions: string[]
  inheritFrom?: UserRole[]
}

// Enhanced role hierarchy with module-specific permissions
export const ROLE_HIERARCHY: Record<UserRole, RolePermissions> = {
  SUPER_ADMIN: {
    role: "SUPER_ADMIN",
    permissions: ["*"], // All permissions
  },
  ORG_ADMIN: {
    role: "ORG_ADMIN", 
    permissions: [
      // Organization management
      "organization.*",
      
      // User management within org
      "users.create", "users.read", "users.update", "users.delete",
      
      // All modules within organization
      "crm.*", "ats.*", "jobs.*", "bench.*", "social.*",
      
      // Reports and analytics
      "reports.*", "analytics.*"
    ],
  },
  HIRING_MANAGER: {
    role: "HIRING_MANAGER",
    permissions: [
      // Job management
      "jobs.create", "jobs.read", "jobs.update", "jobs.delete", "jobs.publish",
      
      // ATS access
      "ats.read", "ats.update", "applications.read", "applications.update",
      "candidates.read", "candidates.update",
      
      // Interview management  
      "interviews.create", "interviews.read", "interviews.update", "interviews.schedule",
      
      // Team management
      "users.read", "users.update",
      
      // Reports for hiring
      "reports.hiring", "analytics.hiring"
    ],
  },
  RECRUITER: {
    role: "RECRUITER",
    permissions: [
      // Candidate sourcing
      "candidates.create", "candidates.read", "candidates.update",
      "applications.read", "applications.update",
      
      // Job access (read-only for most)
      "jobs.read", "jobs.apply",
      
      // Social media recruiting
      "social.read", "social.post", "social.engage",
      
      // Bench management
      "bench.read", "bench.update", "bench.match",
      
      // Basic CRM (contacts only)
      "contacts.create", "contacts.read", "contacts.update"
    ],
  },
  SALES_REP: {
    role: "SALES_REP",
    permissions: [
      // Full CRM access
      "crm.*", "clients.*", "leads.*", "opportunities.*",
      
      // Job posting for clients
      "jobs.create", "jobs.read", "jobs.update", "jobs.client-post",
      
      // Limited user access
      "users.read",
      
      // Sales reports
      "reports.sales", "analytics.sales"
    ],
  },
  VENDOR: {
    role: "VENDOR", 
    permissions: [
      // Limited job access
      "jobs.read", "jobs.apply", "jobs.vendor-submit",
      
      // Candidate submission
      "candidates.create", "candidates.read", "applications.create",
      
      // Vendor profile management
      "vendor.profile.update", "vendor.submissions.read"
    ],
  },
  CANDIDATE: {
    role: "CANDIDATE",
    permissions: [
      // Job browsing and application
      "jobs.read", "jobs.search", "jobs.apply",
      
      // Profile management
      "profile.read", "profile.update",
      
      // Application tracking
      "applications.own.read", "applications.own.update"
    ],
  },
}

/**
 * Permission checker class for RBAC system
 */
export class RBACManager {
  
  /**
   * Check if user has specific permission
   */
  static async hasPermission(
    user: User,
    permission: string,
    context?: {
      organizationId?: string
      resourceId?: string
      ownerId?: string
    }
  ): Promise<boolean> {
    // Super admin has all permissions
    if (user.role === "SUPER_ADMIN") {
      return true
    }
    
    // Get role permissions
    const rolePermissions = ROLE_HIERARCHY[user.role]
    if (!rolePermissions) {
      return false
    }
    
    // Check for wildcard permission
    if (rolePermissions.permissions.includes("*")) {
      return true
    }
    
    // Check exact permission match
    if (rolePermissions.permissions.includes(permission)) {
      return true
    }
    
    // Check wildcard module permissions (e.g., "crm.*")
    const [module, action] = permission.split(".")
    const moduleWildcard = `${module}.*`
    if (rolePermissions.permissions.includes(moduleWildcard)) {
      return true
    }
    
    // Check organization isolation
    if (context?.organizationId && user.organizationId !== context.organizationId) {
      return false
    }
    
    // Check resource ownership for "own" permissions
    if (permission.includes(".own.") && context?.ownerId === user.id) {
      return true
    }
    
    return false
  }
  
  /**
   * Check multiple permissions (AND logic)
   */
  static async hasAllPermissions(
    user: User,
    permissions: string[],
    context?: { organizationId?: string; resourceId?: string; ownerId?: string }
  ): Promise<boolean> {
    for (const permission of permissions) {
      if (!(await this.hasPermission(user, permission, context))) {
        return false
      }
    }
    return true
  }
  
  /**
   * Check if user has any of the specified permissions (OR logic)
   */
  static async hasAnyPermission(
    user: User,
    permissions: string[],
    context?: { organizationId?: string; resourceId?: string; ownerId?: string }
  ): Promise<boolean> {
    for (const permission of permissions) {
      if (await this.hasPermission(user, permission, context)) {
        return true
      }
    }
    return false
  }
  
  /**
   * Get all permissions for a user's role
   */
  static getUserPermissions(role: UserRole): string[] {
    const rolePermissions = ROLE_HIERARCHY[role]
    return rolePermissions?.permissions || []
  }
  
  /**
   * Check if role can access module
   */
  static canAccessModule(role: UserRole, module: string): boolean {
    const permissions = this.getUserPermissions(role)
    return (
      permissions.includes("*") ||
      permissions.includes(`${module}.*`) ||
      permissions.some(p => p.startsWith(`${module}.`))
    )
  }
  
  /**
   * Get accessible modules for role
   */
  static getAccessibleModules(role: UserRole): string[] {
    const permissions = this.getUserPermissions(role)
    const modules = new Set<string>()
    
    if (permissions.includes("*")) {
      return ["crm", "ats", "jobs", "bench", "social", "reports", "analytics"]
    }
    
    permissions.forEach(permission => {
      const [module] = permission.split(".")
      if (module && module !== "*") {
        modules.add(module)
      }
    })
    
    return Array.from(modules)
  }
}

/**
 * Permission middleware for protecting routes/functions
 */
export function requirePermission(permission: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const user = this.user || args.find((arg: any) => arg?.user)?.user
      
      if (!user) {
        throw new Error("Authentication required")
      }
      
      const hasPermission = await RBACManager.hasPermission(user, permission)
      if (!hasPermission) {
        throw new Error(`Permission denied: ${permission}`)
      }
      
      return method.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * Module-specific permission helpers
 */
export const ModulePermissions = {
  CRM: {
    CREATE_LEAD: "crm.leads.create",
    READ_CLIENTS: "crm.clients.read",
    UPDATE_OPPORTUNITY: "crm.opportunities.update",
    DELETE_CONTACT: "crm.contacts.delete"
  },
  
  ATS: {
    CREATE_CANDIDATE: "ats.candidates.create", 
    READ_APPLICATIONS: "ats.applications.read",
    UPDATE_STATUS: "ats.applications.update",
    SCHEDULE_INTERVIEW: "ats.interviews.schedule"
  },
  
  JOBS: {
    CREATE_JOB: "jobs.create",
    PUBLISH_JOB: "jobs.publish", 
    UPDATE_JOB: "jobs.update",
    DELETE_JOB: "jobs.delete"
  },
  
  BENCH: {
    READ_BENCH: "bench.read",
    UPDATE_AVAILABILITY: "bench.update",
    MATCH_CANDIDATES: "bench.match"
  },
  
  SOCIAL: {
    POST_CONTENT: "social.post",
    READ_ANALYTICS: "social.analytics",
    ENGAGE_POSTS: "social.engage"
  }
} as const