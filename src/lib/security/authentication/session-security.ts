import { randomBytes, createHmac, timingSafeEqual } from "crypto"
import { db } from "@/lib/core/db"
import { env } from "@/lib/core/env"
import { AuditLogger } from "@/lib/security/monitoring/audit"
import { type Session, type User, DeviceType } from "@prisma/client"

export interface SecuritySession extends Session {
  user: User
}

export interface SessionRegenerationOptions {
  reason: "login" | "role_change" | "privilege_escalation" | "security_event"
  preserveDeviceInfo?: boolean
  invalidateOtherSessions?: boolean
}

export interface SessionValidationResult {
  valid: boolean
  session?: SecuritySession
  error?: string
  requiresRegeneration?: boolean
}

/**
 * Enhanced session security manager for preventing session fixation,
 * hijacking, and implementing secure session regeneration
 */
export class SessionSecurityManager {
  private static readonly SESSION_TOKEN_LENGTH = 32
  private static readonly HMAC_SECRET = env.SESSION_HMAC_SECRET
  private static readonly IP_CHANGE_THRESHOLD = 3 // Max IP changes before suspicion
  private static readonly SESSION_LIFETIME_HOURS = 24

  /**
   * Generate a cryptographically secure session token with HMAC validation
   */
  static generateSecureSessionToken(): string {
    const randomToken = randomBytes(this.SESSION_TOKEN_LENGTH).toString("hex")
    const timestamp = Date.now().toString()
    const hmac = createHmac("sha256", this.HMAC_SECRET)
    hmac.update(randomToken + timestamp)
    const signature = hmac.digest("hex")
    
    return `${randomToken}.${timestamp}.${signature}`
  }

  /**
   * Validate session token integrity and authenticity
   */
  static validateSessionToken(token: string): boolean {
    try {
      const parts = token.split(".")
      if (parts.length !== 3) return false

      const [randomToken, timestamp, providedSignature] = parts
      
      // Verify HMAC signature
      const hmac = createHmac("sha256", this.HMAC_SECRET)
      hmac.update(randomToken + timestamp)
      const expectedSignature = hmac.digest("hex")
      
      if (!timingSafeEqual(Buffer.from(providedSignature), Buffer.from(expectedSignature))) {
        return false
      }

      // Check token age (prevent replay attacks)
      const tokenAge = Date.now() - parseInt(timestamp)
      const maxAge = this.SESSION_LIFETIME_HOURS * 60 * 60 * 1000
      
      return tokenAge <= maxAge
    } catch {
      return false
    }
  }

  /**
   * Regenerate session token securely after authentication events
   */
  static async regenerateSession(
    currentSessionToken: string,
    options: SessionRegenerationOptions,
    auditContext: {
      userId?: string
      ipAddress?: string
      userAgent?: string
      organizationId?: string
    }
  ): Promise<{ success: boolean; newSessionToken?: string; error?: string }> {
    const auditor = new AuditLogger(auditContext)

    try {
      // Find current session
      const currentSession = await db.session.findUnique({
        where: { sessionToken: currentSessionToken },
        include: { user: true }
      })

      if (!currentSession || !currentSession.isActive) {
        await auditor.log({
          action: "SESSION_REGENERATION_FAILED",
          resourceType: "AUTHENTICATION",
          resourceId: currentSessionToken,
          details: { reason: "session_not_found", regenerationReason: options.reason },
          severity: "medium"
        })
        return { success: false, error: "Session not found or inactive" }
      }

      // Generate new secure session token
      const newSessionToken = this.generateSecureSessionToken()

      // Use database transaction for atomic session regeneration
      const updatedSession = await db.$transaction(async (tx) => {
        // Invalidate other sessions if requested
        if (options.invalidateOtherSessions) {
          await tx.session.updateMany({
            where: {
              userId: currentSession.userId,
              sessionToken: { not: currentSessionToken },
              isActive: true
            },
            data: { isActive: false }
          })

          await auditor.log({
            action: "SESSION_INVALIDATION_BULK",
            resourceType: "AUTHENTICATION",
            resourceId: currentSession.userId,
            details: { reason: options.reason },
            severity: "low"
          })
        }

        // Update current session with new token atomically
        return await tx.session.update({
          where: { sessionToken: currentSessionToken },
          data: {
            sessionToken: newSessionToken,
            lastActivity: new Date(),
            ipAddress: auditContext.ipAddress || currentSession.ipAddress,
            // Reset device info if not preserving
            ...(options.preserveDeviceInfo ? {} : {
              deviceId: null,
              deviceName: null,
              deviceType: DeviceType.UNKNOWN,
              browser: null,
              os: null
            })
          }
        })
      })

      await auditor.log({
        action: "SESSION_REGENERATED",
        resourceType: "AUTHENTICATION",
        resourceId: updatedSession.id,
        details: { 
          reason: options.reason,
          preservedDeviceInfo: options.preserveDeviceInfo,
          invalidatedOthers: options.invalidateOtherSessions 
        },
        severity: "low"
      })

      return { success: true, newSessionToken }

    } catch (error) {
      await auditor.log({
        action: "SESSION_REGENERATION_ERROR",
        resourceType: "AUTHENTICATION",
        resourceId: currentSessionToken,
        details: { 
          error: error instanceof Error ? error.message : "Unknown error",
          reason: options.reason 
        },
        severity: "high"
      })

      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Session regeneration failed" 
      }
    }
  }

  /**
   * Comprehensive session validation with security checks
   */
  static async validateSession(
    sessionToken: string,
    context: {
      ipAddress?: string
      userAgent?: string
      path?: string
    }
  ): Promise<SessionValidationResult> {
    // Validate token format and integrity
    if (!this.validateSessionToken(sessionToken)) {
      return { valid: false, error: "Invalid session token format" }
    }

    try {
      const session = await db.session.findUnique({
        where: { sessionToken },
        include: { user: true }
      })

      if (!session || !session.isActive) {
        return { valid: false, error: "Session not found or inactive" }
      }

      // Check session expiration
      if (new Date() > session.expires) {
        await this.invalidateSession(sessionToken, "expired", context)
        return { valid: false, error: "Session expired" }
      }

      // Check for session timeout
      const now = new Date()
      const timeoutMinutes = session.user.sessionTimeoutMinutes
      const lastActivity = new Date(session.lastActivity)
      const timeoutThreshold = new Date(lastActivity.getTime() + timeoutMinutes * 60 * 1000)

      if (now > timeoutThreshold) {
        await this.invalidateSession(sessionToken, "timeout", context)
        return { valid: false, error: "Session timed out" }
      }

      // Detect potential session hijacking
      const hijackingRisk = await this.detectSessionHijacking(session, context)
      if (hijackingRisk.suspicious) {
        await this.handleSuspiciousSession(session, hijackingRisk.reason, context)
        return { 
          valid: false, 
          error: "Session security violation detected",
          requiresRegeneration: true 
        }
      }

      // Update session activity
      await db.session.update({
        where: { sessionToken },
        data: {
          lastActivity: now,
          ipAddress: context.ipAddress || session.ipAddress
        }
      })

      return { valid: true, session: session as SecuritySession }

    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : "Session validation failed" 
      }
    }
  }

  /**
   * Detect potential session hijacking based on behavioral patterns
   */
  private static async detectSessionHijacking(
    session: Session & { user: User },
    context: { ipAddress?: string; userAgent?: string }
  ): Promise<{ suspicious: boolean; reason?: string }> {
    // IP address change detection
    if (context.ipAddress && session.ipAddress && context.ipAddress !== session.ipAddress) {
      // Check IP change frequency
      const recentSessions = await db.session.findMany({
        where: {
          userId: session.userId,
          isActive: true,
          lastActivity: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
        },
        select: { ipAddress: true },
        distinct: ['ipAddress']
      })

      if (recentSessions.length > this.IP_CHANGE_THRESHOLD) {
        return { suspicious: true, reason: "excessive_ip_changes" }
      }

      // Geolocation check could be added here for dramatic IP changes
      // For now, we'll be permissive but log the change
    }

    // User agent consistency check
    if (context.userAgent && session.browser) {
      // Simple check - in production you'd want more sophisticated UA parsing
      const sessionUA = session.browser.toLowerCase()
      const currentUA = context.userAgent.toLowerCase()
      
      // Check for major browser/OS changes (allow version updates)
      const sessionBrowser = sessionUA.split(' ')[0]
      const currentBrowser = currentUA.split(' ')[0]
      
      if (sessionBrowser && currentBrowser && sessionBrowser !== currentBrowser) {
        return { suspicious: true, reason: "user_agent_mismatch" }
      }
    }

    return { suspicious: false }
  }

  /**
   * Handle suspicious session activity
   */
  private static async handleSuspiciousSession(
    session: Session & { user: User },
    reason: string,
    context: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    const auditContext = {
      userId: session.userId,
      userName: session.user.name || undefined,
      organizationId: session.user.organizationId || undefined,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    }
    const auditor = new AuditLogger(auditContext)

    // Invalidate the suspicious session
    await this.invalidateSession(session.sessionToken, "security_violation", context)

    // Log security event
    await auditor.log({
      action: "SESSION_HIJACKING_DETECTED",
      resourceType: "AUTHENTICATION",
      resourceId: session.id,
      details: { 
        reason,
        originalIP: session.ipAddress,
        currentIP: context.ipAddress,
        originalUA: session.browser,
        currentUA: context.userAgent
      },
      severity: "high"
    })

    // Consider additional security measures:
    // - Lock account temporarily
    // - Require email verification
    // - Invalidate all user sessions
    // - Send security alert email
  }

  /**
   * Invalidate session with audit logging
   */
  static async invalidateSession(
    sessionToken: string,
    reason: "logout" | "timeout" | "expired" | "security_violation" | "admin" | "role_change",
    context?: { ipAddress?: string; userAgent?: string }
  ): Promise<boolean> {
    try {
      const session = await db.session.findUnique({
        where: { sessionToken },
        include: { user: true }
      })

      if (!session) return false

      await db.session.update({
        where: { sessionToken },
        data: { isActive: false }
      })

      const auditContext = {
        userId: session.userId,
        userName: session.user.name || undefined,
        organizationId: session.user.organizationId || undefined,
        ipAddress: context?.ipAddress || session.ipAddress,
        userAgent: context?.userAgent
      }
      const auditor = new AuditLogger(auditContext)

      await auditor.log({
        action: "SESSION_INVALIDATED",
        resourceType: "AUTHENTICATION",
        resourceId: session.id,
        details: { reason, sessionId: session.id },
        severity: reason === "security_violation" ? "high" : "low"
      })

      return true

    } catch (error) {
      console.error("Session invalidation failed:", error)
      return false
    }
  }

  /**
   * Handle role changes by regenerating sessions
   */
  static async handleRoleChange(
    userId: string,
    newRole: string,
    changedBy: string,
    context: { ipAddress?: string; userAgent?: string }
  ): Promise<{ success: boolean; affectedSessions: number }> {
    try {
      const sessions = await db.session.findMany({
        where: { userId, isActive: true }
      })

      let regeneratedCount = 0

      for (const session of sessions) {
        const result = await this.regenerateSession(
          session.sessionToken,
          { reason: "role_change", invalidateOtherSessions: false },
          { userId, ...context }
        )

        if (result.success) {
          regeneratedCount++
        }
      }

      const auditor = new AuditLogger({ userId, ...context })
      await auditor.log({
        action: "SESSION_ROLE_CHANGE_REGENERATION",
        resourceType: "AUTHENTICATION",
        resourceId: userId,
        details: { 
          newRole, 
          changedBy, 
          sessionsRegenerated: regeneratedCount 
        },
        severity: "medium"
      })

      return { success: true, affectedSessions: regeneratedCount }

    } catch (error) {
      return { success: false, affectedSessions: 0 }
    }
  }

  /**
   * Cleanup inactive and expired sessions
   */
  static async cleanupSessions(): Promise<{ cleaned: number; errors: number }> {
    let cleaned = 0
    let errors = 0

    try {
      // Remove expired sessions
      const expiredResult = await db.session.deleteMany({
        where: {
          OR: [
            { expires: { lt: new Date() } },
            { 
              AND: [
                { isActive: false },
                { lastActivity: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } }
              ]
            }
          ]
        }
      })

      cleaned += expiredResult.count

      // Deactivate timed-out sessions
      const users = await db.user.findMany({
        select: { id: true, sessionTimeoutMinutes: true }
      })

      for (const user of users) {
        const timeoutThreshold = new Date(Date.now() - user.sessionTimeoutMinutes * 60 * 1000)
        
        const timedOutResult = await db.session.updateMany({
          where: {
            userId: user.id,
            isActive: true,
            lastActivity: { lt: timeoutThreshold }
          },
          data: { isActive: false }
        })

        cleaned += timedOutResult.count
      }

    } catch (error) {
      errors++
      console.error("Session cleanup error:", error)
    }

    return { cleaned, errors }
  }
}