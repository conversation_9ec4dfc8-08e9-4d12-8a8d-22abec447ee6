import { db } from "@/lib/core/db"
import { sendEmail } from "../../core/email"
import { createAuditLog } from "@/lib/security/monitoring/audit"
import { CSRFSecurityManager } from "@/lib/security/protection/csrf-security"
import { SecureError<PERSON>and<PERSON> } from "@/lib/security/utilities/secure-error-handling"
import crypto from "crypto"

export interface OAuthAccountInfo {
  provider: string
  providerAccountId: string
  email: string
  name?: string
  image?: string
}

export interface SecurityContext {
  ipAddress?: string
  userAgent?: string
  csrfToken?: string
  origin?: string
  referer?: string
}

/**
 * Secure OAuth Account Linking System
 * 
 * This system prevents OAuth account takeover attacks by requiring
 * explicit user verification before linking OAuth accounts to existing users.
 */
export class OAuthSecurityManager {
  
  /**
   * Handle OAuth sign-in attempt with security checks
   */
  static async handleOAuthSignIn(
    oauthAccount: OAuthAccountInfo,
    securityContext: SecurityContext
  ) {
    const { provider, providerAccountId, email, name, image } = oauthAccount
    
    // Check if this OAuth account is already linked
    const existingAccount = await db.account.findUnique({
      where: {
        provider_providerAccountId: {
          provider,
          providerAccountId
        }
      },
      include: { user: true }
    })
    
    if (existingAccount) {
      // OAuth account already linked - allow sign in
      await this.logOAuthEvent("OAUTH_LOGIN_SUCCESS", {
        provider,
        email,
        userId: existingAccount.userId,
        ...securityContext
      })
      
      return {
        success: true,
        user: existingAccount.user,
        action: "signin"
      }
    }
    
    // Check if a user exists with this email
    const existingUser = await db.user.findUnique({
      where: { email }
    })
    
    if (existingUser) {
      // SECURITY: Don't auto-link! Create pending link request
      const pendingLink = await this.createPendingAccountLink(
        oauthAccount,
        existingUser.id,
        securityContext
      )
      
      // Send verification email
      await this.sendAccountLinkVerification(existingUser, pendingLink)
      
      await this.logOAuthEvent("OAUTH_LINK_VERIFICATION_REQUIRED", {
        provider,
        email,
        existingUserId: existingUser.id,
        pendingLinkId: pendingLink.id,
        ...securityContext
      })
      
      return {
        success: false,
        action: "verification_required",
        message: "An account with this email already exists. Please check your email to verify account linking.",
        verificationToken: pendingLink.verificationToken
      }
    }
    
    // No existing user - create new account (safe)
    const newUser = await db.user.create({
      data: {
        email,
        name,
        image,
        role: "CANDIDATE",
        emailVerified: new Date(), // OAuth emails are pre-verified
      }
    })
    
    // Create the OAuth account link
    await db.account.create({
      data: {
        userId: newUser.id,
        type: "oauth",
        provider,
        providerAccountId,
      }
    })
    
    await this.logOAuthEvent("OAUTH_NEW_USER_CREATED", {
      provider,
      email,
      userId: newUser.id,
      ...securityContext
    })
    
    return {
      success: true,
      user: newUser,
      action: "new_user_created"
    }
  }
  
  /**
   * Create a pending account link request
   */
  private static async createPendingAccountLink(
    oauthAccount: OAuthAccountInfo,
    existingUserId: string,
    securityContext: SecurityContext
  ) {
    // Generate secure verification token
    const verificationToken = crypto.randomBytes(32).toString("hex")
    
    // Create pending link (expires in 15 minutes)
    const pendingLink = await db.pendingAccountLink.create({
      data: {
        oauthProvider: oauthAccount.provider,
        oauthAccountId: oauthAccount.providerAccountId,
        oauthEmail: oauthAccount.email,
        oauthName: oauthAccount.name,
        oauthImage: oauthAccount.image,
        existingUserId,
        verificationToken,
        requestIpAddress: securityContext.ipAddress,
        requestUserAgent: securityContext.userAgent,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
      }
    })
    
    return pendingLink
  }
  
  /**
   * Send account link verification email
   */
  private static async sendAccountLinkVerification(
    user: any,
    pendingLink: any
  ) {
    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-account-link?token=${pendingLink.verificationToken}`
    
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #1f2937;">Account Linking Verification</h2>
        
        <div style="background: #fef3cd; border: 1px solid #fbbf24; padding: 16px; border-radius: 8px; margin: 20px 0;">
          <p><strong>⚠️ Security Notice:</strong> Someone tried to link a ${pendingLink.oauthProvider} account to your Sourceflex account.</p>
        </div>
        
        <p>If this was you, click the link below to confirm the account linking:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" 
             style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Verify Account Linking
          </a>
        </div>
        
        <div style="background: #f3f4f6; padding: 16px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 10px 0; color: #374151;">Account Details:</h3>
          <p style="margin: 5px 0;"><strong>Provider:</strong> ${pendingLink.oauthProvider}</p>
          <p style="margin: 5px 0;"><strong>Email:</strong> ${pendingLink.oauthEmail}</p>
          ${pendingLink.oauthName ? `<p style="margin: 5px 0;"><strong>Name:</strong> ${pendingLink.oauthName}</p>` : ''}
        </div>
        
        <p><strong>Important:</strong></p>
        <ul>
          <li>This link expires in 15 minutes</li>
          <li>If you didn't request this linking, ignore this email</li>
          <li>Your account remains secure - no access has been granted</li>
        </ul>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          This verification was requested from IP: ${pendingLink.requestIpAddress || 'Unknown'}<br>
          If you have concerns about account security, contact support immediately.
        </p>
      </div>
    `
    
    await sendEmail({
      to: user.email,
      subject: "🔐 Verify Account Linking - Sourceflex",
      html: emailHtml,
      text: `Account Linking Verification - Someone tried to link a ${pendingLink.oauthProvider} account to your Sourceflex account. If this was you, visit: ${verificationUrl}`
    })
    
    // Mark verification email as sent
    await db.pendingAccountLink.update({
      where: { id: pendingLink.id },
      data: { verificationSent: true }
    })
  }
  
  /**
   * Validate CSRF protection for OAuth operations
   */
  private static validateOAuthCSRF(securityContext: SecurityContext): { valid: boolean; error?: string } {
    // Skip CSRF validation if no token provided (backward compatibility during migration)
    if (!securityContext.csrfToken) {
      return { valid: true }
    }

    const allowedOrigins = [process.env.NEXTAUTH_URL].filter(Boolean) as string[]
    
    // Check origin validation
    if (securityContext.origin && !allowedOrigins.includes(securityContext.origin)) {
      return { valid: false, error: "Invalid origin for OAuth operation" }
    }

    // Validate CSRF token if provided
    if (securityContext.csrfToken) {
      const validation = CSRFSecurityManager.verifySignedToken(
        securityContext.csrfToken,
        process.env.NEXTAUTH_SECRET || ""
      )
      
      if (!validation.valid) {
        return { valid: false, error: `CSRF validation failed: ${validation.reason}` }
      }
    }

    return { valid: true }
  }

  /**
   * Verify account link token and complete the linking
   */
  static async verifyAccountLink(token: string, securityContext: SecurityContext) {
    // Validate CSRF protection for this sensitive operation
    const csrfValidation = this.validateOAuthCSRF(securityContext)
    if (!csrfValidation.valid) {
      await SecureErrorHandler.logSecurityError(
        "OAUTH_CSRF_VALIDATION_FAILED",
        csrfValidation.error || "CSRF validation failed",
        securityContext,
        { operation: "verify_account_link" }
      )
      return { success: false, error: "Security validation failed" }
    }
    const pendingLink = await db.pendingAccountLink.findUnique({
      where: { verificationToken: token },
      include: { existingUser: true }
    })
    
    if (!pendingLink) {
      await this.logOAuthEvent("OAUTH_LINK_VERIFICATION_FAILED", {
        reason: "Invalid token",
        tokenHash: require("crypto").createHash("sha256").update(token).digest("hex").substring(0, 16), // Log hash instead of partial token
        ...securityContext
      })
      return { success: false, error: "Invalid verification token" }
    }
    
    if (pendingLink.expiresAt < new Date()) {
      await this.logOAuthEvent("OAUTH_LINK_VERIFICATION_FAILED", {
        reason: "Token expired",
        pendingLinkId: pendingLink.id,
        ...securityContext
      })
      
      // Clean up expired token
      await db.pendingAccountLink.delete({
        where: { id: pendingLink.id }
      })
      
      return { success: false, error: "Verification token has expired" }
    }
    
    if (pendingLink.isVerified) {
      return { success: false, error: "This link has already been verified" }
    }
    
    // Create the OAuth account link
    await db.account.create({
      data: {
        userId: pendingLink.existingUserId,
        type: "oauth",
        provider: pendingLink.oauthProvider,
        providerAccountId: pendingLink.oauthAccountId,
      }
    })
    
    // Mark as verified
    await db.pendingAccountLink.update({
      where: { id: pendingLink.id },
      data: {
        isVerified: true,
        verifiedAt: new Date()
      }
    })
    
    await this.logOAuthEvent("OAUTH_LINK_VERIFIED_SUCCESS", {
      provider: pendingLink.oauthProvider,
      email: pendingLink.oauthEmail,
      userId: pendingLink.existingUserId,
      pendingLinkId: pendingLink.id,
      ...securityContext
    })
    
    return { 
      success: true, 
      user: pendingLink.existingUser,
      message: "Account successfully linked! You can now sign in with your OAuth provider."
    }
  }
  
  /**
   * Clean up expired pending links (run this periodically)
   */
  static async cleanupExpiredLinks() {
    const expiredLinks = await db.pendingAccountLink.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    })
    
    await this.logOAuthEvent("OAUTH_CLEANUP_EXPIRED_LINKS", {
      deletedCount: expiredLinks.count
    })
    
    return expiredLinks.count
  }
  
  /**
   * Get pending link info for user feedback
   */
  static async getPendingLinkInfo(token: string) {
    const pendingLink = await db.pendingAccountLink.findUnique({
      where: { verificationToken: token },
      include: { existingUser: { select: { email: true, name: true } } }
    })
    
    if (!pendingLink || pendingLink.expiresAt < new Date()) {
      return null
    }
    
    return {
      provider: pendingLink.oauthProvider,
      oauthEmail: pendingLink.oauthEmail,
      oauthName: pendingLink.oauthName,
      existingUserEmail: pendingLink.existingUser.email,
      expiresAt: pendingLink.expiresAt
    }
  }
  
  /**
   * Log OAuth security events
   */
  private static async logOAuthEvent(
    action: string, 
    metadata: Record<string, any>
  ) {
    try {
      await createAuditLog(
        {
          userId: metadata.userId,
          userEmail: metadata.email,
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
        },
        {
          action: action as any, // Will be added to enum
          resourceType: "AUTHENTICATION",
          resourceId: metadata.pendingLinkId || metadata.userId,
          success: action.includes("SUCCESS"),
          metadata: {
            ...metadata,
            timestamp: new Date().toISOString(),
            securityLevel: "HIGH" // OAuth events are high security
          }
        }
      )
    } catch (error) {
      console.error("Failed to log OAuth event:", error)
    }
  }
}