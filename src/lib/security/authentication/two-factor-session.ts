import { randomBytes, timingSafeEqual } from "crypto"
import { db } from "@/lib/core/db"

/**
 * Secure 2FA Session Management
 * 
 * This system creates cryptographically secure session tokens for 2FA verification
 * to prevent bypass attacks where someone could skip the email verification step.
 */

export interface TwoFactorSession {
  id: string
  userId: string
  sessionToken: string
  method: "email" | "totp"
  verified: boolean
  ipAddress?: string
  userAgent?: string
  expiresAt: Date
  createdAt: Date
}

/**
 * Create a new 2FA session for email verification
 */
export async function createEmail2FASession(
  userId: string,
  ipAddress?: string,
  userAgent?: string
): Promise<{ sessionId: string; sessionToken: string }> {
  // Generate cryptographically secure session token
  const sessionToken = randomBytes(32).toString("hex")
  const sessionId = randomBytes(16).toString("hex")
  const expiresAt = new Date(Date.now() + 15 * 60 * 1000) // 15 minutes

  // Store in database (using VerificationToken table with special format)
  await db.verificationToken.create({
    data: {
      identifier: `2fa-session:${sessionId}`,
      token: `${userId}:${sessionToken}:${ipAddress || "unknown"}:${userAgent || "unknown"}`,
      expires: expiresAt,
    }
  })

  return { sessionId, sessionToken }
}

/**
 * Verify an email 2FA session after code verification
 */
export async function verifyEmail2FASession(
  sessionId: string,
  sessionToken: string,
  userId: string
): Promise<{ valid: boolean; error?: string }> {
  try {
    // Find the session
    const session = await db.verificationToken.findFirst({
      where: {
        identifier: `2fa-session:${sessionId}`,
      }
    })

    if (!session) {
      return { valid: false, error: "Session not found" }
    }

    // Check if expired
    if (session.expires < new Date()) {
      await db.verificationToken.delete({
        where: { identifier: `2fa-session:${sessionId}` }
      })
      return { valid: false, error: "Session expired" }
    }

    // Parse and validate session data using timing-safe comparison
    const [storedUserId, storedToken] = session.token.split(":")
    
    if (storedUserId !== userId || !timingSafeEqual(Buffer.from(storedToken), Buffer.from(sessionToken))) {
      return { valid: false, error: "Invalid session" }
    }

    // Mark session as verified by updating the token
    await db.verificationToken.update({
      where: { identifier: `2fa-session:${sessionId}` },
      data: {
        token: `verified:${session.token}`,
      }
    })

    return { valid: true }
  } catch (error) {
    console.error("2FA session verification error:", error)
    return { valid: false, error: "Session verification failed" }
  }
}

/**
 * Consume a verified 2FA session (use it for authentication)
 */
export async function consumeEmail2FASession(
  sessionId: string,
  sessionToken: string,
  userId: string
): Promise<{ valid: boolean; error?: string }> {
  try {
    // Find the session
    const session = await db.verificationToken.findFirst({
      where: {
        identifier: `2fa-session:${sessionId}`,
      }
    })

    if (!session) {
      return { valid: false, error: "Session not found" }
    }

    // Check if expired
    if (session.expires < new Date()) {
      await db.verificationToken.delete({
        where: { identifier: `2fa-session:${sessionId}` }
      })
      return { valid: false, error: "Session expired" }
    }

    // Parse session data
    const parts = session.token.split(":")
    if (parts[0] !== "verified") {
      return { valid: false, error: "Session not verified" }
    }

    const [, storedUserId, storedToken] = parts
    
    if (storedUserId !== userId || !timingSafeEqual(Buffer.from(storedToken), Buffer.from(sessionToken))) {
      return { valid: false, error: "Invalid session" }
    }

    // Delete the session (one-time use)
    await db.verificationToken.delete({
      where: { identifier: `2fa-session:${sessionId}` }
    })

    return { valid: true }
  } catch (error) {
    console.error("2FA session consumption error:", error)
    return { valid: false, error: "Session consumption failed" }
  }
}

/**
 * Clean up expired 2FA sessions
 */
export async function cleanupExpired2FASessions(): Promise<number> {
  try {
    const result = await db.verificationToken.deleteMany({
      where: {
        AND: [
          { identifier: { startsWith: "2fa-session:" } },
          { expires: { lt: new Date() } }
        ]
      }
    })
    
    return result.count
  } catch (error) {
    console.error("2FA session cleanup error:", error)
    return 0
  }
}