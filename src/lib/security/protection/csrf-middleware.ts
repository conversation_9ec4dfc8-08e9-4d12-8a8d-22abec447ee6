import { NextRequest, NextResponse } from "next/server"
import { CSRFSecurityManager } from "@/lib/security/protection/csrf-security"
import { env } from "@/lib/core/env"
import { AuditLogger } from "@/lib/security/monitoring/audit"

export interface CSRFValidationResult {
  valid: boolean
  reason?: string
  response?: NextResponse
}

/**
 * CSRF protection middleware for API routes
 */
export async function validateCSRFToken(
  request: NextRequest,
  options?: {
    skipPaths?: string[]
    customOrigins?: string[]
  }
): Promise<CSRFValidationResult> {
  const { pathname } = request.nextUrl
  const skipPaths = options?.skipPaths || []

  // Skip CSRF validation for whitelisted paths
  if (skipPaths.some(path => pathname.startsWith(path))) {
    return { valid: true }
  }

  // Skip CSRF check for safe methods
  if (["GET", "HEAD", "OPTIONS"].includes(request.method)) {
    return { valid: true }
  }

  // Get allowed origins
  const allowedOrigins = [
    env.NEXTAUTH_URL,
    ...(options?.customOrigins || []),
  ]

  // Validate origin/referer
  if (!CSRFSecurityManager.isOriginAllowed(request, allowedOrigins)) {
    // Log CSRF violation attempt
    const auditContext = {
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      userAgent: request.headers.get('user-agent') || undefined,
    }
    const auditor = new AuditLogger(auditContext)
    await auditor.log({
      action: "CSRF_VIOLATION",
      resourceType: "SYSTEM",
      metadata: {
        details: {
          path: pathname,
          origin: request.headers.get("origin"),
          referer: request.headers.get("referer"),
          method: request.method,
        },
        severity: "high",
      },
    })

    return {
      valid: false,
      reason: "Origin not allowed",
      response: NextResponse.json(
        { error: "Forbidden: Invalid origin" },
        { status: 403 }
      ),
    }
  }

  // For NextAuth endpoints, rely on built-in CSRF protection
  if (pathname.startsWith("/api/auth/")) {
    return { valid: true }
  }

  // For custom API endpoints, validate CSRF token
  const csrfToken = request.headers.get("x-csrf-token")
  
  if (!csrfToken) {
    // Log missing CSRF token
    const auditContext = {
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      userAgent: request.headers.get('user-agent') || undefined,
    }
    const auditor = new AuditLogger(auditContext)
    await auditor.log({
      action: "CSRF_MISSING_TOKEN",
      resourceType: "SYSTEM",
      metadata: {
        details: {
          path: pathname,
          method: request.method,
        },
        severity: "medium",
      },
    })

    return {
      valid: false,
      reason: "CSRF token required",
      response: NextResponse.json(
        { error: "CSRF token required" },
        { status: 403 }
      ),
    }
  }

  // Verify the CSRF token
  const validation = CSRFSecurityManager.verifySignedToken(csrfToken, env.NEXTAUTH_SECRET)
  
  if (!validation.valid) {
    // Log invalid CSRF token
    const auditContext = {
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      userAgent: request.headers.get('user-agent') || undefined,
    }
    const auditor = new AuditLogger(auditContext)
    await auditor.log({
      action: "CSRF_INVALID_TOKEN",
      resourceType: "SYSTEM",
      metadata: {
        details: {
          path: pathname,
          method: request.method,
          reason: validation.reason,
        },
        severity: "high",
      },
    })

    return {
      valid: false,
      reason: validation.reason,
      response: NextResponse.json(
        { error: "Invalid CSRF token" },
        { status: 403 }
      ),
    }
  }

  return { valid: true }
}

/**
 * Create CSRF-protected API route handler
 */
export function withCSRFProtection<T extends (...args: any[]) => any>(
  handler: T,
  options?: {
    skipPaths?: string[]
    customOrigins?: string[]
  }
): T {
  return (async (request: NextRequest, ...args: any[]) => {
    const validation = await validateCSRFToken(request, options)
    
    if (!validation.valid) {
      return validation.response || NextResponse.json(
        { error: "CSRF validation failed" },
        { status: 403 }
      )
    }

    return handler(request, ...args)
  }) as T
}