import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import { SessionSecurityManager } from "../authentication/session-security"
import { EdgeAuditLogger, createEdgeAuditContext } from "../monitoring/edge-audit"

export interface SessionValidationContext {
  ipAddress?: string
  userAgent?: string
  path: string
}

/**
 * Enhanced session validation middleware for protecting API routes
 */
export async function validateSessionSecurity(
  request: NextRequest,
  requireAuth: boolean = true
): Promise<NextResponse | null> {
  const token = await getToken({ 
    req: request,
    secret: process.env.NEXTAUTH_SECRET 
  })

  const context: SessionValidationContext = {
    ipAddress: getClientIP(request),
    userAgent: request.headers.get("user-agent") || undefined,
    path: request.nextUrl.pathname
  }

  // If no auth required and no token, allow through
  if (!requireAuth && !token) {
    return null
  }

  // If auth required but no token, reject
  if (requireAuth && !token) {
    return new NextResponse(
      JSON.stringify({ error: "Authentication required" }),
      { status: 401, headers: { "Content-Type": "application/json" } }
    )
  }

  // If token exists, validate session security
  if (token?.sub) {
    try {
      // For API routes, we use the JWT sub as session identifier
      // In a full implementation, you'd store actual session tokens
      const sessionToken = `jwt-session-${token.sub}`
      
      const validation = await SessionSecurityManager.validateSession(
        sessionToken,
        context
      )

      if (!validation.valid) {
        const auditContext = createEdgeAuditContext(token, {
          headers: {
            'x-forwarded-for': context.ipAddress || '',
            'user-agent': context.userAgent || ''
          }
        })
        const auditor = new EdgeAuditLogger(auditContext)

        await auditor.logSessionValidationFailed({ 
          error: validation.error,
          path: context.path,
          requiresRegeneration: validation.requiresRegeneration 
        })

        // Return appropriate response based on validation failure
        const statusCode = validation.requiresRegeneration ? 403 : 401
        const message = validation.requiresRegeneration 
          ? "Session security violation - please sign in again"
          : "Session invalid or expired"

        return new NextResponse(
          JSON.stringify({ 
            error: message,
            code: validation.requiresRegeneration ? "SESSION_REGENERATION_REQUIRED" : "SESSION_INVALID"
          }),
          { status: statusCode, headers: { "Content-Type": "application/json" } }
        )
      }
    } catch (error) {
      // Log validation error but allow request to continue
      // This prevents security checks from breaking functionality
      console.error("Session validation error:", error)
      
      const auditContext = createEdgeAuditContext(token, {
        headers: {
          'x-forwarded-for': context.ipAddress || '',
          'user-agent': context.userAgent || ''
        }
      })
      const auditor = new EdgeAuditLogger(auditContext)

      await auditor.logSessionValidationError(
        error instanceof Error ? error.message : "Unknown error",
        context.path
      )
    }
  }

  // Session is valid or validation passed, allow request
  return null
}

/**
 * Wrapper for API routes that require session security validation
 */
export function withSessionSecurity(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  requireAuth: boolean = true
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    // Validate session security first
    const securityResponse = await validateSessionSecurity(request, requireAuth)
    if (securityResponse) {
      return securityResponse
    }

    // If validation passes, call the original handler
    return handler(request, ...args)
  }
}

/**
 * Helper to extract client IP from request
 */
function getClientIP(request: NextRequest): string {
  const forwardedFor = request.headers.get("x-forwarded-for")
  const realIp = request.headers.get("x-real-ip") 
  const cfConnectingIp = request.headers.get("cf-connecting-ip")
  
  return forwardedFor?.split(",")[0]?.trim() || 
         realIp || 
         cfConnectingIp || 
         "unknown"
}

/**
 * Session regeneration endpoint for handling security events
 */
export async function regenerateUserSessions(
  userId: string,
  reason: "role_change" | "security_event" | "password_change",
  context: SessionValidationContext
): Promise<{ success: boolean; message: string }> {
  try {
    const result = await SessionSecurityManager.handleRoleChange(
      userId,
      "SYSTEM_TRIGGERED", // Placeholder role for system-triggered regeneration
      "system",
      context
    )

    if (result.success) {
      return {
        success: true,
        message: `Successfully regenerated ${result.affectedSessions} sessions`
      }
    } else {
      return {
        success: false,
        message: "Failed to regenerate sessions"
      }
    }
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Session regeneration failed"
    }
  }
}

/**
 * Terminate all sessions for a user (for security incidents)
 */
export async function terminateAllUserSessions(
  userId: string,
  exceptCurrentSession: boolean = false,
  context: SessionValidationContext
): Promise<{ success: boolean; terminatedCount: number }> {
  try {
    // In a real implementation, you'd have access to actual session tokens
    // For now, we'll use the edge-compatible audit logging to track the termination request
    const auditContext = createEdgeAuditContext({ sub: userId }, {
      headers: {
        'x-forwarded-for': context.ipAddress || '',
        'user-agent': context.userAgent || ''
      }
    })
    const auditor = new EdgeAuditLogger(auditContext)

    await auditor.log({
      action: "SESSION_INVALIDATION_BULK",
      resourceType: "AUTHENTICATION", 
      resourceId: userId,
      severity: "medium",
      metadata: { 
        reason: "admin_action",
        exceptCurrentSession,
        path: context.path
      }
    })

    // TODO: Implement actual session termination when database sessions are used
    return {
      success: true,
      terminatedCount: 0 // Placeholder
    }
  } catch (error) {
    return {
      success: false,
      terminatedCount: 0
    }
  }
}