import { randomBytes, timingSafeEqual } from "crypto"
import { headers } from "next/headers"
import { NextRequest } from "next/server"

export interface CSRFToken {
  token: string
  timestamp: number
}

export class CSRFSecurityManager {
  private static readonly TOKEN_LENGTH = 32
  private static readonly TOKEN_EXPIRY_MS = 60 * 60 * 1000 // 1 hour
  private static readonly COOKIE_NAME = "next-auth.csrf-token"
  private static readonly HEADER_NAME = "x-csrf-token"

  /**
   * Generate a new CSRF token
   */
  static generateToken(): string {
    return randomBytes(this.TOKEN_LENGTH).toString("hex")
  }

  /**
   * Create a signed CSRF token with timestamp
   */
  static createSignedToken(secret: string): string {
    const token = this.generateToken()
    const timestamp = Date.now()
    const data = `${token}.${timestamp}`
    
    // Create HMAC signature
    const crypto = require("crypto")
    const hmac = crypto.createHmac("sha256", secret)
    hmac.update(data)
    const signature = hmac.digest("hex")
    
    return `${data}.${signature}`
  }

  /**
   * Verify a signed CSRF token
   */
  static verifySignedToken(signedToken: string, secret: string): { valid: boolean; reason?: string } {
    try {
      const parts = signedToken.split(".")
      if (parts.length !== 3) {
        return { valid: false, reason: "Invalid token format" }
      }

      const [token, timestampStr, signature] = parts
      const timestamp = parseInt(timestampStr, 10)

      // Check token age
      if (Date.now() - timestamp > this.TOKEN_EXPIRY_MS) {
        return { valid: false, reason: "Token expired" }
      }

      // Verify signature using timing-safe comparison
      const crypto = require("crypto")
      const hmac = crypto.createHmac("sha256", secret)
      hmac.update(`${token}.${timestamp}`)
      const expectedSignature = hmac.digest("hex")

      if (!timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
        return { valid: false, reason: "Invalid signature" }
      }

      return { valid: true }
    } catch (error) {
      return { valid: false, reason: "Token verification failed" }
    }
  }

  /**
   * Extract CSRF token from request headers or body
   */
  static extractTokenFromRequest(request: NextRequest): string | null {
    // Check header first (for API requests)
    const headerToken = request.headers.get(this.HEADER_NAME)
    if (headerToken) {
      return headerToken
    }

    // Check body for form submissions
    if (request.method === "POST" && request.body) {
      // This would need to be extracted from the parsed body in the actual route handler
      // as NextRequest doesn't provide direct body access in middleware
      return null
    }

    return null
  }

  /**
   * Validate CSRF protection for a request
   */
  static async validateRequest(
    request: NextRequest,
    secret: string
  ): Promise<{ valid: boolean; reason?: string }> {
    // Skip CSRF check for safe methods
    if (["GET", "HEAD", "OPTIONS"].includes(request.method)) {
      return { valid: true }
    }

    // Extract token from cookie
    const cookieHeader = request.headers.get("cookie")
    const cookieToken = this.extractTokenFromCookie(cookieHeader)
    
    if (!cookieToken) {
      return { valid: false, reason: "No CSRF cookie found" }
    }

    // Extract token from request
    const requestToken = this.extractTokenFromRequest(request)
    
    if (!requestToken) {
      return { valid: false, reason: "No CSRF token in request" }
    }

    // Tokens must match using timing-safe comparison
    if (!timingSafeEqual(Buffer.from(cookieToken), Buffer.from(requestToken))) {
      return { valid: false, reason: "CSRF token mismatch" }
    }

    // Verify the token signature and expiry
    return this.verifySignedToken(cookieToken, secret)
  }

  /**
   * Extract CSRF token from cookie header
   */
  private static extractTokenFromCookie(cookieHeader: string | null): string | null {
    if (!cookieHeader) return null

    const cookies = cookieHeader.split(";").map(c => c.trim())
    const csrfCookie = cookies.find(c => c.startsWith(`${this.COOKIE_NAME}=`))
    
    if (!csrfCookie) return null

    return csrfCookie.split("=")[1]
  }

  /**
   * Get CSRF cookie options
   */
  static getCookieOptions(isProduction: boolean) {
    return {
      httpOnly: true,
      secure: isProduction,
      sameSite: "strict" as const,
      path: "/",
      maxAge: this.TOKEN_EXPIRY_MS / 1000, // Convert to seconds
    }
  }

  /**
   * Check if origin is allowed
   */
  static isOriginAllowed(request: NextRequest, allowedOrigins: string[]): boolean {
    const origin = request.headers.get("origin")
    const referer = request.headers.get("referer")

    // If no origin or referer, could be a same-origin request
    if (!origin && !referer) {
      return true
    }

    // Check origin first
    if (origin && allowedOrigins.includes(origin)) {
      return true
    }

    // Check referer as fallback
    if (referer) {
      try {
        const refererUrl = new URL(referer)
        return allowedOrigins.includes(refererUrl.origin)
      } catch {
        return false
      }
    }

    return false
  }

  /**
   * Generate CSRF meta tag for HTML pages
   */
  static generateMetaTag(token: string): string {
    return `<meta name="csrf-token" content="${token}" />`
  }

  /**
   * Generate hidden input field for forms
   */
  static generateFormField(token: string): string {
    return `<input type="hidden" name="_csrf" value="${token}" />`
  }
}