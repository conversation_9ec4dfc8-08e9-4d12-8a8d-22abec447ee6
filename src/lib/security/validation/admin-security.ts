import { db } from "../../core/db"
import { sendEmail } from "../../core/email"
import { createAuditLog } from "../monitoring/audit"
import crypto from "crypto"
import { hash, compare } from "bcryptjs"

export interface AdminInvitationData {
  email: string
  name: string
  invitedBy: string
  organizationId?: string
}

export interface SecurityContext {
  ipAddress?: string
  userAgent?: string
  sessionId?: string
}

/**
 * Secure Admin Management System
 * 
 * This system provides secure admin account creation using:
 * - Time-limited invitation tokens
 * - Server-side key derivation
 * - Multi-factor verification
 * - Comprehensive audit logging
 */
export class AdminSecurityManager {
  
  /**
   * Generate a secure admin invitation (replaces simple admin key)
   */
  static async createAdminInvitation(
    data: AdminInvitationData,
    context: SecurityContext,
    masterKey?: string
  ): Promise<{ success: boolean; invitationToken?: string; error?: string }> {
    try {
      // Verify master key if provided (for initial setup)
      if (masterKey && !this.verifyMasterKey(masterKey)) {
        await this.logSecurityEvent("ADMIN_INVITATION_FAILED", {
          reason: "Invalid master key",
          targetEmail: data.email,
          ...context
        })
        return { success: false, error: "Invalid master key" }
      }
      
      // Check if admin already exists
      const existingAdmin = await db.user.findFirst({
        where: {
          role: "SUPER_ADMIN"
        }
      })
      
      // If this is not the first admin, require existing admin authorization
      if (existingAdmin && !masterKey) {
        return { success: false, error: "Admin invitation requires existing admin authorization" }
      }
      
      // Check for existing invitation
      const existingInvitation = await db.adminInvitation.findFirst({
        where: {
          email: data.email,
          expiresAt: { gt: new Date() },
          used: false
        }
      })
      
      if (existingInvitation) {
        return { success: false, error: "Pending invitation already exists for this email" }
      }
      
      // Generate secure invitation token
      const invitationToken = this.generateSecureToken()
      const tokenHash = await hash(invitationToken, 12)
      
      // Create invitation record
      const invitation = await db.adminInvitation.create({
        data: {
          email: data.email,
          name: data.name,
          tokenHash,
          invitedBy: data.invitedBy,
          organizationId: data.organizationId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        }
      })
      
      // Send secure invitation email
      await this.sendAdminInvitationEmail(data, invitationToken)
      
      // Log successful invitation creation
      await this.logSecurityEvent("ADMIN_INVITATION_CREATED", {
        invitationId: invitation.id,
        targetEmail: data.email,
        invitedBy: data.invitedBy,
        ...context
      })
      
      return { 
        success: true, 
        invitationToken: `${invitation.id}:${invitationToken}` // Include ID for lookup
      }
      
    } catch (error) {
      console.error("Admin invitation creation error:", error)
      await this.logSecurityEvent("ADMIN_INVITATION_ERROR", {
        error: error instanceof Error ? error.message : "Unknown error",
        targetEmail: data.email,
        ...context
      })
      return { success: false, error: "Failed to create admin invitation" }
    }
  }
  
  /**
   * Verify admin invitation token and allow account creation
   */
  static async verifyAdminInvitation(
    invitationToken: string,
    context: SecurityContext
  ): Promise<{ 
    valid: boolean
    invitation?: any
    error?: string 
  }> {
    try {
      // Parse invitation token (format: invitationId:token)
      const [invitationId, token] = invitationToken.split(':')
      
      if (!invitationId || !token) {
        await this.logSecurityEvent("ADMIN_INVITATION_VERIFICATION_FAILED", {
          reason: "Invalid token format",
          token: invitationToken.substring(0, 8) + "...",
          ...context
        })
        return { valid: false, error: "Invalid invitation token format" }
      }
      
      // Find invitation
      const invitation = await db.adminInvitation.findUnique({
        where: { id: invitationId }
      })
      
      if (!invitation) {
        await this.logSecurityEvent("ADMIN_INVITATION_VERIFICATION_FAILED", {
          reason: "Invitation not found",
          invitationId,
          ...context
        })
        return { valid: false, error: "Invalid invitation" }
      }
      
      // Check expiration
      if (invitation.expiresAt < new Date()) {
        await this.logSecurityEvent("ADMIN_INVITATION_VERIFICATION_FAILED", {
          reason: "Invitation expired",
          invitationId,
          targetEmail: invitation.email,
          ...context
        })
        return { valid: false, error: "Invitation has expired" }
      }
      
      // Check if already used
      if (invitation.used) {
        await this.logSecurityEvent("ADMIN_INVITATION_VERIFICATION_FAILED", {
          reason: "Invitation already used",
          invitationId,
          targetEmail: invitation.email,
          ...context
        })
        return { valid: false, error: "Invitation has already been used" }
      }
      
      // Verify token
      const tokenValid = await compare(token, invitation.tokenHash)
      if (!tokenValid) {
        await this.logSecurityEvent("ADMIN_INVITATION_VERIFICATION_FAILED", {
          reason: "Invalid token",
          invitationId,
          targetEmail: invitation.email,
          ...context
        })
        return { valid: false, error: "Invalid invitation token" }
      }
      
      // Log successful verification
      await this.logSecurityEvent("ADMIN_INVITATION_VERIFIED", {
        invitationId,
        targetEmail: invitation.email,
        ...context
      })
      
      return { 
        valid: true, 
        invitation: {
          id: invitation.id,
          email: invitation.email,
          name: invitation.name,
          invitedBy: invitation.invitedBy
        }
      }
      
    } catch (error) {
      console.error("Admin invitation verification error:", error)
      await this.logSecurityEvent("ADMIN_INVITATION_VERIFICATION_ERROR", {
        error: error instanceof Error ? error.message : "Unknown error",
        token: invitationToken.substring(0, 8) + "...",
        ...context
      })
      return { valid: false, error: "Verification failed" }
    }
  }
  
  /**
   * Complete admin account creation with invitation
   */
  static async createAdminWithInvitation(
    invitationToken: string,
    accountData: {
      password: string
      confirmPassword?: string
    },
    context: SecurityContext
  ): Promise<{ 
    success: boolean
    user?: any
    error?: string 
  }> {
    try {
      // Verify invitation first
      const verification = await this.verifyAdminInvitation(invitationToken, context)
      if (!verification.valid) {
        return { success: false, error: verification.error }
      }
      
      const invitation = verification.invitation!
      
      // Validate password
      if (accountData.password !== accountData.confirmPassword) {
        return { success: false, error: "Passwords do not match" }
      }
      
      if (accountData.password.length < 12) {
        return { success: false, error: "Admin password must be at least 12 characters" }
      }
      
      // Create admin user
      const hashedPassword = await hash(accountData.password, 12)
      const user = await db.user.create({
        data: {
          name: invitation.name,
          email: invitation.email,
          password: hashedPassword,
          role: "SUPER_ADMIN",
          emailVerified: new Date(), // Pre-verified through invitation
        }
      })
      
      // Mark invitation as used
      await db.adminInvitation.update({
        where: { id: invitation.id },
        data: { 
          used: true, 
          usedAt: new Date(),
          createdUserId: user.id
        }
      })
      
      // Log admin creation
      await this.logSecurityEvent("ADMIN_ACCOUNT_CREATED", {
        userId: user.id,
        userEmail: user.email,
        invitationId: invitation.id,
        invitedBy: invitation.invitedBy,
        ...context
      })
      
      return { 
        success: true, 
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        }
      }
      
    } catch (error) {
      console.error("Admin account creation error:", error)
      await this.logSecurityEvent("ADMIN_ACCOUNT_CREATION_ERROR", {
        error: error instanceof Error ? error.message : "Unknown error",
        token: invitationToken.substring(0, 8) + "...",
        ...context
      })
      return { success: false, error: "Failed to create admin account" }
    }
  }
  
  /**
   * Generate secure random token
   */
  private static generateSecureToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }
  
  /**
   * Verify master key for initial admin setup
   */
  private static verifyMasterKey(providedKey: string): boolean {
    const masterKey = process.env.ADMIN_MASTER_KEY
    if (!masterKey) {
      console.error("ADMIN_MASTER_KEY not configured")
      return false
    }
    
    // Use constant-time comparison to prevent timing attacks
    const providedBuffer = Buffer.from(providedKey, 'utf8')
    const masterBuffer = Buffer.from(masterKey, 'utf8')
    
    if (providedBuffer.length !== masterBuffer.length) {
      return false
    }
    
    return crypto.timingSafeEqual(providedBuffer, masterBuffer)
  }
  
  /**
   * Send secure admin invitation email
   */
  private static async sendAdminInvitationEmail(
    data: AdminInvitationData,
    invitationToken: string
  ) {
    const invitationUrl = `${process.env.NEXTAUTH_URL}/auth/admin/signup?invitation=${invitationToken}`
    
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">🔐 Sourceflex Admin Invitation</h2>
        
        <div style="background: #fee2e2; border: 1px solid #fca5a5; padding: 16px; border-radius: 8px; margin: 20px 0;">
          <p><strong>⚠️ CONFIDENTIAL:</strong> You have been invited to create a SUPER_ADMIN account for Sourceflex.</p>
        </div>
        
        <p>Hello ${data.name},</p>
        
        <p>You have been invited by <strong>${data.invitedBy}</strong> to create a Super Administrator account for the Sourceflex platform.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${invitationUrl}" 
             style="background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Accept Admin Invitation
          </a>
        </div>
        
        <div style="background: #f3f4f6; padding: 16px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 10px 0; color: #374151;">Security Information:</h3>
          <ul style="margin: 0; padding-left: 20px;">
            <li>This invitation expires in 24 hours</li>
            <li>You will have full administrative access</li>
            <li>Your account will be logged and monitored</li>
            <li>Use a strong password (12+ characters)</li>
          </ul>
        </div>
        
        <div style="background: #fef3cd; border: 1px solid #fbbf24; padding: 16px; border-radius: 8px; margin: 20px 0;">
          <p><strong>⚠️ Security Warning:</strong></p>
          <ul style="margin: 10px 0; padding-left: 20px;">
            <li>Do not share this invitation link</li>
            <li>If you didn't expect this invitation, report it immediately</li>
            <li>Admin accounts have full system access</li>
          </ul>
        </div>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          This invitation was sent from: ${data.invitedBy}<br>
          For security concerns, contact your system administrator immediately.
        </p>
      </div>
    `
    
    await sendEmail({
      to: data.email,
      subject: "🔐 Sourceflex Admin Account Invitation - CONFIDENTIAL",
      html: emailHtml,
      text: `Admin Invitation for Sourceflex - You have been invited to create a Super Administrator account. Visit: ${invitationUrl}`
    })
  }
  
  /**
   * List pending admin invitations (for admin dashboard)
   */
  static async getPendingInvitations(): Promise<any[]> {
    return await db.adminInvitation.findMany({
      where: {
        used: false,
        expiresAt: { gt: new Date() }
      },
      select: {
        id: true,
        email: true,
        name: true,
        invitedBy: true,
        createdAt: true,
        expiresAt: true,
        ipAddress: true
      },
      orderBy: { createdAt: 'desc' }
    })
  }
  
  /**
   * Revoke admin invitation
   */
  static async revokeInvitation(
    invitationId: string,
    revokedBy: string,
    context: SecurityContext
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const invitation = await db.adminInvitation.findUnique({
        where: { id: invitationId }
      })
      
      if (!invitation) {
        return { success: false, error: "Invitation not found" }
      }
      
      await db.adminInvitation.update({
        where: { id: invitationId },
        data: { 
          used: true, // Mark as used to prevent future use
          revokedBy,
          revokedAt: new Date()
        }
      })
      
      await this.logSecurityEvent("ADMIN_INVITATION_REVOKED", {
        invitationId,
        targetEmail: invitation.email,
        revokedBy,
        ...context
      })
      
      return { success: true }
      
    } catch (error) {
      console.error("Invitation revocation error:", error)
      return { success: false, error: "Failed to revoke invitation" }
    }
  }
  
  /**
   * Clean up expired invitations
   */
  static async cleanupExpiredInvitations(): Promise<number> {
    const result = await db.adminInvitation.deleteMany({
      where: {
        expiresAt: { lt: new Date() },
        used: false
      }
    })
    
    await this.logSecurityEvent("ADMIN_INVITATIONS_CLEANUP", {
      deletedCount: result.count
    })
    
    return result.count
  }
  
  /**
   * Log security events
   */
  private static async logSecurityEvent(
    action: string,
    metadata: Record<string, any>
  ) {
    try {
      await createAuditLog(
        {
          userId: metadata.userId,
          userEmail: metadata.userEmail || metadata.targetEmail,
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
          sessionId: metadata.sessionId,
        },
        {
          action: action as any, // Will be added to enum
          resourceType: "AUTHENTICATION",
          resourceId: metadata.invitationId || metadata.userId,
          success: !action.includes("FAILED") && !action.includes("ERROR"),
          metadata: {
            ...metadata,
            timestamp: new Date().toISOString(),
            securityLevel: "CRITICAL" // Admin events are critical
          }
        }
      )
    } catch (error) {
      console.error("Failed to log admin security event:", error)
    }
  }
}