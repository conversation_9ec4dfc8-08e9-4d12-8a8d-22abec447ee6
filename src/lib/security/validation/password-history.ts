import { compare, hash } from "bcryptjs"
import { db } from "../../core/db"
import { AuditLogger } from "../monitoring/audit"

export interface PasswordHistoryEntry {
  id: string
  userId: string
  passwordHash: string
  createdAt: Date
}

export class PasswordHistoryManager {
  /**
   * Add a password to user's history
   */
  static async addPasswordToHistory(
    userId: string, 
    password: string,
    organizationId?: string
  ): Promise<void> {
    // Hash the password for storage
    const passwordHash = await hash(password, 12)

    // Store in verification token table with special identifier
    await db.verificationToken.create({
      data: {
        identifier: `password-history:${userId}`,
        token: passwordHash,
        expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      }
    })

    // Log password change
    const auditor = new AuditLogger({
      userId,
      organizationId,
    })

    await auditor.log({
      action: "PASSWORD_RESET_COMPLETED",
      resourceType: "AUTHENTICATION",
      resourceId: userId,
      details: { 
        passwordChanged: true,
        historyUpdated: true 
      },
      severity: "low",
    })

    // Clean up old history entries based on organization policy
    await this.cleanupOldPasswords(userId, organizationId)
  }

  /**
   * Check if password was used recently
   */
  static async isPasswordReused(
    userId: string, 
    password: string, 
    historyCount: number = 12
  ): Promise<boolean> {
    try {
      // Get recent password history
      const historyEntries = await db.verificationToken.findMany({
        where: {
          identifier: `password-history:${userId}`,
          expires: { gt: new Date() }
        },
        orderBy: { expires: 'desc' },
        take: historyCount
      })

      // Check each stored password hash
      for (const entry of historyEntries) {
        const isMatch = await compare(password, entry.token)
        if (isMatch) {
          return true
        }
      }

      return false
    } catch (error) {
      console.error("Password history check failed:", error)
      return false // Fail open for availability
    }
  }

  /**
   * Get password history count for a user
   */
  static async getPasswordHistoryCount(userId: string): Promise<number> {
    return await db.verificationToken.count({
      where: {
        identifier: `password-history:${userId}`,
        expires: { gt: new Date() }
      }
    })
  }

  /**
   * Clean up old password history entries
   */
  static async cleanupOldPasswords(
    userId: string, 
    organizationId?: string
  ): Promise<void> {
    // Get organization policy to determine how many passwords to keep
    let maxHistoryCount = 12 // Default

    if (organizationId) {
      try {
        const policy = await db.securityPolicy.findUnique({
          where: { organizationId }
        })
        maxHistoryCount = policy?.passwordHistoryCount || 12
      } catch (error) {
        console.error("Failed to get security policy for cleanup:", error)
      }
    }

    // Get all password history entries for user
    const allEntries = await db.verificationToken.findMany({
      where: {
        identifier: `password-history:${userId}`,
        expires: { gt: new Date() }
      },
      orderBy: { expires: 'desc' }
    })

    // Delete entries beyond the limit
    if (allEntries.length > maxHistoryCount) {
      const entriesToDelete = allEntries.slice(maxHistoryCount)
      
      await db.verificationToken.deleteMany({
        where: {
          identifier: `password-history:${userId}`,
          token: {
            in: entriesToDelete.map(entry => entry.token)
          }
        }
      })
    }
  }

  /**
   * Clear all password history for a user (admin action)
   */
  static async clearPasswordHistory(
    userId: string,
    adminUserId: string,
    organizationId?: string
  ): Promise<void> {
    // Delete all password history
    await db.verificationToken.deleteMany({
      where: {
        identifier: `password-history:${userId}`
      }
    })

    // Log admin action
    const auditor = new AuditLogger({
      userId: adminUserId,
      organizationId,
    })

    await auditor.log({
      action: "USER_UPDATED",
      resourceType: "USER",
      resourceId: userId,
      details: { 
        action: "password_history_cleared",
        targetUserId: userId,
        reason: "admin_action"
      },
      severity: "medium",
    })
  }

  /**
   * Check if user's password needs to be changed due to expiry
   */
  static async isPasswordExpired(userId: string): Promise<{
    expired: boolean
    daysUntilExpiry?: number
    lastChanged?: Date
  }> {
    try {
      // Get user's organization policy
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { 
          organizationId: true,
          updatedAt: true // Use as proxy for last password change
        }
      })

      if (!user || !user.organizationId) {
        return { expired: false }
      }

      const policy = await db.securityPolicy.findUnique({
        where: { organizationId: user.organizationId }
      })

      // No password expiry policy
      if (!policy || !policy.passwordExpiryDays) {
        return { expired: false }
      }

      // Get last password change from audit log
      const lastPasswordChange = await db.auditLog.findFirst({
        where: {
          userId,
          action: "PASSWORD_RESET_COMPLETED"
        },
        orderBy: { createdAt: 'desc' }
      })

      const lastChanged = lastPasswordChange?.createdAt || user.updatedAt
      const expiryDate = new Date(lastChanged.getTime() + (policy.passwordExpiryDays * 24 * 60 * 60 * 1000))
      const now = new Date()

      const expired = now > expiryDate
      const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))

      return {
        expired,
        daysUntilExpiry: expired ? 0 : daysUntilExpiry,
        lastChanged
      }
    } catch (error) {
      console.error("Password expiry check failed:", error)
      return { expired: false }
    }
  }

  /**
   * Get users with expiring passwords for notifications
   */
  static async getUsersWithExpiringPasswords(
    organizationId: string,
    daysBefore: number = 7
  ): Promise<Array<{
    userId: string
    email: string
    name: string | null
    daysUntilExpiry: number
    lastChanged: Date
  }>> {
    const policy = await db.securityPolicy.findUnique({
      where: { organizationId }
    })

    if (!policy || !policy.passwordExpiryDays) {
      return []
    }

    // Get users in organization
    const users = await db.user.findMany({
      where: { 
        organizationId,
        emailVerified: { not: null } // Only notify verified users
      },
      select: {
        id: true,
        email: true,
        name: true,
        updatedAt: true
      }
    })

    const expiringUsers = []

    for (const user of users) {
      const expiryInfo = await this.isPasswordExpired(user.id)
      
      if (!expiryInfo.expired && 
          expiryInfo.daysUntilExpiry !== undefined && 
          expiryInfo.daysUntilExpiry <= daysBefore) {
        expiringUsers.push({
          userId: user.id,
          email: user.email,
          name: user.name,
          daysUntilExpiry: expiryInfo.daysUntilExpiry,
          lastChanged: expiryInfo.lastChanged!
        })
      }
    }

    return expiringUsers
  }

  /**
   * Cleanup expired password history entries (maintenance job)
   */
  static async cleanupExpiredHistory(): Promise<number> {
    const result = await db.verificationToken.deleteMany({
      where: {
        identifier: { startsWith: "password-history:" },
        expires: { lt: new Date() }
      }
    })

    return result.count
  }
}