import { PasswordSecurityService } from "../../core/password-security"
import { SecurityPolicyService } from "../../core/security-policy"
import { <PERSON>tLogger } from "../monitoring/audit"

export interface EnhancedPasswordPolicy {
  // Basic requirements
  minLength: number
  maxLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  
  // Advanced requirements
  minSpecialChars?: number
  minNumbers?: number
  minUppercase?: number
  minLowercase?: number
  
  // Pattern restrictions
  allowConsecutiveChars?: boolean
  maxConsecutiveChars?: number
  allowRepeatingChars?: boolean
  maxRepeatingChars?: number
  allowKeyboardSequences?: boolean
  allowCommonPasswords?: boolean
  
  // Context restrictions
  allowUsernameInPassword?: boolean
  allowEmailInPassword?: boolean
  allowPersonalInfoInPassword?: boolean
  
  // Advanced security
  requirePasswordStrengthScore?: number
  checkBreachedPasswords?: boolean
  
  // History and expiration
  passwordHistoryCount?: number
  passwordExpiryDays?: number
  
  // Organization-specific
  allowOrganizationName?: boolean
  customBannedWords?: string[]
}

export interface PasswordValidationContext {
  userId?: string
  username?: string
  email?: string
  organizationId?: string
  organizationName?: string
  userRole?: string
  firstName?: string
  lastName?: string
}

export interface EnhancedPasswordValidationResult {
  isValid: boolean
  score: number
  strength: "weak" | "fair" | "good" | "strong" | "very-strong"
  errors: string[]
  warnings: string[]
  suggestions: string[]
  breachInfo?: {
    isBreached: boolean
    breachCount?: number
  }
  entropy: number
  estimatedCrackTime: string
}

export class EnhancedPasswordValidator {
  private static readonly DEFAULT_POLICY: EnhancedPasswordPolicy = {
    minLength: 12,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    minSpecialChars: 1,
    minNumbers: 1,
    minUppercase: 1,
    minLowercase: 1,
    allowConsecutiveChars: false,
    maxConsecutiveChars: 2,
    allowRepeatingChars: false,
    maxRepeatingChars: 2,
    allowKeyboardSequences: false,
    allowCommonPasswords: false,
    allowUsernameInPassword: false,
    allowEmailInPassword: false,
    allowPersonalInfoInPassword: false,
    requirePasswordStrengthScore: 70,
    checkBreachedPasswords: true,
    passwordHistoryCount: 12,
    passwordExpiryDays: 90,
    allowOrganizationName: false,
    customBannedWords: []
  }

  /**
   * Enhanced password validation with comprehensive checks
   */
  static async validatePassword(
    password: string,
    policy: Partial<EnhancedPasswordPolicy> = {},
    context: PasswordValidationContext = {}
  ): Promise<EnhancedPasswordValidationResult> {
    const mergedPolicy = { ...this.DEFAULT_POLICY, ...policy }
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []

    // Basic length validation
    if (password.length < mergedPolicy.minLength) {
      errors.push(`Password must be at least ${mergedPolicy.minLength} characters long`)
    }
    if (password.length > mergedPolicy.maxLength) {
      errors.push(`Password cannot exceed ${mergedPolicy.maxLength} characters`)
    }

    // Character type validation
    const hasUppercase = /[A-Z]/.test(password)
    const hasLowercase = /[a-z]/.test(password)
    const hasNumbers = /\d/.test(password)
    const specialCharRegex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]/
    const hasSpecialChars = specialCharRegex.test(password)

    if (mergedPolicy.requireUppercase && !hasUppercase) {
      errors.push("Password must contain at least one uppercase letter")
    }
    if (mergedPolicy.requireLowercase && !hasLowercase) {
      errors.push("Password must contain at least one lowercase letter")
    }
    if (mergedPolicy.requireNumbers && !hasNumbers) {
      errors.push("Password must contain at least one number")
    }
    if (mergedPolicy.requireSpecialChars && !hasSpecialChars) {
      errors.push("Password must contain at least one special character")
    }

    // Advanced character count validation
    if (mergedPolicy.minSpecialChars) {
      const specialCharCount = (password.match(specialCharRegex) || []).length
      if (specialCharCount < mergedPolicy.minSpecialChars) {
        errors.push(`Password must contain at least ${mergedPolicy.minSpecialChars} special characters`)
      }
    }

    if (mergedPolicy.minNumbers) {
      const numberCount = (password.match(/\d/g) || []).length
      if (numberCount < mergedPolicy.minNumbers) {
        errors.push(`Password must contain at least ${mergedPolicy.minNumbers} numbers`)
      }
    }

    if (mergedPolicy.minUppercase) {
      const uppercaseCount = (password.match(/[A-Z]/g) || []).length
      if (uppercaseCount < mergedPolicy.minUppercase) {
        errors.push(`Password must contain at least ${mergedPolicy.minUppercase} uppercase letters`)
      }
    }

    if (mergedPolicy.minLowercase) {
      const lowercaseCount = (password.match(/[a-z]/g) || []).length
      if (lowercaseCount < mergedPolicy.minLowercase) {
        errors.push(`Password must contain at least ${mergedPolicy.minLowercase} lowercase letters`)
      }
    }

    // Pattern validation
    if (!mergedPolicy.allowConsecutiveChars && this.hasConsecutiveChars(password, mergedPolicy.maxConsecutiveChars || 2)) {
      errors.push(`Password cannot contain more than ${mergedPolicy.maxConsecutiveChars} consecutive identical characters`)
    }

    if (!mergedPolicy.allowRepeatingChars && this.hasRepeatingPatterns(password, mergedPolicy.maxRepeatingChars || 2)) {
      errors.push("Password contains too many repeating patterns")
    }

    if (!mergedPolicy.allowKeyboardSequences && this.hasKeyboardSequences(password)) {
      errors.push("Password cannot contain keyboard sequences (e.g., 'qwerty', '123456')")
    }

    if (!mergedPolicy.allowCommonPasswords && await this.isCommonPassword(password)) {
      errors.push("Password is too common. Please choose a more unique password")
    }

    // Context-based validation
    if (!mergedPolicy.allowUsernameInPassword && context.username && this.containsSubstring(password, context.username)) {
      errors.push("Password cannot contain your username")
    }

    if (!mergedPolicy.allowEmailInPassword && context.email && this.containsEmailParts(password, context.email)) {
      errors.push("Password cannot contain parts of your email address")
    }

    if (!mergedPolicy.allowPersonalInfoInPassword && this.containsPersonalInfo(password, context)) {
      errors.push("Password cannot contain personal information like your name")
    }

    if (!mergedPolicy.allowOrganizationName && context.organizationName && 
        this.containsSubstring(password, context.organizationName)) {
      errors.push("Password cannot contain your organization name")
    }

    // Custom banned words
    if (mergedPolicy.customBannedWords && mergedPolicy.customBannedWords.length > 0) {
      const bannedWord = mergedPolicy.customBannedWords.find(word => 
        password.toLowerCase().includes(word.toLowerCase())
      )
      if (bannedWord) {
        errors.push("Password contains a word that is not allowed by organization policy")
      }
    }

    // Get password strength analysis
    const strengthAnalysis = PasswordSecurityService.analyzePasswordStrength(password)
    const entropy = this.calculateEntropy(password)
    const crackTime = this.estimateCrackTime(entropy)

    // Strength score validation
    if (mergedPolicy.requirePasswordStrengthScore && strengthAnalysis.score < mergedPolicy.requirePasswordStrengthScore) {
      errors.push(`Password strength score (${strengthAnalysis.score}/100) is below required minimum (${mergedPolicy.requirePasswordStrengthScore})`)
    }

    // Breach checking
    let breachInfo
    if (mergedPolicy.checkBreachedPasswords) {
      breachInfo = await PasswordSecurityService.checkPasswordBreach(password)
      if (breachInfo.isBreached) {
        errors.push(`This password has been found in ${breachInfo.breachCount} data breaches and must not be used`)
      }
    }

    // Generate suggestions
    if (strengthAnalysis.feedback.length > 0) {
      suggestions.push(...strengthAnalysis.feedback)
    }

    if (entropy < 50) {
      suggestions.push("Consider using a longer password with more character variety")
    }

    if (!hasSpecialChars) {
      suggestions.push("Add special characters like !@#$%^&*")
    }

    const strength = this.getStrengthLevel(strengthAnalysis.score)

    // Add warnings for borderline cases
    if (strengthAnalysis.score >= 50 && strengthAnalysis.score < 70) {
      warnings.push("Password strength is acceptable but could be improved")
    }

    if (entropy < 40) {
      warnings.push("Password entropy is low - consider making it more unpredictable")
    }

    return {
      isValid: errors.length === 0,
      score: strengthAnalysis.score,
      strength,
      errors,
      warnings,
      suggestions,
      breachInfo,
      entropy,
      estimatedCrackTime: crackTime
    }
  }

  /**
   * Get organization-specific password policy
   */
  static async getOrganizationPasswordPolicy(organizationId: string): Promise<EnhancedPasswordPolicy> {
    const securityPolicy = await SecurityPolicyService.getPolicy(organizationId)
    
    return {
      ...this.DEFAULT_POLICY,
      minLength: securityPolicy.minPasswordLength,
      requireUppercase: securityPolicy.requireUppercase,
      requireLowercase: securityPolicy.requireLowercase,
      requireNumbers: securityPolicy.requireNumbers,
      requireSpecialChars: securityPolicy.requireSpecialChars,
      passwordHistoryCount: securityPolicy.passwordHistoryCount,
      passwordExpiryDays: securityPolicy.passwordExpiryDays || undefined,
    }
  }

  /**
   * Log password validation attempts for audit
   */
  static async logPasswordValidation(
    context: PasswordValidationContext & { result: EnhancedPasswordValidationResult },
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    const auditor = new AuditLogger({
      userId: context.userId,
      ipAddress,
      userAgent,
      organizationId: context.organizationId,
    })

    await auditor.log({
      action: context.result.isValid ? "PASSWORD_VALIDATION_SUCCESS" : "PASSWORD_VALIDATION_FAILED",
      resourceType: "AUTHENTICATION",
      resourceId: context.userId || "unknown",
      details: {
        score: context.result.score,
        strength: context.result.strength,
        entropy: context.result.entropy,
        errors: context.result.errors,
        warnings: context.result.warnings,
        breached: context.result.breachInfo?.isBreached,
      },
      severity: context.result.isValid ? "low" : "medium",
    })
  }

  // Private helper methods
  private static hasConsecutiveChars(password: string, maxConsecutive: number): boolean {
    let consecutiveCount = 1
    for (let i = 1; i < password.length; i++) {
      if (password[i] === password[i - 1]) {
        consecutiveCount++
        if (consecutiveCount > maxConsecutive) return true
      } else {
        consecutiveCount = 1
      }
    }
    return false
  }

  private static hasRepeatingPatterns(password: string, maxRepeating: number): boolean {
    // Check for patterns like "abab" or "123123"
    for (let patternLength = 2; patternLength <= Math.floor(password.length / 2); patternLength++) {
      for (let i = 0; i <= password.length - patternLength * 2; i++) {
        const pattern = password.slice(i, i + patternLength)
        let repetitions = 1
        let nextIndex = i + patternLength
        
        while (nextIndex + patternLength <= password.length && 
               password.slice(nextIndex, nextIndex + patternLength) === pattern) {
          repetitions++
          nextIndex += patternLength
          if (repetitions > maxRepeating) return true
        }
      }
    }
    return false
  }

  private static hasKeyboardSequences(password: string): boolean {
    const sequences = [
      "qwertyuiop", "asdfghjkl", "zxcvbnm",
      "1234567890", "0987654321",
      "abcdefghijklmnopqrstuvwxyz", "zyxwvutsrqponmlkjihgfedcba",
      "qwerty", "asdf", "zxcv", "1234", "4321"
    ]
    
    const lowerPassword = password.toLowerCase()
    return sequences.some(seq => {
      for (let i = 0; i <= seq.length - 4; i++) {
        if (lowerPassword.includes(seq.slice(i, i + 4))) {
          return true
        }
      }
      return false
    })
  }

  private static async isCommonPassword(password: string): Promise<boolean> {
    const commonPasswords = [
      "password", "123456", "123456789", "12345678", "12345", "1234567",
      "password123", "admin", "qwerty", "abc123", "Password1", "welcome",
      "monkey", "dragon", "letmein", "trustno1", "sunshine", "iloveyou",
      "princess", "adobe123", "123123", "welcome123", "login", "guest",
      "hello", "zaq1zaq1", "password1", "qwerty123", "solo", "1qaz2wsx"
    ]
    
    const lowerPassword = password.toLowerCase()
    return commonPasswords.includes(lowerPassword)
  }

  private static containsSubstring(password: string, substring: string): boolean {
    if (!substring || substring.length < 3) return false
    return password.toLowerCase().includes(substring.toLowerCase())
  }

  private static containsEmailParts(password: string, email: string): boolean {
    if (!email) return false
    
    const [localPart, domain] = email.split('@')
    const lowerPassword = password.toLowerCase()
    
    return (localPart && lowerPassword.includes(localPart.toLowerCase())) ||
           (domain && lowerPassword.includes(domain.toLowerCase().split('.')[0]))
  }

  private static containsPersonalInfo(password: string, context: PasswordValidationContext): boolean {
    const lowerPassword = password.toLowerCase()
    const personalInfo = [
      context.firstName,
      context.lastName,
      context.username
    ].filter(Boolean)

    return personalInfo.some(info => 
      info && info.length >= 3 && lowerPassword.includes(info.toLowerCase())
    )
  }

  private static calculateEntropy(password: string): number {
    const charset = new Set(password).size
    return Math.log2(Math.pow(charset, password.length))
  }

  private static estimateCrackTime(entropy: number): string {
    // Rough estimation based on entropy
    // Assumes 1 billion guesses per second
    const seconds = Math.pow(2, entropy - 1) / 1000000000
    
    if (seconds < 1) return "Instantly"
    if (seconds < 60) return "Less than a minute"
    if (seconds < 3600) return `${Math.round(seconds / 60)} minutes`
    if (seconds < 86400) return `${Math.round(seconds / 3600)} hours`
    if (seconds < 31536000) return `${Math.round(seconds / 86400)} days`
    if (seconds < 31536000000) return `${Math.round(seconds / 31536000)} years`
    return "Centuries"
  }

  private static getStrengthLevel(score: number): "weak" | "fair" | "good" | "strong" | "very-strong" {
    if (score < 30) return "weak"
    if (score < 50) return "fair" 
    if (score < 70) return "good"
    if (score < 90) return "strong"
    return "very-strong"
  }
}