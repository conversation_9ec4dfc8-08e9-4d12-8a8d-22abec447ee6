/**
 * Security configuration validator to ensure all required security secrets
 * are properly configured before the application starts
 */

export interface SecurityConfig {
  SESSION_HMAC_SECRET: string
  NEXTAUTH_SECRET: string
  DATABASE_URL: string
  EMAIL_FROM?: string
  NEXTAUTH_URL: string
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  config?: SecurityConfig
}

export class SecurityConfigValidator {
  private static readonly REQUIRED_SECRETS = [
    'SESSION_HMAC_SECRET',
    'NEXTAUTH_SECRET', 
    'DATABASE_URL',
    'NEXTAUTH_URL'
  ] as const

  private static readonly MINIMUM_SECRET_LENGTHS = {
    SESSION_HMAC_SECRET: 32,
    NEXTAUTH_SECRET: 32
  } as const

  private static readonly SECRET_PATTERNS = {
    // Check for common weak patterns
    WEAK_PATTERNS: [
      /^(test|dev|demo|example|secret|password|123|abc)/i,
      /^(.)\1{5,}$/, // Repeated characters
      /^(012|abc|qwe|asd)/i // Common keyboard patterns
    ],
    DATABASE_URL: /^postgresql:\/\/.+/,
    NEXTAUTH_URL: /^https?:\/\/.+/
  }

  /**
   * Validate all security-related environment variables
   */
  static validate(): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    const config: Partial<SecurityConfig> = {}

    // Check required secrets exist and meet minimum requirements
    for (const secretName of this.REQUIRED_SECRETS) {
      const value = process.env[secretName]
      
      if (!value) {
        errors.push(`${secretName} environment variable is required but not set`)
        continue
      }

      config[secretName] = value

      // Check minimum length requirements
      const minLength = this.MINIMUM_SECRET_LENGTHS[secretName as keyof typeof this.MINIMUM_SECRET_LENGTHS]
      if (minLength && value.length < minLength) {
        errors.push(`${secretName} must be at least ${minLength} characters long (current: ${value.length})`)
      }

      // Check for weak secret patterns (except DATABASE_URL and URLs)
      if (secretName.includes('SECRET') || secretName.includes('HMAC')) {
        const isWeak = this.SECRET_PATTERNS.WEAK_PATTERNS.some(pattern => pattern.test(value))
        if (isWeak) {
          errors.push(`${secretName} appears to use a weak or predictable pattern`)
        }
      }
    }

    // Validate specific URL formats
    if (config.DATABASE_URL && !this.SECRET_PATTERNS.DATABASE_URL.test(config.DATABASE_URL)) {
      errors.push('DATABASE_URL must be a valid PostgreSQL connection string')
    }

    if (config.NEXTAUTH_URL && !this.SECRET_PATTERNS.NEXTAUTH_URL.test(config.NEXTAUTH_URL)) {
      errors.push('NEXTAUTH_URL must be a valid HTTP/HTTPS URL')
    }

    // Production-specific validations
    if (process.env.NODE_ENV === 'production') {
      this.validateProductionConfig(config, errors, warnings)
    }

    // Development warnings
    if (process.env.NODE_ENV === 'development') {
      warnings.push('Running in development mode - some security validations are relaxed')
      
      if (config.NEXTAUTH_URL?.startsWith('http://')) {
        warnings.push('NEXTAUTH_URL uses HTTP in development - ensure HTTPS is used in production')
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      config: errors.length === 0 ? config as SecurityConfig : undefined
    }
  }

  /**
   * Production-specific security validations
   */
  private static validateProductionConfig(
    config: Partial<SecurityConfig>,
    errors: string[],
    warnings: string[]
  ): void {
    // HTTPS requirement in production
    if (config.NEXTAUTH_URL && !config.NEXTAUTH_URL.startsWith('https://')) {
      errors.push('NEXTAUTH_URL must use HTTPS in production')
    }

    // Database SSL requirement for production
    if (config.DATABASE_URL && !config.DATABASE_URL.includes('sslmode=require')) {
      warnings.push('DATABASE_URL should include sslmode=require in production')
    }

    // Check for localhost/development URLs in production
    if (config.NEXTAUTH_URL && (
      config.NEXTAUTH_URL.includes('localhost') || 
      config.NEXTAUTH_URL.includes('127.0.0.1') ||
      config.NEXTAUTH_URL.includes('.local')
    )) {
      errors.push('NEXTAUTH_URL cannot use localhost or local domains in production')
    }
  }

  /**
   * Generate secure secrets for development/testing
   */
  static generateSecureSecret(length: number = 32): string {
    const crypto = require('crypto')
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * Validate and initialize security configuration
   * Call this during application startup
   */
  static validateAndInit(): SecurityConfig {
    const result = this.validate()

    // Log warnings
    if (result.warnings.length > 0) {
      console.warn('🟡 Security Configuration Warnings:')
      result.warnings.forEach(warning => console.warn(`  - ${warning}`))
    }

    // Fail fast on errors
    if (!result.valid) {
      console.error('❌ Security Configuration Errors:')
      result.errors.forEach(error => console.error(`  - ${error}`))
      
      if (process.env.NODE_ENV === 'development') {
        console.error('\n💡 Development Help:')
        console.error('  Generate secure secrets with:')
        console.error(`  SESSION_HMAC_SECRET="${this.generateSecureSecret()}"`)
        console.error(`  NEXTAUTH_SECRET="${this.generateSecureSecret()}"`)
      }
      
      throw new Error('Security configuration validation failed. Application cannot start with insecure configuration.')
    }

    console.log('✅ Security configuration validated successfully')
    return result.config!
  }

  /**
   * Runtime security check - validates secrets are still properly configured
   */
  static runtimeSecurityCheck(): boolean {
    try {
      const result = this.validate()
      return result.valid
    } catch {
      return false
    }
  }

  /**
   * Get obfuscated configuration info for debugging (without exposing secrets)
   */
  static getObfuscatedConfig(): Record<string, string> {
    const result: Record<string, string> = {}
    
    for (const secretName of this.REQUIRED_SECRETS) {
      const value = process.env[secretName]
      if (value) {
        if (secretName.includes('SECRET') || secretName.includes('HMAC')) {
          result[secretName] = `${value.substring(0, 4)}...${value.substring(value.length - 4)} (${value.length} chars)`
        } else if (secretName === 'DATABASE_URL') {
          const url = new URL(value)
          result[secretName] = `${url.protocol}//${url.hostname}:${url.port}/${url.pathname.substring(1)}`
        } else {
          result[secretName] = value
        }
      } else {
        result[secretName] = 'NOT SET'
      }
    }
    
    return result
  }
}