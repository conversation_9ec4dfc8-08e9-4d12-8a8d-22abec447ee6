import {
  generateRegistrationOptions,
  verifyRegistrationResponse,
  generateAuthenticationOptions,
  verifyAuthenticationResponse,
} from "@simplewebauthn/server"
import type {
  GenerateRegistrationOptionsOpts,
  GenerateAuthenticationOptionsOpts,
  VerifyRegistrationResponseOpts,
  VerifyAuthenticationResponseOpts,
} from "@simplewebauthn/server"

// Define response types locally since they're not exported
export interface RegistrationResponseJSON {
  id: string
  rawId: string
  response: {
    clientDataJSON: string
    attestationObject: string
  }
  type: "public-key"
}

export interface AuthenticationResponseJSON {
  id: string
  rawId: string
  response: {
    clientDataJSON: string
    authenticatorData: string
    signature: string
    userHandle?: string
  }
  clientExtensionResults?: any
  type: "public-key"
}
import { db } from "../../core/db"
import { env } from "../../core/env"

// WebAuthn configuration
const rpName = "Sourceflex"
const rpID = env.NEXTAUTH_URL ? new URL(env.NEXTAUTH_URL).hostname : "localhost"
const origin = env.NEXTAUTH_URL || "http://localhost:3000"

export interface WebAuthnRegistrationResult {
  success: boolean
  credentialId?: string
  error?: string
}

export interface WebAuthnAuthenticationResult {
  success: boolean
  credentialId?: string
  error?: string
}

export class WebAuthnService {
  /**
   * Generate registration options for a new WebAuthn credential
   */
  static async generateRegistrationOptions(
    userId: string,
    userName: string,
    userEmail: string
  ): Promise<any> {
    // Get existing credentials for this user
    const existingCredentials = await db.webAuthnCredential.findMany({
      where: { userId },
      select: {
        credentialId: true,
        deviceType: true,
      },
    })

    const excludeCredentials = existingCredentials.map((cred) => ({
      id: Buffer.from(cred.credentialId, "base64"),
      type: "public-key" as const,
      transports: ["usb", "ble", "nfc", "internal"] as any,
    }))

    const options = await generateRegistrationOptions({
      rpName,
      rpID,
      userID: userId,
      userName: userEmail,
      userDisplayName: userName,
      timeout: 60000,
      attestationType: "none",
      excludeCredentials,
      authenticatorSelection: {
        authenticatorAttachment: "cross-platform", // Prefer external authenticators
        userVerification: "preferred",
        residentKey: "preferred",
      },
      supportedAlgorithmIDs: [-7, -257], // ES256 and RS256
    })

    // Store challenge temporarily (in production, use Redis or similar)
    await db.user.update({
      where: { id: userId },
      data: {
        // We'll store the challenge in a temporary field or use session storage
        // For this example, we'll use a simple approach
      },
    })

    return options
  }

  /**
   * Verify registration response and save credential
   */
  static async verifyRegistration(
    userId: string,
    response: RegistrationResponseJSON,
    expectedChallenge: string,
    deviceName?: string
  ): Promise<WebAuthnRegistrationResult> {
    try {
      const verification = await verifyRegistrationResponse({
        response,
        expectedChallenge,
        expectedOrigin: origin,
        expectedRPID: rpID,
        requireUserVerification: false,
      } as VerifyRegistrationResponseOpts)

      if (!verification.verified || !verification.registrationInfo) {
        return {
          success: false,
          error: "Registration verification failed",
        }
      }

      const { credentialID, credentialPublicKey, counter } = verification.registrationInfo

      // Check if credential already exists
      const existingCredential = await db.webAuthnCredential.findUnique({
        where: { credentialId: Buffer.from(credentialID).toString("base64") },
      })

      if (existingCredential) {
        return {
          success: false,
          error: "This authenticator is already registered",
        }
      }

      // Save the credential
      const credential = await db.webAuthnCredential.create({
        data: {
          userId,
          credentialId: Buffer.from(credentialID).toString("base64"),
          publicKey: Buffer.from(credentialPublicKey).toString("base64"),
          counter,
          deviceType: this.detectDeviceType(response),
          name: deviceName || this.generateDeviceName(response),
        },
      })

      // Log the registration
      await db.auditLog.create({
        data: {
          userId,
          action: "TWO_FACTOR_ENABLED",
          resourceType: "AUTHENTICATION",
          resourceId: credential.id,
          metadata: {
            method: "webauthn",
            deviceName: credential.name,
            deviceType: credential.deviceType,
          },
          success: true,
        },
      })

      return {
        success: true,
        credentialId: credential.id,
      }
    } catch (error) {
      console.error("WebAuthn registration error:", error)
      return {
        success: false,
        error: "Registration failed due to server error",
      }
    }
  }

  /**
   * Generate authentication options for WebAuthn login
   */
  static async generateAuthenticationOptions(
    userId?: string
  ): Promise<any> {
    // Get user's credentials if userId provided
    let allowCredentials: Array<{
      id: Buffer
      type: "public-key"
      transports: string[]
    }> = []

    if (userId) {
      const userCredentials = await db.webAuthnCredential.findMany({
        where: { userId },
        select: { credentialId: true },
      })

      allowCredentials = userCredentials.map((cred) => ({
        id: Buffer.from(cred.credentialId, "base64"),
        type: "public-key" as const,
        transports: ["usb", "ble", "nfc", "internal"],
      }))
    }

    const options = await generateAuthenticationOptions({
      timeout: 60000,
      allowCredentials: allowCredentials.length > 0 ? allowCredentials : undefined,
      userVerification: "preferred",
      rpID,
    } as GenerateAuthenticationOptionsOpts)

    return options
  }

  /**
   * Verify authentication response
   */
  static async verifyAuthentication(
    response: AuthenticationResponseJSON,
    expectedChallenge: string,
    userId?: string
  ): Promise<WebAuthnAuthenticationResult> {
    try {
      // Find the credential
      const credentialId = Buffer.from(response.id, "base64url").toString("base64")
      const credential = await db.webAuthnCredential.findUnique({
        where: { credentialId },
        include: { user: true },
      })

      if (!credential) {
        return {
          success: false,
          error: "Credential not found",
        }
      }

      // If userId is provided, verify it matches
      if (userId && credential.userId !== userId) {
        return {
          success: false,
          error: "Credential does not belong to user",
        }
      }

      const verification = await verifyAuthenticationResponse({
        response,
        expectedChallenge,
        expectedOrigin: origin,
        expectedRPID: rpID,
        authenticator: {
          credentialID: Buffer.from(credential.credentialId, "base64"),
          credentialPublicKey: Buffer.from(credential.publicKey, "base64"),
          counter: credential.counter,
        },
        requireUserVerification: false,
      } as VerifyAuthenticationResponseOpts)

      if (!verification.verified) {
        return {
          success: false,
          error: "Authentication verification failed",
        }
      }

      // Update counter
      await db.webAuthnCredential.update({
        where: { id: credential.id },
        data: {
          counter: verification.authenticationInfo.newCounter,
          lastUsedAt: new Date(),
        },
      })

      // Log successful authentication
      await db.auditLog.create({
        data: {
          userId: credential.userId,
          userEmail: credential.user.email,
          userName: credential.user.name,
          organizationId: credential.user.organizationId,
          action: "USER_LOGIN",
          resourceType: "AUTHENTICATION",
          resourceId: credential.id,
          metadata: {
            method: "webauthn",
            deviceName: credential.name,
            credentialId: credential.id,
          },
          success: true,
        },
      })

      return {
        success: true,
        credentialId: credential.id,
      }
    } catch (error) {
      console.error("WebAuthn authentication error:", error)
      return {
        success: false,
        error: "Authentication failed due to server error",
      }
    }
  }

  /**
   * Get user's WebAuthn credentials
   */
  static async getUserCredentials(userId: string) {
    return await db.webAuthnCredential.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        deviceType: true,
        createdAt: true,
        lastUsedAt: true,
      },
      orderBy: { createdAt: "desc" },
    })
  }

  /**
   * Remove a WebAuthn credential
   */
  static async removeCredential(
    credentialId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const credential = await db.webAuthnCredential.findFirst({
        where: {
          id: credentialId,
          userId,
        },
      })

      if (!credential) {
        return {
          success: false,
          error: "Credential not found",
        }
      }

      await db.webAuthnCredential.delete({
        where: { id: credentialId },
      })

      // Log the removal
      await db.auditLog.create({
        data: {
          userId,
          action: "TWO_FACTOR_DISABLED",
          resourceType: "AUTHENTICATION",
          resourceId: credentialId,
          metadata: {
            method: "webauthn",
            deviceName: credential.name,
            action: "credential_removed",
          },
          success: true,
        },
      })

      return { success: true }
    } catch (error) {
      console.error("Error removing WebAuthn credential:", error)
      return {
        success: false,
        error: "Failed to remove credential",
      }
    }
  }

  /**
   * Check if user has WebAuthn credentials
   */
  static async hasWebAuthnCredentials(userId: string): Promise<boolean> {
    const count = await db.webAuthnCredential.count({
      where: { userId },
    })
    return count > 0
  }

  /**
   * Detect device type from registration response
   */
  private static detectDeviceType(response: RegistrationResponseJSON): string {
    // This is a simplified detection based on the response
    // In practice, you might want more sophisticated detection
    const clientDataJSON = JSON.parse(
      Buffer.from(response.response.clientDataJSON, "base64url").toString()
    )

    // Check for platform authenticator hints
    if (clientDataJSON.clientExtensions?.devicePubKey) {
      return "platform"
    }

    return "cross-platform"
  }

  /**
   * Generate a device name from registration response
   */
  private static generateDeviceName(response: RegistrationResponseJSON): string {
    // This is a simple implementation
    // In practice, you might want to extract more information
    const timestamp = new Date().toLocaleDateString()
    const deviceType = this.detectDeviceType(response)
    
    return `${deviceType === "platform" ? "Built-in" : "Security Key"} - ${timestamp}`
  }

  /**
   * Clean up old challenges (if stored in database)
   */
  static async cleanupChallenges(): Promise<void> {
    // If you're storing challenges in the database, clean them up here
    // For this implementation, we're not storing them persistently
  }
}