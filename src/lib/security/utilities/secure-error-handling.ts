import { createHash } from "crypto"
import { <PERSON>tLogger } from "../monitoring/audit"

export interface SecurityErrorContext {
  userId?: string
  ipAddress?: string
  userAgent?: string
  organizationId?: string
}

export interface SecurityErrorOptions {
  includeHash?: boolean
  hashLength?: number
  logLevel?: "low" | "medium" | "high"
  publicMessage?: string
}

/**
 * Secure error handling utilities to prevent information disclosure
 * while maintaining comprehensive audit logging for security incidents
 */
export class SecureErrorHandler {
  
  /**
   * Create a safe error response that doesn't leak sensitive information
   */
  static createSafeError(
    originalError: string | Error,
    context: SecurityErrorContext,
    options: SecurityErrorOptions = {}
  ): { publicError: string; logData: any } {
    const errorMessage = originalError instanceof Error ? originalError.message : originalError
    const errorHash = options.includeHash ? this.createErrorHash(errorMessage, options.hashLength) : undefined
    
    // Generic public error message
    const publicError = options.publicMessage || "An error occurred. Please try again."
    
    // Detailed log data for internal tracking
    const logData = {
      errorHash,
      context,
      timestamp: new Date().toISOString(),
      severity: options.logLevel || "medium"
    }
    
    return { publicError, logData }
  }
  
  /**
   * Create a non-reversible hash of sensitive data for logging
   */
  static createErrorHash(data: string, length: number = 16): string {
    return createHash("sha256")
      .update(data)
      .digest("hex")
      .substring(0, length)
  }
  
  /**
   * Log security error with safe data handling
   */
  static async logSecurityError(
    action: string,
    error: string | Error,
    context: SecurityErrorContext,
    additionalData?: Record<string, any>
  ): Promise<void> {
    try {
      const auditor = new AuditLogger(context)
      const { logData } = this.createSafeError(error, context, { includeHash: true })
      
      await auditor.log({
        action: action as any,
        resourceType: "SECURITY",
        resourceId: logData.errorHash,
        details: {
          ...logData,
          ...additionalData,
          errorType: error instanceof Error ? error.constructor.name : "string"
        },
        severity: logData.severity as any
      })
    } catch (auditError) {
      // Fallback to console logging if audit system fails
      console.error("Security error audit failed:", {
        originalError: error instanceof Error ? error.message : error,
        auditError: auditError instanceof Error ? auditError.message : auditError,
        context
      })
    }
  }
  
  /**
   * Safe token logging - creates hash instead of storing partial tokens
   */
  static createTokenHash(token: string, prefix?: string): string {
    const hash = this.createErrorHash(token, 12)
    return prefix ? `${prefix}:${hash}` : hash
  }
  
  /**
   * Sanitize sensitive data from objects for logging
   */
  static sanitizeForLogging(data: Record<string, any>): Record<string, any> {
    const sensitiveKeys = [
      'password', 'token', 'secret', 'key', 'credential',
      'auth', 'session', 'cookie', 'authorization'
    ]
    
    const sanitized: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(data)) {
      const keyLower = key.toLowerCase()
      const isSensitive = sensitiveKeys.some(sensitive => keyLower.includes(sensitive))
      
      if (isSensitive && typeof value === 'string') {
        sanitized[key] = this.createTokenHash(value, key)
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeForLogging(value)
      } else {
        sanitized[key] = value
      }
    }
    
    return sanitized
  }
  
  /**
   * Create standardized error responses for API endpoints
   */
  static createAPIErrorResponse(
    statusCode: number,
    error: string | Error,
    context: SecurityErrorContext,
    customMessage?: string
  ): { status: number; body: any; logData: any } {
    const { publicError, logData } = this.createSafeError(error, context, {
      publicMessage: customMessage,
      includeHash: true,
      logLevel: statusCode >= 500 ? "high" : "medium"
    })
    
    return {
      status: statusCode,
      body: {
        error: publicError,
        timestamp: new Date().toISOString(),
        ...(process.env.NODE_ENV === 'development' && {
          debug: { errorHash: logData.errorHash }
        })
      },
      logData
    }
  }
}

/**
 * Decorator for secure error handling in async functions
 */
export function withSecureErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context: SecurityErrorContext,
  actionName: string
): T {
  return (async (...args: any[]) => {
    try {
      return await fn(...args)
    } catch (error) {
      await SecureErrorHandler.logSecurityError(
        actionName,
        error instanceof Error ? error : String(error),
        context
      )
      
      // Re-throw with safe message
      const { publicError } = SecureErrorHandler.createSafeError(error, context)
      throw new Error(publicError)
    }
  }) as T
}