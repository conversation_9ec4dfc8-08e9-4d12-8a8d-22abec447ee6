import { randomBytes, createHash, createHmac, timingSafeEqual } from "crypto"
import { db } from "../../core/db"
import { <PERSON>tLogger } from "../monitoring/audit"

export interface VerificationToken {
  token: string
  hashedToken: string
  expires: Date
  attemptCount: number
}

export interface VerificationResult {
  success: boolean
  error?: string
  remainingAttempts?: number
  lockoutMinutes?: number
  requiresNewToken?: boolean
}

export interface VerificationContext {
  ipAddress?: string
  userAgent?: string
  userId?: string
}

/**
 * Enhanced email verification security manager with cryptographic tokens,
 * brute force protection, and comprehensive rate limiting
 */
export class EmailVerificationSecurityManager {
  private static readonly TOKEN_LENGTH = 6
  private static readonly TOKEN_EXPIRY_MINUTES = 15
  private static readonly MAX_ATTEMPTS = 5
  private static readonly LOCKOUT_MINUTES = 30
  private static readonly MAX_DAILY_SENDS = 10
  private static readonly MIN_RESEND_MINUTES = 2
  private static readonly HMAC_SECRET = process.env.EMAIL_VERIFICATION_HMAC_SECRET || "fallback-secret"

  /**
   * Generate a cryptographically secure verification token
   */
  static generateSecureToken(): { token: string; hashedToken: string } {
    // Generate cryptographically secure random token
    const randomBuffer = randomBytes(4) // 32 bits for 6-digit code
    let token = ""
    
    // Convert to 6-digit code with even distribution
    for (let i = 0; i < this.TOKEN_LENGTH; i++) {
      const randomValue = randomBuffer.readUInt32BE(0) + i * 7919 // Prime offset
      token += (randomValue % 10).toString()
    }

    // Create HMAC hash for storage (prevents rainbow table attacks)
    const hashedToken = this.hashToken(token)

    return { token, hashedToken }
  }

  /**
   * Hash verification token for secure storage
   */
  private static hashToken(token: string): string {
    const hmac = createHmac("sha256", this.HMAC_SECRET)
    hmac.update(token)
    return hmac.digest("hex")
  }

  /**
   * Validate token using timing-safe comparison
   */
  private static validateTokenHash(token: string, storedHash: string): boolean {
    const computedHash = this.hashToken(token)
    const storedBuffer = Buffer.from(storedHash, "hex")
    const computedBuffer = Buffer.from(computedHash, "hex")
    
    if (storedBuffer.length !== computedBuffer.length) {
      return false
    }

    return timingSafeEqual(storedBuffer, computedBuffer)
  }

  /**
   * Create secure verification token with comprehensive tracking
   */
  static async createVerificationToken(
    email: string,
    context: VerificationContext
  ): Promise<{ success: boolean; token?: string; error?: string; waitMinutes?: number }> {
    const auditor = new AuditLogger({
      userEmail: email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      userId: context.userId
    })

    try {
      // Check daily send limit
      const dailyLimit = await this.checkDailySendLimit(email, context)
      if (!dailyLimit.canSend) {
        await auditor.log({
          action: "EMAIL_VERIFICATION_RATE_LIMITED",
          resourceType: "AUTHENTICATION",
          resourceId: email,
          details: { 
            reason: "daily_limit_exceeded",
            dailySends: dailyLimit.todaySends,
            limit: this.MAX_DAILY_SENDS
          },
          severity: "medium"
        })
        return { 
          success: false, 
          error: `Daily verification limit exceeded. Try again tomorrow.` 
        }
      }

      // Check resend timing
      const resendCheck = await this.checkResendTiming(email)
      if (!resendCheck.canResend) {
        await auditor.log({
          action: "EMAIL_VERIFICATION_TOO_FREQUENT",
          resourceType: "AUTHENTICATION",
          resourceId: email,
          details: { waitMinutes: resendCheck.waitMinutes },
          severity: "low"
        })
        return { 
          success: false, 
          error: `Please wait ${resendCheck.waitMinutes} minutes before requesting another code.`,
          waitMinutes: resendCheck.waitMinutes
        }
      }

      // Generate secure token
      const { token, hashedToken } = this.generateSecureToken()
      const expires = new Date(Date.now() + this.TOKEN_EXPIRY_MINUTES * 60 * 1000)

      // Clean up existing tokens
      await db.verificationToken.deleteMany({
        where: { identifier: email }
      })

      // Store hashed token
      await db.verificationToken.create({
        data: {
          identifier: email,
          token: hashedToken,
          expires,
          // Store metadata for security tracking
          metadata: {
            createdAt: new Date().toISOString(),
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            attemptCount: 0
          }
        }
      })

      await auditor.log({
        action: "EMAIL_VERIFICATION_TOKEN_CREATED",
        resourceType: "AUTHENTICATION",
        resourceId: email,
        details: { 
          expiresAt: expires.toISOString(),
          hashedToken: hashedToken.substring(0, 8) + "..." // Log partial hash for debugging
        },
        severity: "low"
      })

      return { success: true, token }

    } catch (error) {
      await auditor.log({
        action: "EMAIL_VERIFICATION_TOKEN_ERROR",
        resourceType: "AUTHENTICATION",
        resourceId: email,
        details: { error: error instanceof Error ? error.message : "Unknown error" },
        severity: "high"
      })

      return { 
        success: false, 
        error: "Failed to create verification token" 
      }
    }
  }

  /**
   * Verify token with comprehensive security checks
   */
  static async verifyToken(
    email: string,
    token: string,
    context: VerificationContext
  ): Promise<VerificationResult> {
    const auditor = new AuditLogger({
      userEmail: email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      userId: context.userId
    })

    try {
      // Find verification token
      const verificationRecord = await db.verificationToken.findFirst({
        where: { identifier: email }
      })

      if (!verificationRecord) {
        await auditor.log({
          action: "EMAIL_VERIFICATION_TOKEN_NOT_FOUND",
          resourceType: "AUTHENTICATION",
          resourceId: email,
          details: { providedToken: token },
          severity: "medium"
        })
        return { 
          success: false, 
          error: "No verification code found. Please request a new one.",
          requiresNewToken: true
        }
      }

      // Check if token has expired
      if (verificationRecord.expires < new Date()) {
        await this.cleanupExpiredToken(email)
        await auditor.log({
          action: "EMAIL_VERIFICATION_TOKEN_EXPIRED",
          resourceType: "AUTHENTICATION",
          resourceId: email,
          details: { 
            expiredAt: verificationRecord.expires.toISOString(),
            providedToken: token 
          },
          severity: "low"
        })
        return { 
          success: false, 
          error: "Verification code has expired. Please request a new one.",
          requiresNewToken: true
        }
      }

      // Check attempt count for brute force protection
      const metadata = verificationRecord.metadata as any || {}
      const attemptCount = (metadata.attemptCount || 0) + 1

      if (attemptCount > this.MAX_ATTEMPTS) {
        await this.lockoutToken(email)
        await auditor.log({
          action: "EMAIL_VERIFICATION_ATTEMPTS_EXCEEDED",
          resourceType: "AUTHENTICATION",
          resourceId: email,
          details: { 
            attemptCount,
            maxAttempts: this.MAX_ATTEMPTS,
            lockoutMinutes: this.LOCKOUT_MINUTES
          },
          severity: "high"
        })
        return { 
          success: false, 
          error: `Too many failed attempts. Please wait ${this.LOCKOUT_MINUTES} minutes and request a new code.`,
          lockoutMinutes: this.LOCKOUT_MINUTES,
          requiresNewToken: true
        }
      }

      // Validate token with timing-safe comparison
      const isValid = this.validateTokenHash(token, verificationRecord.token)

      // Update attempt count regardless of result
      await db.verificationToken.update({
        where: { id: verificationRecord.id },
        data: {
          metadata: {
            ...metadata,
            attemptCount,
            lastAttemptAt: new Date().toISOString(),
            lastAttemptIP: context.ipAddress
          }
        }
      })

      if (!isValid) {
        const remainingAttempts = this.MAX_ATTEMPTS - attemptCount
        
        await auditor.log({
          action: "EMAIL_VERIFICATION_INVALID_TOKEN",
          resourceType: "AUTHENTICATION",
          resourceId: email,
          details: { 
            attemptCount,
            remainingAttempts,
            providedToken: token
          },
          severity: remainingAttempts <= 1 ? "high" : "medium"
        })

        return { 
          success: false, 
          error: `Invalid verification code. ${remainingAttempts} attempts remaining.`,
          remainingAttempts 
        }
      }

      // Token is valid - verify email and cleanup
      await db.user.update({
        where: { email },
        data: { emailVerified: new Date() }
      })

      await db.verificationToken.deleteMany({
        where: { identifier: email }
      })

      await auditor.log({
        action: "EMAIL_VERIFICATION_SUCCESS",
        resourceType: "AUTHENTICATION",
        resourceId: email,
        details: { attemptCount },
        severity: "low"
      })

      return { success: true }

    } catch (error) {
      await auditor.log({
        action: "EMAIL_VERIFICATION_ERROR",
        resourceType: "AUTHENTICATION",
        resourceId: email,
        details: { 
          error: error instanceof Error ? error.message : "Unknown error",
          providedToken: token 
        },
        severity: "high"
      })

      return { 
        success: false, 
        error: "Verification failed due to system error" 
      }
    }
  }

  /**
   * Check daily send limits to prevent abuse
   */
  private static async checkDailySendLimit(
    email: string, 
    context: VerificationContext
  ): Promise<{ canSend: boolean; todaySends: number }> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Count verification emails sent today from audit logs
    const todaySends = await db.auditLog.count({
      where: {
        userEmail: email,
        action: "EMAIL_VERIFICATION_TOKEN_CREATED",
        createdAt: { gte: today }
      }
    })

    return {
      canSend: todaySends < this.MAX_DAILY_SENDS,
      todaySends
    }
  }

  /**
   * Check timing constraints for resending verification codes
   */
  private static async checkResendTiming(email: string): Promise<{ canResend: boolean; waitMinutes?: number }> {
    const lastSend = await db.auditLog.findFirst({
      where: {
        userEmail: email,
        action: "EMAIL_VERIFICATION_TOKEN_CREATED"
      },
      orderBy: { createdAt: "desc" }
    })

    if (!lastSend) {
      return { canResend: true }
    }

    const timeSinceLastSend = Date.now() - lastSend.createdAt.getTime()
    const minWaitTime = this.MIN_RESEND_MINUTES * 60 * 1000

    if (timeSinceLastSend >= minWaitTime) {
      return { canResend: true }
    }

    const waitMinutes = Math.ceil((minWaitTime - timeSinceLastSend) / (60 * 1000))
    return { canResend: false, waitMinutes }
  }

  /**
   * Lockout token after too many failed attempts
   */
  private static async lockoutToken(email: string): Promise<void> {
    await db.verificationToken.deleteMany({
      where: { identifier: email }
    })

    // Create lockout record (using expired token as marker)
    const lockoutUntil = new Date(Date.now() + this.LOCKOUT_MINUTES * 60 * 1000)
    
    await db.verificationToken.create({
      data: {
        identifier: email,
        token: "LOCKED_OUT",
        expires: lockoutUntil,
        metadata: {
          type: "lockout",
          lockedAt: new Date().toISOString(),
          lockoutMinutes: this.LOCKOUT_MINUTES
        }
      }
    })
  }

  /**
   * Clean up expired tokens
   */
  private static async cleanupExpiredToken(email: string): Promise<void> {
    await db.verificationToken.deleteMany({
      where: { 
        identifier: email,
        expires: { lt: new Date() }
      }
    })
  }

  /**
   * Get verification status for email
   */
  static async getVerificationStatus(email: string): Promise<{
    hasActiveToken: boolean
    isLocked: boolean
    expiresAt?: Date
    attemptsRemaining?: number
    lockoutMinutes?: number
  }> {
    const token = await db.verificationToken.findFirst({
      where: { identifier: email }
    })

    if (!token) {
      return { hasActiveToken: false, isLocked: false }
    }

    // Check if this is a lockout record
    const metadata = token.metadata as any || {}
    if (metadata.type === "lockout") {
      if (token.expires > new Date()) {
        const lockoutMinutes = Math.ceil((token.expires.getTime() - Date.now()) / (60 * 1000))
        return { 
          hasActiveToken: false, 
          isLocked: true, 
          lockoutMinutes 
        }
      } else {
        // Lockout expired, clean up
        await this.cleanupExpiredToken(email)
        return { hasActiveToken: false, isLocked: false }
      }
    }

    // Regular token
    if (token.expires < new Date()) {
      await this.cleanupExpiredToken(email)
      return { hasActiveToken: false, isLocked: false }
    }

    const attemptCount = metadata.attemptCount || 0
    const attemptsRemaining = Math.max(0, this.MAX_ATTEMPTS - attemptCount)

    return {
      hasActiveToken: true,
      isLocked: false,
      expiresAt: token.expires,
      attemptsRemaining
    }
  }

  /**
   * Cleanup all expired verification tokens (maintenance function)
   */
  static async cleanupExpiredTokens(): Promise<{ cleaned: number }> {
    const result = await db.verificationToken.deleteMany({
      where: {
        expires: { lt: new Date() }
      }
    })

    return { cleaned: result.count }
  }

  /**
   * Analyze verification patterns for suspicious activity
   */
  static async detectSuspiciousActivity(
    email: string,
    context: VerificationContext
  ): Promise<{ suspicious: boolean; reasons: string[] }> {
    const reasons: string[] = []
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000)

    // Check for excessive verification requests
    const recentRequests = await db.auditLog.count({
      where: {
        userEmail: email,
        action: "EMAIL_VERIFICATION_TOKEN_CREATED",
        createdAt: { gte: last24Hours }
      }
    })

    if (recentRequests >= this.MAX_DAILY_SENDS) {
      reasons.push("excessive_daily_requests")
    }

    // Check for multiple failed attempts from different IPs
    if (context.ipAddress) {
      const failedAttempts = await db.auditLog.findMany({
        where: {
          userEmail: email,
          action: "EMAIL_VERIFICATION_INVALID_TOKEN",
          createdAt: { gte: last24Hours }
        },
        select: { ipAddress: true }
      })

      const uniqueIPs = new Set(failedAttempts.map(a => a.ipAddress).filter(Boolean))
      if (uniqueIPs.size > 3) {
        reasons.push("multiple_ip_failures")
      }
    }

    return {
      suspicious: reasons.length > 0,
      reasons
    }
  }
}