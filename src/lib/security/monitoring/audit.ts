import { type AuditAction, type AuditResourceType, type User, type Organization } from "@prisma/client";
import { db } from "../../core/db";

// Types for audit logging
export interface AuditContext {
  userId?: string;
  userEmail?: string;
  userName?: string;
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

export interface AuditLogData {
  action: AuditAction;
  resourceType: AuditResourceType;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  success?: boolean;
  errorMessage?: string;
  details?: string;
}

// Main audit logging function
export async function createAuditLog(
  context: AuditContext,
  data: AuditLogData
): Promise<void> {
  try {
    await db.auditLog.create({
      data: {
        // User context
        userId: context.userId,
        userEmail: context.userEmail,
        userName: context.userName,
        
        // Organization context
        organizationId: context.organizationId,
        
        // Action details
        action: data.action,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        
        // Changes
        oldValues: data.oldValues,
        newValues: data.newValues,
        
        // Request context
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        
        // Additional data
        metadata: data.metadata ? { 
          ...data.metadata, 
          ...(data.details && { details: data.details }) 
        } : (data.details ? { details: data.details } : undefined),
        success: data.success ?? true,
        errorMessage: data.errorMessage,
      },
    });
  } catch (error) {
    // Don't let audit logging failures break the main operation
    console.error("Failed to create audit log:", error);
  }
}

// Helper functions for common audit operations
export class AuditLogger {
  private context: AuditContext;

  constructor(context: AuditContext) {
    this.context = context;
  }

  // Generic log method for direct logging
  async log(data: AuditLogData): Promise<void> {
    await createAuditLog(this.context, data);
  }

  // Authentication events
  async logLogin(success: boolean, errorMessage?: string) {
    await createAuditLog(this.context, {
      action: success ? "USER_LOGIN" : "USER_LOGIN_FAILED",
      resourceType: "AUTHENTICATION",
      success,
      errorMessage,
      metadata: {
        loginTime: new Date().toISOString(),
      },
    });
  }

  async logLogout() {
    await createAuditLog(this.context, {
      action: "USER_LOGOUT",
      resourceType: "AUTHENTICATION",
      metadata: {
        logoutTime: new Date().toISOString(),
      },
    });
  }

  async logPasswordReset(stage: "requested" | "completed", userEmail: string) {
    await createAuditLog(this.context, {
      action: stage === "requested" ? "PASSWORD_RESET_REQUESTED" : "PASSWORD_RESET_COMPLETED",
      resourceType: "AUTHENTICATION",
      metadata: {
        targetUserEmail: userEmail,
        resetTime: new Date().toISOString(),
      },
    });
  }

  async log2FAChange(enabled: boolean) {
    await createAuditLog(this.context, {
      action: enabled ? "TWO_FACTOR_ENABLED" : "TWO_FACTOR_DISABLED",
      resourceType: "AUTHENTICATION",
      resourceId: this.context.userId,
      metadata: {
        changeTime: new Date().toISOString(),
      },
    });
  }

  // User management events
  async logUserCreated(newUser: Partial<User>) {
    await createAuditLog(this.context, {
      action: "USER_CREATED",
      resourceType: "USER",
      resourceId: newUser.id,
      newValues: {
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        organizationId: newUser.organizationId,
      },
      metadata: {
        createdAt: new Date().toISOString(),
      },
    });
  }

  async logUserUpdated(userId: string, oldValues: any, newValues: any) {
    await createAuditLog(this.context, {
      action: "USER_UPDATED",
      resourceType: "USER",
      resourceId: userId,
      oldValues,
      newValues,
      metadata: {
        updatedAt: new Date().toISOString(),
        changedFields: Object.keys(newValues),
      },
    });
  }

  async logUserRoleChanged(userId: string, oldRole: string, newRole: string) {
    await createAuditLog(this.context, {
      action: "USER_ROLE_CHANGED",
      resourceType: "USER",
      resourceId: userId,
      oldValues: { role: oldRole },
      newValues: { role: newRole },
      metadata: {
        changedAt: new Date().toISOString(),
        severity: "HIGH", // Role changes are critical
      },
    });
  }

  async logUserDeleted(userId: string, deletedUser: any) {
    await createAuditLog(this.context, {
      action: "USER_DELETED",
      resourceType: "USER",
      resourceId: userId,
      oldValues: {
        email: deletedUser.email,
        name: deletedUser.name,
        role: deletedUser.role,
      },
      metadata: {
        deletedAt: new Date().toISOString(),
        severity: "HIGH",
      },
    });
  }

  async logUserInvited(email: string, role: string, organizationId: string) {
    await createAuditLog(this.context, {
      action: "USER_INVITED",
      resourceType: "USER",
      newValues: {
        email,
        role,
        organizationId,
      },
      metadata: {
        invitedAt: new Date().toISOString(),
        invitedBy: this.context.userName,
      },
    });
  }

  // Organization management events
  async logOrganizationCreated(org: Partial<Organization>) {
    await createAuditLog(this.context, {
      action: "ORGANIZATION_CREATED",
      resourceType: "ORGANIZATION",
      resourceId: org.id,
      newValues: {
        name: org.name,
        slug: org.slug,
      },
      metadata: {
        createdAt: new Date().toISOString(),
      },
    });
  }

  async logOrganizationUpdated(orgId: string, oldValues: any, newValues: any) {
    await createAuditLog(this.context, {
      action: "ORGANIZATION_UPDATED",
      resourceType: "ORGANIZATION",
      resourceId: orgId,
      oldValues,
      newValues,
      metadata: {
        updatedAt: new Date().toISOString(),
        changedFields: Object.keys(newValues),
      },
    });
  }

  async logOrganizationSettingsUpdated(orgId: string, oldSettings: any, newSettings: any) {
    await createAuditLog(this.context, {
      action: "ORGANIZATION_SETTINGS_UPDATED",
      resourceType: "ORGANIZATION",
      resourceId: orgId,
      oldValues: { settings: oldSettings },
      newValues: { settings: newSettings },
      metadata: {
        updatedAt: new Date().toISOString(),
      },
    });
  }

  // Job management events
  async logJobCreated(jobId: string, jobData: any) {
    await createAuditLog(this.context, {
      action: "JOB_CREATED",
      resourceType: "JOB",
      resourceId: jobId,
      newValues: {
        title: jobData.title,
        status: jobData.status,
        locationType: jobData.locationType,
        employmentType: jobData.employmentType,
      },
      metadata: {
        createdAt: new Date().toISOString(),
      },
    });
  }

  async logJobUpdated(jobId: string, oldValues: any, newValues: any) {
    await createAuditLog(this.context, {
      action: "JOB_UPDATED",
      resourceType: "JOB",
      resourceId: jobId,
      oldValues,
      newValues,
      metadata: {
        updatedAt: new Date().toISOString(),
        changedFields: Object.keys(newValues),
      },
    });
  }

  async logJobPublished(jobId: string, jobTitle: string) {
    await createAuditLog(this.context, {
      action: "JOB_PUBLISHED",
      resourceType: "JOB",
      resourceId: jobId,
      metadata: {
        jobTitle,
        publishedAt: new Date().toISOString(),
      },
    });
  }

  async logJobDeleted(jobId: string, jobData: any) {
    await createAuditLog(this.context, {
      action: "JOB_DELETED",
      resourceType: "JOB",
      resourceId: jobId,
      oldValues: {
        title: jobData.title,
        status: jobData.status,
      },
      metadata: {
        deletedAt: new Date().toISOString(),
      },
    });
  }

  // Application management events
  async logApplicationCreated(applicationId: string, applicationData: any) {
    await createAuditLog(this.context, {
      action: "APPLICATION_CREATED",
      resourceType: "APPLICATION",
      resourceId: applicationId,
      newValues: {
        jobId: applicationData.jobId,
        email: applicationData.email,
        status: applicationData.status,
      },
      metadata: {
        appliedAt: new Date().toISOString(),
      },
    });
  }

  async logApplicationStatusChanged(applicationId: string, oldStatus: string, newStatus: string, jobTitle?: string) {
    await createAuditLog(this.context, {
      action: "APPLICATION_STATUS_CHANGED",
      resourceType: "APPLICATION",
      resourceId: applicationId,
      oldValues: { status: oldStatus },
      newValues: { status: newStatus },
      metadata: {
        changedAt: new Date().toISOString(),
        jobTitle,
      },
    });
  }

  // Admin and security events
  async logUnauthorizedAccess(resourceType: AuditResourceType, resourceId?: string, attemptedAction?: string) {
    await createAuditLog(this.context, {
      action: "UNAUTHORIZED_ACCESS_ATTEMPTED",
      resourceType,
      resourceId,
      success: false,
      metadata: {
        attemptedAction,
        attemptedAt: new Date().toISOString(),
        severity: "HIGH",
      },
    });
  }

  async logSuspiciousActivity(details: string, severity: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM") {
    await createAuditLog(this.context, {
      action: "SUSPICIOUS_ACTIVITY_DETECTED",
      resourceType: "SYSTEM",
      success: false,
      metadata: {
        details,
        detectedAt: new Date().toISOString(),
        severity,
      },
    });
  }

  async logDataExport(resourceType: AuditResourceType, exportType: string, recordCount: number) {
    await createAuditLog(this.context, {
      action: "DATA_EXPORT_REQUESTED",
      resourceType,
      metadata: {
        exportType,
        recordCount,
        exportedAt: new Date().toISOString(),
        severity: "MEDIUM",
      },
    });
  }
}

// Helper function to create audit context from tRPC context
export function createAuditContext(
  session: any,
  request?: {
    headers?: Record<string, string>;
    ip?: string;
  }
): AuditContext {
  return {
    userId: session?.user?.id,
    userEmail: session?.user?.email,
    userName: session?.user?.name,
    organizationId: session?.user?.organizationId,
    ipAddress: request?.ip || request?.headers?.['x-forwarded-for'] || request?.headers?.['x-real-ip'],
    userAgent: request?.headers?.['user-agent'],
    sessionId: session?.sessionToken,
  };
}

// Query functions for retrieving audit logs
export async function getAuditLogs(params: {
  organizationId?: string;
  userId?: string;
  action?: AuditAction;
  resourceType?: AuditResourceType;
  resourceId?: string;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
  includeSystem?: boolean;
}) {
  const {
    organizationId,
    userId,
    action,
    resourceType,
    resourceId,
    startDate,
    endDate,
    page = 1,
    limit = 50,
    includeSystem = false,
  } = params;

  const skip = (page - 1) * limit;

  const where: any = {
    ...(organizationId && { organizationId }),
    ...(userId && { userId }),
    ...(action && { action }),
    ...(resourceType && { resourceType }),
    ...(resourceId && { resourceId }),
    ...(startDate && endDate && {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    }),
  };

  // Exclude system events unless specifically requested
  if (!includeSystem) {
    where.resourceType = {
      not: "SYSTEM",
    };
  }

  const [logs, total] = await Promise.all([
    db.auditLog.findMany({
      where,
      skip,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    }),
    db.auditLog.count({ where }),
  ]);

  return {
    logs,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  };
}

// Get audit log statistics
export async function getAuditStats(params: {
  organizationId?: string;
  startDate?: Date;
  endDate?: Date;
}) {
  const { organizationId, startDate, endDate } = params;

  const where: any = {
    ...(organizationId && { organizationId }),
    ...(startDate && endDate && {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    }),
  };

  const [
    totalLogs,
    actionCounts,
    resourceTypeCounts,
    failedOperations,
    recentActivity,
  ] = await Promise.all([
    db.auditLog.count({ where }),
    db.auditLog.groupBy({
      by: ['action'],
      where,
      _count: true,
      orderBy: { _count: { action: 'desc' } },
      take: 10,
    }),
    db.auditLog.groupBy({
      by: ['resourceType'],
      where,
      _count: true,
    }),
    db.auditLog.count({
      where: {
        ...where,
        success: false,
      },
    }),
    db.auditLog.count({
      where: {
        ...where,
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
    }),
  ]);

  return {
    totalLogs,
    actionCounts: actionCounts.reduce((acc, item) => {
      acc[item.action] = item._count;
      return acc;
    }, {} as Record<string, number>),
    resourceTypeCounts: resourceTypeCounts.reduce((acc, item) => {
      acc[item.resourceType] = item._count;
      return acc;
    }, {} as Record<string, number>),
    failedOperations,
    recentActivity,
  };
}