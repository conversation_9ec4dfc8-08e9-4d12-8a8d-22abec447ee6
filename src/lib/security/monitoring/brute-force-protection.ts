import { db } from "../../core/db"
import { SecurityPolicyService } from "../../core/security-policy"

export interface LoginAttemptResult {
  allowed: boolean
  attemptsRemaining: number
  lockoutExpiration?: Date
  progressiveDelay?: number
}

export class BruteForceProtection {
  /**
   * Record a login attempt
   */
  static async recordLoginAttempt(
    email: string,
    ipAddress: string,
    userAgent: string,
    success: boolean,
    failureReason?: string,
    location?: string
  ): Promise<void> {
    await db.loginAttempt.create({
      data: {
        email,
        ipAddress,
        userAgent,
        success,
        failureReason,
        location,
      }
    })

    // If login failed, update user's failed attempt counter
    if (!success) {
      const user = await db.user.findUnique({
        where: { email },
        select: { 
          id: true, 
          failedLoginAttempts: true,
          organizationId: true,
          accountLockedUntil: true
        }
      })

      if (user) {
        await this.handleFailedLogin(user.id, user.organizationId || "")
      }
    } else {
      // Reset failed attempts on successful login
      await this.resetFailedAttempts(email)
    }
  }

  /**
   * Check if login is allowed based on brute force protection
   */
  static async checkLoginAllowed(
    email: string,
    ipAddress: string,
    organizationId?: string
  ): Promise<LoginAttemptResult> {
    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        failedLoginAttempts: true,
        accountLockedUntil: true,
        organizationId: true
      }
    })

    if (!user) {
      // For non-existent users, still apply IP-based rate limiting
      return await this.checkIpBasedLimits(ipAddress, organizationId)
    }

    const orgId = user.organizationId || organizationId || ""
    const policy = orgId ? await SecurityPolicyService.getPolicy(orgId) : null

    const maxAttempts = policy?.maxFailedAttempts || 5
    const lockoutMinutes = policy?.lockoutDurationMinutes || 30

    // Check if account is currently locked
    if (user.accountLockedUntil && user.accountLockedUntil > new Date()) {
      return {
        allowed: false,
        attemptsRemaining: 0,
        lockoutExpiration: user.accountLockedUntil
      }
    }

    // Calculate remaining attempts
    const attemptsRemaining = Math.max(0, maxAttempts - user.failedLoginAttempts)

    // Apply progressive delay based on failed attempts
    const progressiveDelay = this.calculateProgressiveDelay(user.failedLoginAttempts)

    // Check IP-based limits as well
    const ipCheck = await this.checkIpBasedLimits(ipAddress, orgId)
    if (!ipCheck.allowed) {
      return ipCheck
    }

    return {
      allowed: attemptsRemaining > 0,
      attemptsRemaining,
      progressiveDelay
    }
  }

  /**
   * Handle a failed login attempt
   */
  private static async handleFailedLogin(
    userId: string,
    organizationId: string
  ): Promise<void> {
    const policy = organizationId ? await SecurityPolicyService.getPolicy(organizationId) : null
    const maxAttempts = policy?.maxFailedAttempts || 5
    const lockoutMinutes = policy?.lockoutDurationMinutes || 30

    const user = await db.user.update({
      where: { id: userId },
      data: {
        failedLoginAttempts: { increment: 1 }
      },
      select: {
        id: true,
        email: true,
        failedLoginAttempts: true,
        organizationId: true
      }
    })

    // Lock account if max attempts reached
    if (user.failedLoginAttempts >= maxAttempts) {
      const lockoutUntil = new Date(Date.now() + lockoutMinutes * 60 * 1000)
      
      await db.user.update({
        where: { id: userId },
        data: {
          accountLockedUntil: lockoutUntil
        }
      })

      // Log the account lockout
      await db.auditLog.create({
        data: {
          userId: user.id,
          userEmail: user.email,
          organizationId: user.organizationId,
          action: "SUSPICIOUS_ACTIVITY_DETECTED",
          resourceType: "AUTHENTICATION",
          resourceId: user.id,
          metadata: {
            reason: "account_locked_brute_force",
            failedAttempts: user.failedLoginAttempts,
            lockoutUntil: lockoutUntil.toISOString()
          },
          success: true,
        }
      })

      // TODO: Send security alert email to user
      console.log(`Account locked for user ${user.email} due to brute force attempts`)
    }
  }

  /**
   * Reset failed login attempts after successful login
   */
  private static async resetFailedAttempts(email: string): Promise<void> {
    await db.user.updateMany({
      where: { email },
      data: {
        failedLoginAttempts: 0,
        accountLockedUntil: null
      }
    })
  }

  /**
   * Check IP-based rate limiting
   */
  private static async checkIpBasedLimits(
    ipAddress: string,
    organizationId?: string
  ): Promise<LoginAttemptResult> {
    const timeWindow = 15 * 60 * 1000 // 15 minutes
    const since = new Date(Date.now() - timeWindow)

    // Count failed attempts from this IP in the time window
    const recentFailures = await db.loginAttempt.count({
      where: {
        ipAddress,
        success: false,
        createdAt: { gte: since }
      }
    })

    // Get policy-based limits or use defaults
    let maxIpAttempts = 20 // Default for IP-based limiting
    if (organizationId) {
      const policy = await SecurityPolicyService.getPolicy(organizationId)
      maxIpAttempts = policy.maxFailedAttempts * 4 // 4x the user limit for IP
    }

    const attemptsRemaining = Math.max(0, maxIpAttempts - recentFailures)

    return {
      allowed: recentFailures < maxIpAttempts,
      attemptsRemaining,
      progressiveDelay: this.calculateProgressiveDelay(recentFailures)
    }
  }

  /**
   * Calculate progressive delay based on failed attempts
   */
  private static calculateProgressiveDelay(attempts: number): number {
    if (attempts <= 2) return 0
    if (attempts <= 4) return 2000 // 2 seconds
    if (attempts <= 6) return 5000 // 5 seconds
    if (attempts <= 8) return 10000 // 10 seconds
    return 30000 // 30 seconds for 9+ attempts
  }

  /**
   * Get recent login attempts for analysis
   */
  static async getRecentAttempts(
    email?: string,
    ipAddress?: string,
    hours: number = 24
  ): Promise<any[]> {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000)

    return await db.loginAttempt.findMany({
      where: {
        ...(email && { email }),
        ...(ipAddress && { ipAddress }),
        createdAt: { gte: since }
      },
      orderBy: { createdAt: "desc" },
      take: 100
    })
  }

  /**
   * Detect suspicious login patterns
   */
  static async detectSuspiciousActivity(
    email: string,
    ipAddress: string,
    location?: string
  ): Promise<{
    suspicious: boolean
    reasons: string[]
    riskScore: number
  }> {
    const reasons: string[] = []
    let riskScore = 0

    // Get user's recent login history
    const recentLogins = await db.loginAttempt.findMany({
      where: {
        email,
        success: true,
        createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
      },
      orderBy: { createdAt: "desc" },
      take: 20
    })

    // Check for new IP address
    const knownIps = new Set(recentLogins.map(login => login.ipAddress))
    if (recentLogins.length > 0 && !knownIps.has(ipAddress)) {
      reasons.push("Login from new IP address")
      riskScore += 2
    }

    // Check for new location
    if (location && recentLogins.length > 0) {
      const knownLocations = new Set(recentLogins.map(login => login.location).filter(Boolean))
      if (knownLocations.size > 0 && !knownLocations.has(location)) {
        reasons.push("Login from new location")
        riskScore += 3
      }
    }

    // Check for rapid-fire attempts from same IP
    const recentFromIp = await db.loginAttempt.count({
      where: {
        ipAddress,
        createdAt: { gte: new Date(Date.now() - 5 * 60 * 1000) } // Last 5 minutes
      }
    })

    if (recentFromIp > 10) {
      reasons.push("High frequency login attempts from IP")
      riskScore += 4
    }

    // Check for failed attempts before success
    const recentFailures = await db.loginAttempt.count({
      where: {
        email,
        success: false,
        createdAt: { gte: new Date(Date.now() - 15 * 60 * 1000) } // Last 15 minutes
      }
    })

    if (recentFailures > 3) {
      reasons.push("Multiple failed attempts before success")
      riskScore += 2
    }

    // Check for login during unusual hours (if we have enough history)
    if (recentLogins.length >= 5) {
      const loginHours = recentLogins.map(login => login.createdAt.getHours())
      const currentHour = new Date().getHours()
      const usualHours = this.getUsualActivityHours(loginHours)
      
      if (!usualHours.includes(currentHour)) {
        reasons.push("Login during unusual hours")
        riskScore += 1
      }
    }

    return {
      suspicious: riskScore >= 4,
      reasons,
      riskScore
    }
  }

  /**
   * Get usual activity hours for a user
   */
  private static getUsualActivityHours(loginHours: number[]): number[] {
    const hourCounts: { [hour: number]: number } = {}
    
    loginHours.forEach(hour => {
      hourCounts[hour] = (hourCounts[hour] || 0) + 1
    })

    const sortedHours = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 12) // Top 12 hours (half the day)
      .map(([hour]) => parseInt(hour))

    return sortedHours
  }

  /**
   * Unlock user account (admin function)
   */
  static async unlockAccount(userId: string, unlockedBy: string): Promise<void> {
    const user = await db.user.update({
      where: { id: userId },
      data: {
        failedLoginAttempts: 0,
        accountLockedUntil: null
      },
      select: {
        id: true,
        email: true,
        organizationId: true
      }
    })

    // Log the unlock action
    await db.auditLog.create({
      data: {
        userId: unlockedBy,
        organizationId: user.organizationId,
        action: "USER_UPDATED",
        resourceType: "USER",
        resourceId: user.id,
        metadata: {
          action: "account_unlocked",
          targetUser: user.email
        },
        success: true,
      }
    })
  }

  /**
   * Clean up old login attempts
   */
  static async cleanup(): Promise<number> {
    // Keep login attempts for 30 days
    const cutoff = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    const result = await db.loginAttempt.deleteMany({
      where: {
        createdAt: { lt: cutoff }
      }
    })

    return result.count
  }
}