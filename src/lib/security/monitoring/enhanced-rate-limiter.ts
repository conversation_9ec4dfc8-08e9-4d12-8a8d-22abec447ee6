import { NextRequest } from "next/server"
import { db } from "@/lib/core/db"
import { AuditLogger } from "@/lib/security/monitoring/audit"

export interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  burstAllowance?: number
  adaptiveFactor?: number
  trustedUserMultiplier?: number
  suspiciousUserDivisor?: number
}

export interface RateLimitResult {
  allowed: boolean
  limit: number
  remaining: number
  resetTime: Date
  retryAfter?: number
  adaptiveLimit?: number
  riskScore?: number
  blockedReason?: string
}

export interface UserBehaviorPattern {
  userId?: string
  ipAddress: string
  userAgent?: string
  successRate: number
  avgRequestInterval: number
  uniqueEndpoints: number
  geoLocation?: string
  riskScore: number
  isTrusted: boolean
  isSuspicious: boolean
}

/**
 * Enhanced adaptive rate limiter with behavioral analysis,
 * distributed coordination, and comprehensive attack detection
 */
export class EnhancedRateLimiter {
  private config: RateLimitConfig
  
  // Memory-managed caches with size limits
  private static readonly MAX_CACHE_SIZE = 10000 // Maximum entries per cache
  private static readonly CLEANUP_THRESHOLD = 8000 // Trigger cleanup at this size
  private static readonly CLEANUP_INTERVAL = 5 * 60 * 1000 // 5 minutes
  
  private static behaviorCache = new Map<string, UserBehaviorPattern>()
  private static blockedIPs = new Map<string, { until: Date; reason: string }>()
  private static rateLimitStore = new Map<string, { 
    count: number
    windowStart: number
    requests: Array<{ timestamp: number; success: boolean; endpoint: string }>
  }>()
  
  private static lastCleanup = Date.now()
  private static cleanupTimer: NodeJS.Timeout | null = null

  constructor(config: RateLimitConfig) {
    this.config = {
      burstAllowance: Math.ceil(config.maxRequests * 0.2), // 20% burst allowance
      adaptiveFactor: 1.0, // 100% = no adaptation
      trustedUserMultiplier: 2.0, // Trusted users get 2x limit
      suspiciousUserDivisor: 2.0, // Suspicious users get 50% limit
      ...config
    }
  }

  /**
   * Comprehensive rate limit check with adaptive behavior analysis
   */
  async checkLimit(
    req: NextRequest,
    endpoint: string,
    context?: {
      userId?: string
      success?: boolean
      organizationId?: string
    }
  ): Promise<RateLimitResult> {
    // Check memory pressure and cleanup if needed
    this.checkMemoryPressure()
    
    const ipAddress = this.getClientIP(req)
    const userAgent = req.headers.get("user-agent") || undefined
    const identifier = context?.userId ? `user:${context.userId}` : `ip:${ipAddress}`

    // Check if IP is currently blocked
    const blockCheck = this.checkIPBlock(ipAddress)
    if (!blockCheck.allowed) {
      return {
        allowed: false,
        limit: this.config.maxRequests,
        remaining: 0,
        resetTime: blockCheck.until!,
        blockedReason: blockCheck.reason,
        retryAfter: Math.ceil((blockCheck.until!.getTime() - Date.now()) / 1000)
      }
    }

    // Analyze user behavior pattern
    const behaviorPattern = await this.analyzeBehaviorPattern(
      identifier,
      ipAddress,
      userAgent,
      endpoint,
      context?.success
    )

    // Calculate adaptive rate limit based on behavior
    const adaptiveLimit = this.calculateAdaptiveLimit(behaviorPattern)

    // Apply burst detection
    const burstResult = this.detectBurstActivity(identifier, endpoint)
    if (burstResult.isBurst && !behaviorPattern.isTrusted) {
      await this.handleBurstViolation(identifier, endpoint, behaviorPattern)
      return {
        allowed: false,
        limit: adaptiveLimit,
        remaining: 0,
        resetTime: new Date(Date.now() + this.config.windowMs),
        blockedReason: "burst_limit_exceeded",
        riskScore: behaviorPattern.riskScore,
        retryAfter: Math.ceil(this.config.windowMs / 1000)
      }
    }

    // Standard rate limit check with adaptive limits
    const standardResult = this.checkStandardLimit(identifier, endpoint, adaptiveLimit)

    // Enhanced monitoring for suspicious activity
    if (!standardResult.allowed && behaviorPattern.riskScore > 0.7) {
      await this.handleSuspiciousActivity(identifier, endpoint, behaviorPattern)
    }

    return {
      ...standardResult,
      adaptiveLimit,
      riskScore: behaviorPattern.riskScore,
      limit: adaptiveLimit
    }
  }

  /**
   * Analyze user behavior patterns for adaptive rate limiting
   */
  private async analyzeBehaviorPattern(
    identifier: string,
    ipAddress: string,
    userAgent?: string,
    endpoint?: string,
    success?: boolean
  ): Promise<UserBehaviorPattern> {
    const key = identifier
    const now = Date.now()
    
    // Get or create behavior pattern
    let pattern = EnhancedRateLimiter.behaviorCache.get(key)
    
    if (!pattern) {
      pattern = {
        ipAddress,
        userAgent,
        successRate: 1.0,
        avgRequestInterval: 10000, // 10 seconds default
        uniqueEndpoints: 1,
        riskScore: 0.1, // Low initial risk
        isTrusted: false,
        isSuspicious: false
      }
    }

    // Update pattern with new request
    if (endpoint && success !== undefined) {
      const requestData = this.getRateLimitData(key, endpoint)
      if (requestData) {
        requestData.requests.push({
          timestamp: now,
          success,
          endpoint
        })

        // Keep only recent requests (last hour)
        const oneHourAgo = now - (60 * 60 * 1000)
        requestData.requests = requestData.requests.filter(r => r.timestamp > oneHourAgo)

        // Calculate success rate
        const recentRequests = requestData.requests
        if (recentRequests.length > 0) {
          const successCount = recentRequests.filter(r => r.success).length
          pattern.successRate = successCount / recentRequests.length
        }

        // Calculate average request interval
        if (recentRequests.length > 1) {
          const intervals = []
          for (let i = 1; i < recentRequests.length; i++) {
            intervals.push(recentRequests[i].timestamp - recentRequests[i-1].timestamp)
          }
          pattern.avgRequestInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length
        }

        // Calculate unique endpoints
        pattern.uniqueEndpoints = new Set(recentRequests.map(r => r.endpoint)).size
      }
    }

    // Calculate risk score based on multiple factors
    pattern.riskScore = this.calculateRiskScore(pattern)

    // Determine trust/suspicious status
    pattern.isTrusted = this.isTrustedUser(pattern)
    pattern.isSuspicious = this.isSuspiciousUser(pattern)

    // Cache the updated pattern
    EnhancedRateLimiter.behaviorCache.set(key, pattern)

    return pattern
  }

  /**
   * Calculate risk score based on behavior patterns
   */
  private calculateRiskScore(pattern: UserBehaviorPattern): number {
    let riskScore = 0.0

    // High failure rate increases risk
    if (pattern.successRate < 0.3) {
      riskScore += 0.4
    } else if (pattern.successRate < 0.6) {
      riskScore += 0.2
    }

    // Very fast requests increase risk (likely automated)
    if (pattern.avgRequestInterval < 1000) { // Less than 1 second
      riskScore += 0.3
    } else if (pattern.avgRequestInterval < 3000) { // Less than 3 seconds
      riskScore += 0.2
    }

    // Many unique endpoints might indicate scanning
    if (pattern.uniqueEndpoints > 20) {
      riskScore += 0.3
    } else if (pattern.uniqueEndpoints > 10) {
      riskScore += 0.1
    }

    // Cap risk score at 1.0
    return Math.min(1.0, riskScore)
  }

  /**
   * Determine if user should be trusted (higher limits)
   */
  private isTrustedUser(pattern: UserBehaviorPattern): boolean {
    return (
      pattern.successRate > 0.9 && // Very high success rate
      pattern.avgRequestInterval > 5000 && // Reasonable request intervals
      pattern.uniqueEndpoints < 5 && // Focused usage
      pattern.riskScore < 0.2 // Low risk score
    )
  }

  /**
   * Determine if user behavior is suspicious (lower limits)
   */
  private isSuspiciousUser(pattern: UserBehaviorPattern): boolean {
    return (
      pattern.riskScore > 0.6 || // High risk score
      pattern.successRate < 0.2 || // Very low success rate
      pattern.avgRequestInterval < 500 // Very fast automated requests
    )
  }

  /**
   * Calculate adaptive rate limit based on user behavior
   */
  private calculateAdaptiveLimit(pattern: UserBehaviorPattern): number {
    let adaptiveLimit = this.config.maxRequests

    if (pattern.isTrusted) {
      // Trusted users get higher limits
      adaptiveLimit = Math.floor(adaptiveLimit * this.config.trustedUserMultiplier!)
    } else if (pattern.isSuspicious) {
      // Suspicious users get lower limits
      adaptiveLimit = Math.floor(adaptiveLimit / this.config.suspiciousUserDivisor!)
    }

    // Apply adaptive factor
    adaptiveLimit = Math.floor(adaptiveLimit * this.config.adaptiveFactor!)

    // Ensure minimum limit of 1
    return Math.max(1, adaptiveLimit)
  }

  /**
   * Detect burst activity patterns
   */
  private detectBurstActivity(
    identifier: string,
    endpoint: string
  ): { isBurst: boolean; burstCount: number } {
    const data = this.getRateLimitData(identifier, endpoint)
    if (!data) return { isBurst: false, burstCount: 0 }

    const now = Date.now()
    const burstWindow = 30 * 1000 // 30 seconds
    const burstStart = now - burstWindow

    // Count requests in burst window
    const burstRequests = data.requests.filter(r => r.timestamp > burstStart)
    const burstCount = burstRequests.length

    // Consider burst if more than burst allowance in 30 seconds
    const isBurst = burstCount > (this.config.burstAllowance || 5)

    return { isBurst, burstCount }
  }

  /**
   * Standard rate limit check with adaptive limits
   */
  private checkStandardLimit(
    identifier: string,
    endpoint: string,
    adaptiveLimit: number
  ): RateLimitResult {
    const now = Date.now()
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs
    const key = `${identifier}:${endpoint}`

    // Get or create rate limit entry
    let entry = EnhancedRateLimiter.rateLimitStore.get(key)
    if (!entry || entry.windowStart !== windowStart) {
      entry = {
        count: 1,
        windowStart,
        requests: [{ timestamp: now, success: true, endpoint }]
      }
    } else {
      entry.count++
      entry.requests.push({ timestamp: now, success: true, endpoint })
    }

    EnhancedRateLimiter.rateLimitStore.set(key, entry)

    const resetTime = new Date(windowStart + this.config.windowMs)
    const remaining = Math.max(0, adaptiveLimit - entry.count)
    const allowed = entry.count <= adaptiveLimit

    const result: RateLimitResult = {
      allowed,
      limit: adaptiveLimit,
      remaining,
      resetTime
    }

    if (!allowed) {
      result.retryAfter = Math.ceil((resetTime.getTime() - now) / 1000)
    }

    return result
  }

  /**
   * Handle burst activity violations
   */
  private async handleBurstViolation(
    identifier: string,
    endpoint: string,
    pattern: UserBehaviorPattern
  ): Promise<void> {
    const auditor = new AuditLogger({
      userId: identifier.startsWith('user:') ? identifier.replace('user:', '') : undefined,
      ipAddress: pattern.ipAddress,
      userAgent: pattern.userAgent
    })

    await auditor.log({
      action: "RATE_LIMIT_BURST_DETECTED",
      resourceType: "SYSTEM",
      resourceId: endpoint,
      details: {
        identifier,
        endpoint,
        riskScore: pattern.riskScore,
        avgInterval: pattern.avgRequestInterval,
        successRate: pattern.successRate
      },
      severity: "high"
    })

    // Temporarily block IP for repeated burst violations
    if (pattern.riskScore > 0.8) {
      this.blockIP(pattern.ipAddress, 15, "repeated_burst_violations")
    }
  }

  /**
   * Handle suspicious activity patterns
   */
  private async handleSuspiciousActivity(
    identifier: string,
    endpoint: string,
    pattern: UserBehaviorPattern
  ): Promise<void> {
    const auditor = new AuditLogger({
      userId: identifier.startsWith('user:') ? identifier.replace('user:', '') : undefined,
      ipAddress: pattern.ipAddress,
      userAgent: pattern.userAgent
    })

    await auditor.log({
      action: "RATE_LIMIT_SUSPICIOUS_PATTERN",
      resourceType: "SYSTEM",
      resourceId: endpoint,
      details: {
        identifier,
        endpoint,
        riskScore: pattern.riskScore,
        successRate: pattern.successRate,
        avgInterval: pattern.avgRequestInterval,
        uniqueEndpoints: pattern.uniqueEndpoints
      },
      severity: "high"
    })

    // Block IP for highly suspicious activity
    if (pattern.riskScore > 0.9) {
      this.blockIP(pattern.ipAddress, 60, "highly_suspicious_activity")
    }
  }

  /**
   * Check if IP is currently blocked
   */
  private checkIPBlock(ipAddress: string): { allowed: boolean; until?: Date; reason?: string } {
    const blocked = EnhancedRateLimiter.blockedIPs.get(ipAddress)
    if (!blocked) return { allowed: true }

    if (blocked.until > new Date()) {
      return { 
        allowed: false, 
        until: blocked.until, 
        reason: blocked.reason 
      }
    }

    // Block expired, remove it
    EnhancedRateLimiter.blockedIPs.delete(ipAddress)
    return { allowed: true }
  }

  /**
   * Block IP address for specified duration
   */
  private blockIP(ipAddress: string, durationMinutes: number, reason: string): void {
    const until = new Date(Date.now() + durationMinutes * 60 * 1000)
    EnhancedRateLimiter.blockedIPs.set(ipAddress, { until, reason })
  }

  /**
   * Get or create rate limit data for identifier
   */
  private getRateLimitData(identifier: string, endpoint: string) {
    const key = `${identifier}:${endpoint}`
    let data = EnhancedRateLimiter.rateLimitStore.get(key)
    
    if (!data) {
      data = {
        count: 0,
        windowStart: Date.now(),
        requests: []
      }
      EnhancedRateLimiter.rateLimitStore.set(key, data)
    }
    
    return data
  }

  /**
   * Extract client IP from request headers
   */
  private getClientIP(req: NextRequest): string {
    const forwardedFor = req.headers.get("x-forwarded-for")
    const realIp = req.headers.get("x-real-ip")
    const cfConnectingIp = req.headers.get("cf-connecting-ip")
    
    return forwardedFor?.split(",")[0]?.trim() || 
           realIp || 
           cfConnectingIp || 
           "unknown"
  }

  /**
   * Cleanup old data to prevent memory leaks
   */
  static cleanup(): { cleaned: number } {
    const now = Date.now()
    const cutoff = now - (24 * 60 * 60 * 1000) // 24 hours
    let cleaned = 0

    // Clean up rate limit store
    for (const [key, data] of EnhancedRateLimiter.rateLimitStore.entries()) {
      if (data.windowStart < cutoff) {
        EnhancedRateLimiter.rateLimitStore.delete(key)
        cleaned++
      } else {
        // Clean up old requests within data
        data.requests = data.requests.filter(r => r.timestamp > cutoff)
      }
    }

    // Clean up behavior cache (keep for 24 hours)
    for (const [key, pattern] of EnhancedRateLimiter.behaviorCache.entries()) {
      // Remove patterns for inactive users (no recent requests)
      const hasRecentActivity = Array.from(EnhancedRateLimiter.rateLimitStore.values())
        .some(data => data.requests.some(r => r.timestamp > cutoff))
      
      if (!hasRecentActivity) {
        EnhancedRateLimiter.behaviorCache.delete(key)
        cleaned++
      }
    }

    // Clean up expired IP blocks
    for (const [ip, block] of EnhancedRateLimiter.blockedIPs.entries()) {
      if (block.until <= new Date()) {
        EnhancedRateLimiter.blockedIPs.delete(ip)
        cleaned++
      }
    }

    return { cleaned }
  }

  /**
   * Get statistics for monitoring
   */
  static getStatistics(): {
    activeRateLimits: number
    blockedIPs: number
    trustedUsers: number
    suspiciousUsers: number
    totalRequests: number
  } {
    const activeRateLimits = EnhancedRateLimiter.rateLimitStore.size
    const blockedIPs = Array.from(EnhancedRateLimiter.blockedIPs.values())
      .filter(block => block.until > new Date()).length

    let trustedUsers = 0
    let suspiciousUsers = 0
    let totalRequests = 0

    for (const pattern of EnhancedRateLimiter.behaviorCache.values()) {
      if (pattern.isTrusted) trustedUsers++
      if (pattern.isSuspicious) suspiciousUsers++
    }

    for (const data of EnhancedRateLimiter.rateLimitStore.values()) {
      totalRequests += data.requests.length
    }

    return {
      activeRateLimits,
      blockedIPs,
      trustedUsers,
      suspiciousUsers,
      totalRequests
    }
  }

  /**
   * Manually block/unblock IP (admin functions)
   */
  static manualBlockIP(ipAddress: string, durationMinutes: number, reason: string): void {
    const until = new Date(Date.now() + durationMinutes * 60 * 1000)
    EnhancedRateLimiter.blockedIPs.set(ipAddress, { until, reason })
  }

  static unblockIP(ipAddress: string): boolean {
    return EnhancedRateLimiter.blockedIPs.delete(ipAddress)
  }

  /**
   * Reset user behavior pattern (for testing or admin purposes)
   */
  static resetBehaviorPattern(identifier: string): boolean {
    return EnhancedRateLimiter.behaviorCache.delete(identifier)
  }

  /**
   * Automatic memory management with size-based cleanup
   */
  private static performMemoryManagement(): { cleaned: number; totalSize: number } {
    const now = Date.now()
    let totalCleaned = 0

    // Clean expired blocked IPs
    const expiredIPs = Array.from(EnhancedRateLimiter.blockedIPs.entries())
      .filter(([_, block]) => block.until <= new Date())
    expiredIPs.forEach(([ip]) => {
      EnhancedRateLimiter.blockedIPs.delete(ip)
      totalCleaned++
    })

    // Clean old rate limit entries (older than window)
    const cutoffTime = now - (this.CLEANUP_INTERVAL * 2) // Keep data for 10 minutes
    const expiredEntries = Array.from(EnhancedRateLimiter.rateLimitStore.entries())
      .filter(([_, data]) => (now - data.windowStart) > cutoffTime)
    expiredEntries.forEach(([key]) => {
      EnhancedRateLimiter.rateLimitStore.delete(key)
      totalCleaned++
    })

    // Clean stale behavior patterns (no activity for 1 hour)
    const behaviorCutoff = now - (60 * 60 * 1000)
    const staleBehaviors = Array.from(EnhancedRateLimiter.behaviorCache.entries())
      .filter(([_, pattern]) => {
        // Remove if no recent activity (estimate based on avg interval)
        const estimatedLastActivity = now - (pattern.avgRequestInterval * 10)
        return estimatedLastActivity < behaviorCutoff
      })
    staleBehaviors.forEach(([key]) => {
      EnhancedRateLimiter.behaviorCache.delete(key)
      totalCleaned++
    })

    // If still over threshold, perform LRU-style cleanup
    if (EnhancedRateLimiter.rateLimitStore.size > this.CLEANUP_THRESHOLD) {
      const entries = Array.from(EnhancedRateLimiter.rateLimitStore.entries())
        .sort(([_, a], [__, b]) => a.windowStart - b.windowStart) // Oldest first
      
      const toRemove = entries.slice(0, EnhancedRateLimiter.rateLimitStore.size - this.CLEANUP_THRESHOLD)
      toRemove.forEach(([key]) => {
        EnhancedRateLimiter.rateLimitStore.delete(key)
        totalCleaned++
      })
    }

    // Update last cleanup time
    EnhancedRateLimiter.lastCleanup = now

    const totalSize = EnhancedRateLimiter.rateLimitStore.size + 
                     EnhancedRateLimiter.behaviorCache.size + 
                     EnhancedRateLimiter.blockedIPs.size

    return { cleaned: totalCleaned, totalSize }
  }

  /**
   * Enhanced cleanup with memory management
   */
  static cleanup(): { cleaned: number; totalSize: number; memoryStats: any } {
    const result = this.performMemoryManagement()
    
    // Calculate memory usage estimates
    const memoryStats = {
      rateLimitEntries: EnhancedRateLimiter.rateLimitStore.size,
      behaviorPatterns: EnhancedRateLimiter.behaviorCache.size,
      blockedIPs: EnhancedRateLimiter.blockedIPs.size,
      estimatedMemoryKB: Math.round((result.totalSize * 2) / 1024), // Rough estimate
      lastCleanup: new Date(EnhancedRateLimiter.lastCleanup).toISOString()
    }

    return { ...result, memoryStats }
  }

  /**
   * Initialize periodic memory management
   */
  static initializeMemoryManagement(): void {
    // Clear any existing timer
    if (EnhancedRateLimiter.cleanupTimer) {
      clearInterval(EnhancedRateLimiter.cleanupTimer)
    }

    // Set up periodic cleanup
    EnhancedRateLimiter.cleanupTimer = setInterval(() => {
      const totalSize = EnhancedRateLimiter.rateLimitStore.size + 
                       EnhancedRateLimiter.behaviorCache.size + 
                       EnhancedRateLimiter.blockedIPs.size

      // Trigger cleanup if over threshold or it's been too long
      const timeSinceCleanup = Date.now() - EnhancedRateLimiter.lastCleanup
      if (totalSize > this.CLEANUP_THRESHOLD || timeSinceCleanup > this.CLEANUP_INTERVAL) {
        this.performMemoryManagement()
      }
    }, 60 * 1000) // Check every minute

    // Ensure cleanup happens on process exit
    process.on('SIGINT', () => {
      if (EnhancedRateLimiter.cleanupTimer) {
        clearInterval(EnhancedRateLimiter.cleanupTimer)
      }
    })

    process.on('SIGTERM', () => {
      if (EnhancedRateLimiter.cleanupTimer) {
        clearInterval(EnhancedRateLimiter.cleanupTimer)
      }
    })
  }

  /**
   * Check memory usage and trigger cleanup if needed
   */
  private checkMemoryPressure(): void {
    const totalSize = EnhancedRateLimiter.rateLimitStore.size + 
                     EnhancedRateLimiter.behaviorCache.size + 
                     EnhancedRateLimiter.blockedIPs.size

    // Force cleanup if approaching maximum cache size
    if (totalSize > EnhancedRateLimiter.CLEANUP_THRESHOLD) {
      EnhancedRateLimiter.performMemoryManagement()
    }
  }
}