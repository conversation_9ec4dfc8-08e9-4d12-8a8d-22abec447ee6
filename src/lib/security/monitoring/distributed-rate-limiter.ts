import { db } from "../../core/db"
import { AuditLogger } from "../monitoring/audit"
import { EnhancedRateLimiter, RateLimitConfig, RateLimitResult } from "./enhanced-rate-limiter"

export interface DistributedRateLimitConfig extends RateLimitConfig {
  useDatabase: boolean
  fallbackToLocal: boolean
  syncInterval: number
}

export interface DistributedRateLimitEntry {
  identifier: string
  endpoint: string
  windowStart: Date
  count: number
  behaviorData?: any
  lastUpdated: Date
}

/**
 * Distributed rate limiter that synchronizes across multiple server instances
 * using database storage with local caching for performance
 */
export class DistributedRateLimiter {
  private config: DistributedRateLimitConfig
  private localLimiter: EnhancedRateLimiter
  private lastSync: number = 0
  private static distributedCache = new Map<string, DistributedRateLimitEntry>()

  constructor(config: DistributedRateLimitConfig) {
    this.config = {
      useDatabase: true,
      fallbackToLocal: true,
      syncInterval: 30000, // Sync every 30 seconds
      ...config
    }

    this.localLimiter = new EnhancedRateLimiter(config)
  }

  /**
   * Check rate limit with distributed coordination
   */
  async checkLimit(
    req: any,
    endpoint: string,
    context?: {
      userId?: string
      success?: boolean
      organizationId?: string
    }
  ): Promise<RateLimitResult> {
    const identifier = context?.userId ? `user:${context.userId}` : `ip:${this.getClientIP(req)}`
    
    if (!this.config.useDatabase) {
      // Fall back to local-only rate limiting
      return this.localLimiter.checkLimit(req, endpoint, context)
    }

    try {
      // Sync with database if needed
      if (Date.now() - this.lastSync > this.config.syncInterval) {
        await this.syncWithDatabase()
      }

      // Check distributed rate limit
      const result = await this.checkDistributedLimit(identifier, endpoint, req, context)
      
      // Update local cache and database
      await this.updateDistributedState(identifier, endpoint, result, context)
      
      return result

    } catch (error) {
      console.error("Distributed rate limiting error:", error)
      
      if (this.config.fallbackToLocal) {
        // Fallback to local rate limiting
        return this.localLimiter.checkLimit(req, endpoint, context)
      }
      
      // Fail open - allow request but log error
      await this.logDistributedError(identifier, endpoint, error)
      return {
        allowed: true,
        limit: this.config.maxRequests,
        remaining: this.config.maxRequests - 1,
        resetTime: new Date(Date.now() + this.config.windowMs)
      }
    }
  }

  /**
   * Check rate limit using distributed data
   */
  private async checkDistributedLimit(
    identifier: string,
    endpoint: string,
    req: any,
    context?: any
  ): Promise<RateLimitResult> {
    const now = new Date()
    const windowStart = new Date(Math.floor(now.getTime() / this.config.windowMs) * this.config.windowMs)
    const key = `${identifier}:${endpoint}`

    // Get current distributed state
    const distributedEntry = await this.getDistributedEntry(identifier, endpoint, windowStart)
    const localEntry = DistributedRateLimiter.distributedCache.get(key)

    // Combine distributed and local counts
    let totalCount = distributedEntry.count
    if (localEntry && localEntry.windowStart.getTime() === windowStart.getTime()) {
      // Add local count that hasn't been synced yet
      const timeSinceSync = now.getTime() - this.lastSync
      if (timeSinceSync < this.config.syncInterval) {
        const localCountSinceSync = this.estimateLocalCountSinceSync(localEntry, timeSinceSync)
        totalCount += localCountSinceSync
      }
    }

    // Increment count for current request
    totalCount += 1

    // Calculate adaptive limit (simplified for distributed context)
    const adaptiveLimit = await this.calculateDistributedAdaptiveLimit(
      identifier, 
      distributedEntry.behaviorData
    )

    const resetTime = new Date(windowStart.getTime() + this.config.windowMs)
    const remaining = Math.max(0, adaptiveLimit - totalCount)
    const allowed = totalCount <= adaptiveLimit

    return {
      allowed,
      limit: adaptiveLimit,
      remaining,
      resetTime,
      retryAfter: allowed ? undefined : Math.ceil((resetTime.getTime() - now.getTime()) / 1000)
    }
  }

  /**
   * Get or create distributed rate limit entry
   */
  private async getDistributedEntry(
    identifier: string,
    endpoint: string,
    windowStart: Date
  ): Promise<DistributedRateLimitEntry> {
    try {
      // Try to get existing entry from database
      const existing = await db.rateLimitEntry.findUnique({
        where: {
          identifier_route_windowStart: {
            identifier,
            route: endpoint,
            windowStart
          }
        },
        select: {
          count: true,
          metadata: true,
          updatedAt: true
        }
      })

      if (existing) {
        return {
          identifier,
          endpoint,
          windowStart,
          count: existing.count,
          behaviorData: existing.metadata,
          lastUpdated: existing.updatedAt
        }
      }
    } catch (error) {
      console.warn("Failed to get distributed rate limit entry:", error)
    }

    // Return empty entry if not found or error
    return {
      identifier,
      endpoint,
      windowStart,
      count: 0,
      lastUpdated: new Date()
    }
  }

  /**
   * Update distributed state in database and local cache
   */
  private async updateDistributedState(
    identifier: string,
    endpoint: string,
    result: RateLimitResult,
    context?: any
  ): Promise<void> {
    const now = new Date()
    const windowStart = new Date(Math.floor(now.getTime() / this.config.windowMs) * this.config.windowMs)
    const key = `${identifier}:${endpoint}`

    // Update local cache
    DistributedRateLimiter.distributedCache.set(key, {
      identifier,
      endpoint,
      windowStart,
      count: result.limit - result.remaining,
      lastUpdated: now
    })

    // Update database (async, non-blocking)
    this.updateDatabaseEntry(identifier, endpoint, windowStart, result, context)
      .catch(error => console.warn("Failed to update distributed rate limit:", error))
  }

  /**
   * Update database entry (async operation)
   */
  private async updateDatabaseEntry(
    identifier: string,
    endpoint: string,
    windowStart: Date,
    result: RateLimitResult,
    context?: any
  ): Promise<void> {
    const behaviorData = {
      adaptiveLimit: result.adaptiveLimit,
      riskScore: result.riskScore,
      lastRequest: new Date().toISOString(),
      endpoint
    }

    await db.rateLimitEntry.upsert({
      where: {
        identifier_route_windowStart: {
          identifier,
          route: endpoint,
          windowStart
        }
      },
      update: {
        count: { increment: 1 },
        metadata: behaviorData,
        updatedAt: new Date()
      },
      create: {
        identifier,
        route: endpoint,
        windowStart,
        count: 1,
        metadata: behaviorData
      }
    })

    // Log rate limit violation if blocked
    if (!result.allowed) {
      await this.logRateLimitViolation(identifier, endpoint, result)
    }
  }

  /**
   * Calculate adaptive limit for distributed context
   */
  private async calculateDistributedAdaptiveLimit(
    identifier: string,
    behaviorData?: any
  ): Promise<number> {
    // Get recent behavior data from database
    const recentBehavior = await this.getRecentBehaviorData(identifier)
    
    let adaptiveLimit = this.config.maxRequests

    if (recentBehavior) {
      // Apply adaptive factors based on distributed behavior analysis
      if (recentBehavior.isTrusted) {
        adaptiveLimit = Math.floor(adaptiveLimit * (this.config.trustedUserMultiplier || 2.0))
      } else if (recentBehavior.isSuspicious) {
        adaptiveLimit = Math.floor(adaptiveLimit / (this.config.suspiciousUserDivisor || 2.0))
      }
    }

    return Math.max(1, adaptiveLimit)
  }

  /**
   * Get recent behavior data from database
   */
  private async getRecentBehaviorData(identifier: string): Promise<any> {
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      
      const entries = await db.rateLimitEntry.findMany({
        where: {
          identifier,
          windowStart: { gte: oneHourAgo }
        },
        select: {
          count: true,
          metadata: true,
          route: true
        },
        orderBy: { windowStart: 'desc' },
        take: 10
      })

      if (entries.length === 0) return null

      // Analyze behavior patterns
      const totalRequests = entries.reduce((sum, entry) => sum + entry.count, 0)
      const uniqueRoutes = new Set(entries.map(e => e.route)).size
      const avgRequestsPerWindow = totalRequests / entries.length

      // Simple heuristics for trust/suspicion
      const isTrusted = (
        avgRequestsPerWindow < (this.config.maxRequests * 0.5) && // Moderate usage
        uniqueRoutes < 5 && // Not scanning many endpoints
        totalRequests > 0 // Active user
      )

      const isSuspicious = (
        avgRequestsPerWindow > (this.config.maxRequests * 0.8) || // Heavy usage
        uniqueRoutes > 15 // Potential scanning
      )

      return {
        totalRequests,
        uniqueRoutes,
        avgRequestsPerWindow,
        isTrusted,
        isSuspicious
      }
    } catch (error) {
      console.warn("Failed to get recent behavior data:", error)
      return null
    }
  }

  /**
   * Sync local cache with database
   */
  private async syncWithDatabase(): Promise<void> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
      
      // Get recent entries from database
      const recentEntries = await db.rateLimitEntry.findMany({
        where: {
          windowStart: { gte: fiveMinutesAgo }
        },
        select: {
          identifier: true,
          route: true,
          windowStart: true,
          count: true,
          metadata: true,
          updatedAt: true
        }
      })

      // Update local cache with database data
      for (const entry of recentEntries) {
        const key = `${entry.identifier}:${entry.route}`
        const cached = DistributedRateLimiter.distributedCache.get(key)
        
        // Only update if database entry is newer
        if (!cached || entry.updatedAt > cached.lastUpdated) {
          DistributedRateLimiter.distributedCache.set(key, {
            identifier: entry.identifier,
            endpoint: entry.route,
            windowStart: entry.windowStart,
            count: entry.count,
            behaviorData: entry.metadata,
            lastUpdated: entry.updatedAt
          })
        }
      }

      this.lastSync = Date.now()
    } catch (error) {
      console.error("Failed to sync with database:", error)
    }
  }

  /**
   * Estimate local count since last sync
   */
  private estimateLocalCountSinceSync(entry: DistributedRateLimitEntry, timeSinceSync: number): number {
    // Conservative estimate - assume some requests happened locally
    const syncIntervalRatio = timeSinceSync / this.config.syncInterval
    return Math.floor(syncIntervalRatio * 2) // Estimate up to 2 requests per sync interval
  }

  /**
   * Log rate limit violation
   */
  private async logRateLimitViolation(
    identifier: string,
    endpoint: string,
    result: RateLimitResult
  ): Promise<void> {
    const auditor = new AuditLogger({
      userId: identifier.startsWith('user:') ? identifier.replace('user:', '') : undefined,
      ipAddress: identifier.startsWith('ip:') ? identifier.replace('ip:', '') : undefined
    })

    await auditor.log({
      action: "DISTRIBUTED_RATE_LIMIT_EXCEEDED",
      resourceType: "SYSTEM",
      resourceId: endpoint,
      details: {
        identifier,
        endpoint,
        limit: result.limit,
        remaining: result.remaining,
        adaptiveLimit: result.adaptiveLimit,
        riskScore: result.riskScore,
        blockedReason: result.blockedReason
      },
      severity: "medium"
    })
  }

  /**
   * Log distributed rate limiting errors
   */
  private async logDistributedError(
    identifier: string,
    endpoint: string,
    error: any
  ): Promise<void> {
    const auditor = new AuditLogger({
      userId: identifier.startsWith('user:') ? identifier.replace('user:', '') : undefined,
      ipAddress: identifier.startsWith('ip:') ? identifier.replace('ip:', '') : undefined
    })

    await auditor.log({
      action: "DISTRIBUTED_RATE_LIMIT_ERROR",
      resourceType: "SYSTEM",
      resourceId: endpoint,
      details: {
        identifier,
        endpoint,
        error: error instanceof Error ? error.message : "Unknown error",
        fallbackToLocal: this.config.fallbackToLocal
      },
      severity: "high"
    })
  }

  /**
   * Extract client IP from request
   */
  private getClientIP(req: any): string {
    if (req?.headers?.get) {
      const forwardedFor = req.headers.get("x-forwarded-for")
      const realIp = req.headers.get("x-real-ip")
      const cfConnectingIp = req.headers.get("cf-connecting-ip")
      
      return forwardedFor?.split(",")[0]?.trim() || 
             realIp || 
             cfConnectingIp || 
             "unknown"
    }
    
    return "unknown"
  }

  /**
   * Cleanup old distributed data
   */
  static async cleanup(): Promise<{ cleaned: number }> {
    let cleaned = 0

    try {
      // Clean up old database entries
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      const result = await db.rateLimitEntry.deleteMany({
        where: {
          windowStart: { lt: oneDayAgo }
        }
      })
      cleaned += result.count

      // Clean up local cache
      const now = Date.now()
      const cutoff = now - (24 * 60 * 60 * 1000)

      for (const [key, entry] of DistributedRateLimiter.distributedCache.entries()) {
        if (entry.lastUpdated.getTime() < cutoff) {
          DistributedRateLimiter.distributedCache.delete(key)
          cleaned++
        }
      }
    } catch (error) {
      console.error("Failed to cleanup distributed rate limit data:", error)
    }

    return { cleaned }
  }

  /**
   * Get distributed statistics
   */
  static async getStatistics(): Promise<{
    distributedEntries: number
    cachedEntries: number
    databaseEntries: number
    recentViolations: number
  }> {
    const cachedEntries = DistributedRateLimiter.distributedCache.size
    
    let databaseEntries = 0
    let recentViolations = 0

    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      
      databaseEntries = await db.rateLimitEntry.count({
        where: {
          windowStart: { gte: oneHourAgo }
        }
      })

      recentViolations = await db.auditLog.count({
        where: {
          action: { in: ["DISTRIBUTED_RATE_LIMIT_EXCEEDED", "RATE_LIMIT_BURST_DETECTED"] },
          createdAt: { gte: oneHourAgo }
        }
      })
    } catch (error) {
      console.warn("Failed to get distributed statistics:", error)
    }

    return {
      distributedEntries: databaseEntries + cachedEntries,
      cachedEntries,
      databaseEntries,
      recentViolations
    }
  }
}

/**
 * Factory function to create appropriate rate limiter based on configuration
 */
export function createRateLimiter(config: DistributedRateLimitConfig): DistributedRateLimiter | EnhancedRateLimiter {
  if (config.useDatabase) {
    return new DistributedRateLimiter(config)
  } else {
    return new EnhancedRateLimiter(config)
  }
}

/**
 * Predefined distributed rate limiters for common use cases
 */
export const distributedRateLimiters = {
  // Authentication endpoints - strict distributed limits
  auth: new DistributedRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    burstAllowance: 2,
    useDatabase: true,
    fallbackToLocal: true,
    syncInterval: 30000
  }),

  // API endpoints - moderate distributed limits
  api: new DistributedRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    burstAllowance: 20,
    useDatabase: true,
    fallbackToLocal: true,
    syncInterval: 60000
  }),

  // Upload endpoints - strict file upload limits
  upload: new DistributedRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10,
    burstAllowance: 3,
    useDatabase: true,
    fallbackToLocal: true,
    syncInterval: 30000
  })
}