import { type AuditAction, type AuditResourceType } from "@prisma/client";

// Types for audit logging in edge runtime
export interface EdgeAuditContext {
  userId?: string;
  userEmail?: string;
  userName?: string;
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

export interface EdgeAuditLogData {
  action: AuditAction;
  resourceType: AuditResourceType;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  success?: boolean;
  errorMessage?: string;
  details?: string;
  severity?: "low" | "medium" | "high";
}

/**
 * Edge-compatible audit logging that queues logs for processing
 * This function runs in Edge Runtime and doesn't use Prisma directly
 */
export async function createEdgeAuditLog(
  context: EdgeAuditContext,
  data: EdgeAuditLogData
): Promise<void> {
  try {
    // For edge runtime, we'll send the audit log to an API endpoint
    // that can handle database operations in Node.js runtime
    const auditPayload = {
      context,
      data: {
        ...data,
        timestamp: new Date().toISOString(),
      },
    };

    // Queue the audit log by calling the API endpoint
    // Use fetch with no-wait to avoid blocking the middleware
    fetch('/api/internal/audit-log', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add a special header to identify internal requests
        'X-Internal-Request': 'audit-log',
      },
      body: JSON.stringify(auditPayload),
    }).catch((error) => {
      // Silent failure - don't let audit logging break the main flow
      console.error("Failed to queue audit log:", error);
    });

  } catch (error) {
    // Don't let audit logging failures break the main operation
    console.error("Failed to create edge audit log:", error);
  }
}

/**
 * Edge-compatible audit logger class for middleware use
 */
export class EdgeAuditLogger {
  private context: EdgeAuditContext;

  constructor(context: EdgeAuditContext) {
    this.context = context;
  }

  // Generic log method for direct logging
  async log(data: EdgeAuditLogData): Promise<void> {
    await createEdgeAuditLog(this.context, data);
  }

  // Authentication events
  async logLogin(success: boolean, errorMessage?: string) {
    await createEdgeAuditLog(this.context, {
      action: success ? "USER_LOGIN" : "USER_LOGIN_FAILED",
      resourceType: "AUTHENTICATION",
      success,
      errorMessage,
      severity: success ? "low" : "medium",
      metadata: {
        loginTime: new Date().toISOString(),
      },
    });
  }

  async logLogout() {
    await createEdgeAuditLog(this.context, {
      action: "USER_LOGOUT",
      resourceType: "AUTHENTICATION",
      severity: "low",
      metadata: {
        logoutTime: new Date().toISOString(),
      },
    });
  }

  async logSessionValidationFailed(details: Record<string, any>) {
    await createEdgeAuditLog(this.context, {
      action: "SESSION_VALIDATION_FAILED",
      resourceType: "AUTHENTICATION",
      resourceId: this.context.userId,
      success: false,
      severity: details.requiresRegeneration ? "high" : "medium",
      metadata: {
        ...details,
        validationTime: new Date().toISOString(),
      },
    });
  }

  async logSessionValidationError(error: string, path: string) {
    await createEdgeAuditLog(this.context, {
      action: "SESSION_VALIDATION_FAILED",
      resourceType: "AUTHENTICATION", 
      resourceId: this.context.userId,
      success: false,
      errorMessage: error,
      severity: "medium",
      metadata: {
        path,
        validationTime: new Date().toISOString(),
      },
    });
  }

  async logUnauthorizedAccess(resourceType: AuditResourceType, resourceId?: string, attemptedAction?: string) {
    await createEdgeAuditLog(this.context, {
      action: "UNAUTHORIZED_ACCESS_ATTEMPTED",
      resourceType,
      resourceId,
      success: false,
      severity: "high",
      metadata: {
        attemptedAction,
        attemptedAt: new Date().toISOString(),
      },
    });
  }

  async logSuspiciousActivity(details: string, severity: "low" | "medium" | "high" = "medium") {
    await createEdgeAuditLog(this.context, {
      action: "SUSPICIOUS_ACTIVITY_DETECTED",
      resourceType: "SYSTEM",
      success: false,
      severity,
      metadata: {
        details,
        detectedAt: new Date().toISOString(),
      },
    });
  }

  async logRateLimitExceeded(endpoint: string, limit: number) {
    await createEdgeAuditLog(this.context, {
      action: "API_RATE_LIMIT_EXCEEDED",
      resourceType: "SYSTEM",
      success: false,
      severity: "medium",
      metadata: {
        endpoint,
        limit,
        exceededAt: new Date().toISOString(),
      },
    });
  }

  async logCSRFViolation(origin: string, expectedOrigins: string[]) {
    await createEdgeAuditLog(this.context, {
      action: "CSRF_VIOLATION",
      resourceType: "SYSTEM", 
      success: false,
      severity: "high",
      metadata: {
        origin,
        expectedOrigins,
        detectedAt: new Date().toISOString(),
      },
    });
  }
}

/**
 * Helper function to create edge audit context from request
 */
export function createEdgeAuditContext(
  token: any,
  request: {
    headers?: Record<string, string>;
    ip?: string;
  }
): EdgeAuditContext {
  return {
    userId: token?.sub || token?.id,
    userEmail: token?.email,
    userName: token?.name,
    organizationId: token?.organizationId,
    ipAddress: request?.ip || request?.headers?.['x-forwarded-for'] || request?.headers?.['x-real-ip'],
    userAgent: request?.headers?.['user-agent'],
    sessionId: token?.sessionToken || token?.jti,
  };
}