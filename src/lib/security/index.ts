/**
 * Security layer exports - Authentication & Protection
 * All authentication and security related functionality
 */

// Authentication
export { SessionSecurityManager } from './authentication/session-security'
export { OAuthSecurityManager } from './authentication/oauth-security'
export { 
  createEmail2FASession,
  verifyEmail2FASession,
  consumeEmail2FASession,
  cleanupExpired2FASessions
} from './authentication/two-factor-session'

// Protection middleware
export { CSRFSecurityManager } from './protection/csrf-security'
export { validateCSRFToken, withCSRFProtection } from './protection/csrf-middleware'
export { validateSessionSecurity } from './protection/session-middleware'
export { 
  createPublicRateLimit,
  createAPIRateLimit, 
  createAuthRateLimit
} from './protection/rate-limit-middleware'

// Security validation
export { EnhancedPasswordValidator } from './validation/enhanced-password-validator'
export { PasswordHistoryManager } from './validation/password-history'
export { AdminSecurityManager } from './validation/admin-security'
export { SecurityConfigValidator } from './validation/security-config-validator'

// Security monitoring
export { AuditLogger, createAuditLog, createAuditContext, getAuditLogs, getAuditStats } from './monitoring/audit'
export { EnhancedRateLimiter } from './monitoring/enhanced-rate-limiter'
export { DistributedRateLimiter } from './monitoring/distributed-rate-limiter'
export { BruteForceProtection } from './monitoring/brute-force-protection'

// Security utilities
export { SecureErrorHandler } from './utilities/secure-error-handling'
export { EmailVerificationSecurityManager } from './utilities/email-verification-security'
export { WebAuthnService } from './utilities/webauthn'