import { TOTP, Secret } from "otpauth"
import QRCode from "qrcode"
import { randomBytes } from "crypto"
import { db } from "./db"
import { sendTwoFactorEmail } from "./email"

// Generate a new TOTP secret for a user
export function generateTOTPSecret(userEmail: string, issuer: string = "Sourceflex") {
  const secret = new Secret({ size: 20 })
  
  const totp = new TOTP({
    issuer,
    label: userEmail,
    algorithm: "SHA1",
    digits: 6,
    period: 30,
    secret,
  })

  return {
    secret: secret.base32,
    uri: totp.toString(),
    totp,
  }
}

// Generate QR code data URL for TOTP setup
export async function generateQRCode(uri: string): Promise<string> {
  try {
    return await QRCode.toDataURL(uri)
  } catch (error) {
    console.error("Error generating QR code:", error)
    throw new Error("Failed to generate QR code")
  }
}

// Verify TOTP token
export function verifyTOTP(secret: string, token: string): boolean {
  try {
    const totp = new TOTP({
      algorithm: "SHA1",
      digits: 6,
      period: 30,
      secret: Secret.fromBase32(secret),
    })

    // Allow for time drift - check current period and previous/next periods
    const delta = totp.validate({ token, window: 1 })
    return delta !== null
  } catch (error) {
    console.error("Error verifying TOTP:", error)
    return false
  }
}

// Generate backup codes
export function generateBackupCodes(count: number = 10): string[] {
  const codes: string[] = []
  
  for (let i = 0; i < count; i++) {
    const code = randomBytes(4).toString("hex").toUpperCase()
    // Format as XXXX-XXXX for readability
    const formattedCode = `${code.slice(0, 4)}-${code.slice(4, 8)}`
    codes.push(formattedCode)
  }
  
  return codes
}

// Verify backup code and mark as used
export async function verifyBackupCode(userId: string, code: string): Promise<boolean> {
  const user = await db.user.findUnique({
    where: { id: userId },
    select: { twoFactorBackupCodes: true },
  })

  if (!user || !user.twoFactorBackupCodes.includes(code)) {
    return false
  }

  // Remove the used backup code
  const updatedCodes = user.twoFactorBackupCodes.filter((c: string) => c !== code)
  
  await db.user.update({
    where: { id: userId },
    data: { twoFactorBackupCodes: updatedCodes },
  })

  return true
}

// Enable 2FA for a user
export async function enableTwoFactor(userId: string, secret: string, backupCodes: string[]) {
  return await db.user.update({
    where: { id: userId },
    data: {
      twoFactorEnabled: true,
      twoFactorSecret: secret,
      twoFactorBackupCodes: backupCodes,
    },
  })
}

// Disable 2FA for a user
export async function disableTwoFactor(userId: string) {
  return await db.user.update({
    where: { id: userId },
    data: {
      twoFactorEnabled: false,
      twoFactorSecret: null,
      twoFactorBackupCodes: [],
    },
  })
}

// Check if user has 2FA enabled
export async function getUserTwoFactorStatus(userId: string) {
  const user = await db.user.findUnique({
    where: { id: userId },
    select: {
      twoFactorEnabled: true,
      twoFactorBackupCodes: true,
    },
  })

  return {
    enabled: user?.twoFactorEnabled || false,
    backupCodesCount: user?.twoFactorBackupCodes.length || 0,
  }
}

// Verify 2FA token (TOTP or backup code)
export async function verifyTwoFactorToken(userId: string, token: string): Promise<{
  valid: boolean
  method: "totp" | "backup" | null
}> {
  const user = await db.user.findUnique({
    where: { id: userId },
    select: {
      twoFactorEnabled: true,
      twoFactorSecret: true,
      twoFactorBackupCodes: true,
    },
  })

  if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
    return { valid: false, method: null }
  }

  // First try TOTP
  if (verifyTOTP(user.twoFactorSecret, token)) {
    return { valid: true, method: "totp" }
  }

  // Then try backup codes
  if (await verifyBackupCode(userId, token)) {
    return { valid: true, method: "backup" }
  }

  return { valid: false, method: null }
}

// Email-based 2FA functions

// Generate a 6-digit email 2FA code
function generateEmail2FACode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Send email 2FA code
export async function sendEmail2FA(userId: string, deviceInfo?: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Get user details
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        name: true,
      }
    })

    if (!user) {
      return { success: false, error: "User not found" }
    }

    // Generate 6-digit code
    const code = generateEmail2FACode()
    const expires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Store code in VerificationToken table (reusing for email 2FA)
    await db.verificationToken.deleteMany({
      where: { 
        identifier: `2fa:${userId}` // Use prefix to distinguish from email verification
      }
    })

    await db.verificationToken.create({
      data: {
        identifier: `2fa:${userId}`,
        token: code,
        expires,
      }
    })

    // Send email
    const emailSent = await sendTwoFactorEmail(
      user.email,
      code,
      user.name || undefined,
      deviceInfo
    )

    if (!emailSent) {
      return { success: false, error: "Failed to send email" }
    }

    return { success: true }
  } catch (error) {
    console.error("Email 2FA error:", error)
    return { success: false, error: "Internal server error" }
  }
}

// Verify email 2FA code
export async function verifyEmail2FA(userId: string, code: string): Promise<{ valid: boolean; error?: string }> {
  try {
    // Find the verification code
    const verificationToken = await db.verificationToken.findFirst({
      where: { 
        identifier: `2fa:${userId}`,
        token: code 
      }
    })
    
    if (!verificationToken) {
      return { valid: false, error: "Invalid verification code" }
    }
    
    // Check if code has expired
    if (verificationToken.expires < new Date()) {
      // Delete expired code
      await db.verificationToken.deleteMany({
        where: { identifier: `2fa:${userId}` }
      })
      return { valid: false, error: "Verification code has expired. Please request a new one." }
    }
    
    // Delete the used code
    await db.verificationToken.deleteMany({
      where: { identifier: `2fa:${userId}` }
    })
    
    return { valid: true }
  } catch (error) {
    console.error("Email 2FA verification error:", error)
    return { valid: false, error: "Internal server error" }
  }
}

// Check if user can resend email 2FA code
export async function canResendEmail2FA(userId: string): Promise<{ canResend: boolean; waitMinutes?: number }> {
  try {
    // Check for existing verification code
    const existingToken = await db.verificationToken.findFirst({
      where: { identifier: `2fa:${userId}` },
      orderBy: { expires: 'desc' }
    })
    
    if (!existingToken) {
      return { canResend: true }
    }
    
    // Allow resending if code is older than 2 minutes (to prevent spam)
    const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000)
    const codeCreatedAt = new Date(existingToken.expires.getTime() - 10 * 60 * 1000) // Code expires in 10 min
    
    if (codeCreatedAt < twoMinutesAgo) {
      return { canResend: true }
    }
    
    const waitMinutes = Math.ceil((2 * 60 * 1000 - (Date.now() - codeCreatedAt.getTime())) / (60 * 1000))
    return { canResend: false, waitMinutes }
  } catch (error) {
    console.error("Check resend 2FA error:", error)
    return { canResend: true } // Allow resend on error
  }
}