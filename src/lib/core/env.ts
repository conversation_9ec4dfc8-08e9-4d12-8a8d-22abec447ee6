import { z } from "zod";

const envSchema = z.object({
  DATABASE_URL: z.string().min(1, "Database URL is required"),
  NEXTAUTH_SECRET: z.string().min(32, "NextAuth secret must be at least 32 characters"),
  NEXTAUTH_URL: z.string().min(1, "NextAuth URL is required"),
  NODE_ENV: z.enum(["development", "test", "production"]).default("development"),
  
  // Session security
  SESSION_HMAC_SECRET: z.string().min(32, "Session HMAC secret must be at least 32 characters"),
  
  // OAuth Providers (optional for development)
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  LINKEDIN_CLIENT_ID: z.string().optional(),
  LINKEDIN_CLIENT_SECRET: z.string().optional(),
  
  // Email Service
  RESEND_API_KEY: z.string().optional(),
  FROM_EMAIL: z.string().email().default("<EMAIL>"),
  
  // Admin creation security
  ADMIN_CREATION_KEY: z.string().optional(),
});

// Debug environment loading in development
if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Environment Variables Debug:');
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'SET' : 'NOT SET');
  console.log('NEXTAUTH_SECRET:', process.env.NEXTAUTH_SECRET ? 'SET (length: ' + process.env.NEXTAUTH_SECRET.length + ')' : 'NOT SET');
  console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL || 'NOT SET');
  console.log('SESSION_HMAC_SECRET:', process.env.SESSION_HMAC_SECRET ? 'SET (length: ' + process.env.SESSION_HMAC_SECRET.length + ')' : 'NOT SET');
  console.log('NODE_ENV:', process.env.NODE_ENV || 'NOT SET');
}

let env: z.infer<typeof envSchema>;

try {
  env = envSchema.parse(process.env);
  if (process.env.NODE_ENV === 'development') {
    console.log('✅ Environment validation successful!');
  }
} catch (error) {
  console.error('❌ Environment validation failed:');
  if (error instanceof z.ZodError) {
    error.errors.forEach((err) => {
      console.error(`  - ${err.path.join('.')}: ${err.message}`);
    });
  }
  
  console.error('\n💡 Make sure you have the required environment variables in your .env file');
  console.error('Required variables: DATABASE_URL, NEXTAUTH_SECRET, NEXTAUTH_URL, SESSION_HMAC_SECRET\n');
  
  // Don't throw in development to allow debugging
  if (process.env.NODE_ENV === 'production') {
    throw error;
  }
  
  // Provide fallbacks for development
  env = {
    DATABASE_URL: process.env.DATABASE_URL || 'postgresql://developer:dev_password@localhost:5432/sourceflex',
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || 'development-secret-key-for-local-development-only-with-more-characters-to-meet-minimum-32-char-requirement',
    NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3000',
    SESSION_HMAC_SECRET: process.env.SESSION_HMAC_SECRET || 'development-hmac-secret-key-for-local-development-only-with-more-characters-to-meet-minimum-32-char-requirement',
    NODE_ENV: (process.env.NODE_ENV as 'development' | 'test' | 'production') || 'development',
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    LINKEDIN_CLIENT_ID: process.env.LINKEDIN_CLIENT_ID,
    LINKEDIN_CLIENT_SECRET: process.env.LINKEDIN_CLIENT_SECRET,
    RESEND_API_KEY: process.env.RESEND_API_KEY,
    FROM_EMAIL: process.env.FROM_EMAIL || "<EMAIL>",
    ADMIN_CREATION_KEY: process.env.ADMIN_CREATION_KEY,
  };
  
  console.log('🔧 Using fallback environment variables for development');
  console.log('If you see validation errors above, they can be safely ignored in development.');
}

export { env };