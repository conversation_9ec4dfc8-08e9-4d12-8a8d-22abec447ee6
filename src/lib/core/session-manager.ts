import { db } from "./db"
import { type Session, type User, DeviceType } from "@prisma/client"
import { U<PERSON>arser } from "ua-parser-js"

export interface DeviceInfo {
  deviceId: string
  deviceName: string
  deviceType: DeviceType
  browser: string
  os: string
  ipAddress: string
}

export interface SessionWithUser extends Session {
  user: User
}

export class SessionManager {
  /**
   * Create a new enhanced session with device tracking
   */
  static async createSession(
    userId: string,
    sessionToken: string,
    expires: Date,
    deviceInfo: DeviceInfo
  ): Promise<Session> {
    // Check concurrent session limits
    await this.enforceSessionLimits(userId)

    return await db.session.create({
      data: {
        userId,
        sessionToken,
        expires,
        deviceId: deviceInfo.deviceId,
        deviceName: deviceInfo.deviceName,
        deviceType: deviceInfo.deviceType,
        browser: deviceInfo.browser,
        os: deviceInfo.os,
        ipAddress: deviceInfo.ipAddress,
        isActive: true,
        lastActivity: new Date(),
      },
    })
  }

  /**
   * Update session activity and check for timeout
   */
  static async updateSessionActivity(
    sessionToken: string,
    ipAddress?: string
  ): Promise<Session | null> {
    const session = await db.session.findUnique({
      where: { sessionToken },
      include: { user: true }
    })

    if (!session || !session.isActive) {
      return null
    }

    // Check if session has timed out
    const now = new Date()
    const timeoutMinutes = session.user.sessionTimeoutMinutes
    const lastActivity = new Date(session.lastActivity)
    const timeoutThreshold = new Date(lastActivity.getTime() + timeoutMinutes * 60 * 1000)

    if (now > timeoutThreshold) {
      // Session has timed out
      await this.deactivateSession(sessionToken, "timeout")
      return null
    }

    // Update last activity
    return await db.session.update({
      where: { sessionToken },
      data: {
        lastActivity: now,
        ...(ipAddress && { ipAddress })
      },
    })
  }

  /**
   * Deactivate a session
   */
  static async deactivateSession(
    sessionToken: string,
    reason: "logout" | "timeout" | "security" | "admin" = "logout"
  ): Promise<void> {
    await db.session.update({
      where: { sessionToken },
      data: { isActive: false },
    })

    // Log the session termination
    const session = await db.session.findUnique({
      where: { sessionToken },
      include: { user: true }
    })

    if (session) {
      await db.auditLog.create({
        data: {
          userId: session.userId,
          userEmail: session.user.email,
          userName: session.user.name,
          organizationId: session.user.organizationId,
          action: "USER_LOGOUT",
          resourceType: "AUTHENTICATION",
          resourceId: session.id,
          ipAddress: session.ipAddress,
          metadata: { reason, sessionId: session.id },
          success: true,
        },
      })
    }
  }

  /**
   * Enforce concurrent session limits for a user
   */
  static async enforceSessionLimits(userId: string): Promise<void> {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { maxConcurrentSessions: true }
    })

    if (!user) return

    const activeSessions = await db.session.findMany({
      where: {
        userId,
        isActive: true,
        expires: { gt: new Date() }
      },
      orderBy: { lastActivity: "asc" }
    })

    // If we're at or over the limit, deactivate oldest sessions
    const limit = user.maxConcurrentSessions
    if (activeSessions.length >= limit) {
      const sessionsToDeactivate = activeSessions.slice(0, activeSessions.length - limit + 1)
      
      for (const session of sessionsToDeactivate) {
        await this.deactivateSession(session.sessionToken, "security")
      }
    }
  }

  /**
   * Get all active sessions for a user
   */
  static async getUserSessions(userId: string): Promise<Session[]> {
    return await db.session.findMany({
      where: {
        userId,
        isActive: true,
        expires: { gt: new Date() }
      },
      orderBy: { lastActivity: "desc" }
    })
  }

  /**
   * Terminate all sessions for a user (except current one)
   */
  static async terminateAllUserSessions(
    userId: string,
    exceptSessionToken?: string
  ): Promise<number> {
    const sessions = await db.session.findMany({
      where: {
        userId,
        isActive: true,
        ...(exceptSessionToken && {
          sessionToken: { not: exceptSessionToken }
        })
      }
    })

    for (const session of sessions) {
      await this.deactivateSession(session.sessionToken, "admin")
    }

    return sessions.length
  }

  /**
   * Parse user agent and create device info
   */
  static parseDeviceInfo(
    userAgent: string,
    ipAddress: string,
    deviceId?: string
  ): DeviceInfo {
    const parser = new UAParser(userAgent)
    const result = parser.getResult()

    // Generate device ID if not provided
    const finalDeviceId = deviceId || this.generateDeviceId(userAgent, ipAddress)

    // Determine device type
    let deviceType: DeviceType = DeviceType.UNKNOWN
    if (result.device.type === "mobile") {
      deviceType = DeviceType.MOBILE
    } else if (result.device.type === "tablet") {
      deviceType = DeviceType.TABLET
    } else {
      deviceType = DeviceType.DESKTOP
    }

    // Create device name
    const deviceName = this.createDeviceName(result)

    return {
      deviceId: finalDeviceId,
      deviceName,
      deviceType,
      browser: `${result.browser.name || "Unknown"} ${result.browser.version || ""}`.trim(),
      os: `${result.os.name || "Unknown"} ${result.os.version || ""}`.trim(),
      ipAddress,
    }
  }

  /**
   * Generate a consistent device ID based on user agent and IP
   */
  private static generateDeviceId(userAgent: string, ipAddress: string): string {
    const crypto = require("crypto")
    const hash = crypto.createHash("sha256")
    hash.update(userAgent + ipAddress)
    return hash.digest("hex").substring(0, 16)
  }

  /**
   * Create a user-friendly device name
   */
  private static createDeviceName(parsedUA: any): string {
    const { browser, os, device } = parsedUA
    
    if (device.vendor && device.model) {
      return `${device.vendor} ${device.model}`
    }
    
    if (browser.name && os.name) {
      return `${browser.name} on ${os.name}`
    }
    
    return browser.name || os.name || "Unknown Device"
  }

  /**
   * Cleanup expired sessions
   */
  static async cleanupExpiredSessions(): Promise<number> {
    const result = await db.session.deleteMany({
      where: {
        OR: [
          { expires: { lt: new Date() } },
          { 
            AND: [
              { isActive: false },
              { lastActivity: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } } // 30 days old
            ]
          }
        ]
      }
    })

    return result.count
  }
}