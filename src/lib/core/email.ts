import { Resend } from 'resend';
import { env } from './env';

// Initialize Resend client
const resend = env.RESEND_API_KEY ? new Resend(env.RESEND_API_KEY) : null;

// Email templates
export const emailTemplates = {
  passwordReset: (resetLink: string, userEmail: string) => ({
    subject: 'Reset Your Sourceflex Password',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8fafc;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: white; border-radius: 8px; padding: 40px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="text-align: center; margin-bottom: 32px;">
                <h1 style="color: #1e293b; font-size: 24px; font-weight: 600; margin: 0;">
                  🔐 Sourceflex
                </h1>
              </div>
              
              <h2 style="color: #334155; font-size: 20px; font-weight: 600; margin: 0 0 16px 0;">
                Reset Your Password
              </h2>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 24px 0;">
                We received a request to reset the password for your Sourceflex account (<strong>${userEmail}</strong>).
              </p>
              
              <div style="text-align: center; margin: 32px 0;">
                <a href="${resetLink}" 
                   style="display: inline-block; background: #3b82f6; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 500; font-size: 16px;">
                  Reset Password
                </a>
              </div>
              
              <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 24px 0 0 0;">
                This link will expire in 1 hour for security reasons. If you didn't request this password reset, you can safely ignore this email.
              </p>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;">
              
              <p style="color: #94a3b8; font-size: 12px; text-align: center; margin: 0;">
                © 2025 Sourceflex. Professional recruitment platform.
              </p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
Reset Your Sourceflex Password

We received a request to reset the password for your account (${userEmail}).

Click the link below to reset your password:
${resetLink}

This link will expire in 1 hour for security reasons.

If you didn't request this password reset, you can safely ignore this email.

© 2025 Sourceflex
    `.trim()
  }),

  userInvitation: (invitationLink: string, organizationName: string, inviterName: string, userEmail: string, role: string) => ({
    subject: `You're invited to join ${organizationName} on Sourceflex`,
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>You're Invited to Sourceflex</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8fafc;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: white; border-radius: 8px; padding: 40px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="text-align: center; margin-bottom: 32px;">
                <h1 style="color: #1e293b; font-size: 24px; font-weight: 600; margin: 0;">
                  🚀 Sourceflex
                </h1>
              </div>
              
              <h2 style="color: #334155; font-size: 20px; font-weight: 600; margin: 0 0 16px 0;">
                You're Invited!
              </h2>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;">
                <strong>${inviterName}</strong> has invited you to join <strong>${organizationName}</strong> on Sourceflex as a <strong>${role.replace('_', ' ').toLowerCase()}</strong>.
              </p>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 24px 0;">
                Sourceflex is a comprehensive recruitment platform that streamlines hiring, candidate management, and team collaboration.
              </p>
              
              <div style="background: #f1f5f9; border-radius: 6px; padding: 20px; margin: 24px 0;">
                <h3 style="color: #334155; font-size: 16px; font-weight: 600; margin: 0 0 12px 0;">
                  Your Account Details:
                </h3>
                <p style="color: #64748b; font-size: 14px; margin: 0 0 8px 0;">
                  <strong>Email:</strong> ${userEmail}
                </p>
                <p style="color: #64748b; font-size: 14px; margin: 0 0 8px 0;">
                  <strong>Organization:</strong> ${organizationName}
                </p>
                <p style="color: #64748b; font-size: 14px; margin: 0;">
                  <strong>Role:</strong> ${role.replace('_', ' ')}
                </p>
              </div>
              
              <div style="text-align: center; margin: 32px 0;">
                <a href="${invitationLink}" 
                   style="display: inline-block; background: #10b981; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 500; font-size: 16px;">
                  Accept Invitation
                </a>
              </div>
              
              <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 24px 0 0 0;">
                This invitation will expire in 7 days. If you have any questions, please contact your administrator.
              </p>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;">
              
              <p style="color: #94a3b8; font-size: 12px; text-align: center; margin: 0;">
                © 2025 Sourceflex. Professional recruitment platform.
              </p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
You're Invited to Join ${organizationName} on Sourceflex

${inviterName} has invited you to join ${organizationName} on Sourceflex as a ${role.replace('_', ' ').toLowerCase()}.

Your Account Details:
- Email: ${userEmail}
- Organization: ${organizationName}
- Role: ${role.replace('_', ' ')}

Accept your invitation here:
${invitationLink}

This invitation will expire in 7 days.

© 2025 Sourceflex
    `.trim()
  }),

  emailVerification: (verificationCode: string, userEmail: string, userName: string) => ({
    subject: 'Your Sourceflex Verification Code',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Account</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8fafc;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: white; border-radius: 8px; padding: 40px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="text-align: center; margin-bottom: 32px;">
                <h1 style="color: #1e293b; font-size: 24px; font-weight: 600; margin: 0;">
                  ✉️ Sourceflex
                </h1>
              </div>
              
              <h2 style="color: #334155; font-size: 20px; font-weight: 600; margin: 0 0 16px 0;">
                Verify Your Email Address
              </h2>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;">
                Hi ${userName || 'there'}! Welcome to Sourceflex.
              </p>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 24px 0;">
                To complete your account setup and start using Sourceflex, please enter the verification code below in your browser:
              </p>
              
              <div style="background: #f0f9ff; border: 2px solid #3b82f6; border-radius: 8px; padding: 24px; margin: 32px 0; text-align: center;">
                <p style="color: #1e40af; font-size: 14px; font-weight: 500; margin: 0 0 8px 0; text-transform: uppercase; letter-spacing: 1px;">
                  Verification Code
                </p>
                <div style="font-size: 32px; font-weight: 700; color: #1e40af; letter-spacing: 6px; font-family: 'Courier New', monospace;">
                  ${verificationCode}
                </div>
                <p style="color: #64748b; font-size: 12px; margin: 8px 0 0 0;">
                  Enter this code in your browser to verify ${userEmail}
                </p>
              </div>
              
              <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 12px 16px; margin: 24px 0;">
                <p style="color: #92400e; font-size: 14px; margin: 0;">
                  <strong>Important:</strong> This code expires in 15 minutes for security.
                </p>
              </div>
              
              <p style="color: #64748b; font-size: 14px; line-height: 1.5; margin: 24px 0 0 0;">
                If you didn't create an account with Sourceflex, you can safely ignore this email.
              </p>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;">
              
              <p style="color: #94a3b8; font-size: 12px; text-align: center; margin: 0;">
                © 2025 Sourceflex. Professional recruitment platform.
              </p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
Your Sourceflex Verification Code

Hi ${userName || 'there'}! Welcome to Sourceflex.

To complete your account setup and start using Sourceflex, please enter the verification code below:

Verification Code: ${verificationCode}

This code expires in 15 minutes for security reasons.

If you didn't create an account with Sourceflex, you can safely ignore this email.

© 2025 Sourceflex
    `.trim()
  }),

  twoFactorEmail: (verificationCode: string, userEmail: string, userName: string, deviceInfo?: string) => ({
    subject: 'Sourceflex Security Code',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Security Code</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8fafc;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: white; border-radius: 8px; padding: 40px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="text-align: center; margin-bottom: 32px;">
                <h1 style="color: #1e293b; font-size: 24px; font-weight: 600; margin: 0;">
                  🔐 Sourceflex
                </h1>
              </div>
              
              <h2 style="color: #334155; font-size: 20px; font-weight: 600; margin: 0 0 16px 0;">
                Security Code
              </h2>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;">
                Hi ${userName || 'there'}! Someone is trying to sign in to your Sourceflex account.
              </p>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 24px 0;">
                If this is you, enter the security code below to complete your sign-in:
              </p>
              
              <div style="background: #f0f9ff; border: 2px solid #3b82f6; border-radius: 8px; padding: 24px; margin: 32px 0; text-align: center;">
                <p style="color: #1e40af; font-size: 14px; font-weight: 500; margin: 0 0 8px 0; text-transform: uppercase; letter-spacing: 1px;">
                  Security Code
                </p>
                <div style="font-size: 32px; font-weight: 700; color: #1e40af; letter-spacing: 6px; font-family: 'Courier New', monospace;">
                  ${verificationCode}
                </div>
                <p style="color: #64748b; font-size: 12px; margin: 8px 0 0 0;">
                  Enter this code in your browser to sign in
                </p>
              </div>
              
              ${deviceInfo ? `
              <div style="background: #f1f5f9; border-radius: 6px; padding: 16px; margin: 24px 0;">
                <p style="color: #334155; font-size: 14px; font-weight: 500; margin: 0 0 8px 0;">
                  Sign-in attempt details:
                </p>
                <p style="color: #64748b; font-size: 12px; margin: 0;">
                  ${deviceInfo}
                </p>
              </div>
              ` : ''}
              
              <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 12px 16px; margin: 24px 0;">
                <p style="color: #92400e; font-size: 14px; margin: 0;">
                  <strong>Important:</strong> This code expires in 10 minutes. If you didn't try to sign in, ignore this email and consider changing your password.
                </p>
              </div>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;">
              
              <p style="color: #94a3b8; font-size: 12px; text-align: center; margin: 0;">
                © 2025 Sourceflex. Professional recruitment platform.
              </p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
Sourceflex Security Code

Hi ${userName || 'there'}! Someone is trying to sign in to your Sourceflex account.

If this is you, enter the security code below to complete your sign-in:

Security Code: ${verificationCode}

${deviceInfo ? `Sign-in attempt details: ${deviceInfo}` : ''}

This code expires in 10 minutes. If you didn't try to sign in, ignore this email and consider changing your password.

© 2025 Sourceflex
    `.trim()
  }),

  welcomeCandidate: (userEmail: string, userName: string) => ({
    subject: 'Welcome to Sourceflex - Your Job Search Journey Begins!',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Sourceflex</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8fafc;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: white; border-radius: 8px; padding: 40px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="text-align: center; margin-bottom: 32px;">
                <h1 style="color: #1e293b; font-size: 24px; font-weight: 600; margin: 0;">
                  🎉 Welcome to Sourceflex!
                </h1>
              </div>
              
              <h2 style="color: #334155; font-size: 20px; font-weight: 600; margin: 0 0 16px 0;">
                Hi ${userName}!
              </h2>
              
              <p style="color: #64748b; font-size: 16px; line-height: 1.5; margin: 0 0 16px 0;">
                Welcome to Sourceflex, the premier platform connecting talented candidates with leading companies. We're excited to help you find your next career opportunity!
              </p>
              
              <div style="background: #f0f9ff; border-left: 4px solid #3b82f6; padding: 16px; margin: 24px 0;">
                <h3 style="color: #1e40af; font-size: 16px; font-weight: 600; margin: 0 0 8px 0;">
                  🚀 Get Started
                </h3>
                <p style="color: #1e40af; font-size: 14px; margin: 0;">
                  Complete your profile and start browsing thousands of job opportunities from top companies.
                </p>
              </div>
              
              <div style="margin: 24px 0;">
                <h3 style="color: #334155; font-size: 16px; font-weight: 600; margin: 0 0 16px 0;">
                  What you can do on Sourceflex:
                </h3>
                <ul style="color: #64748b; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                  <li>Browse and apply to thousands of job opportunities</li>
                  <li>Get matched with positions that fit your skills</li>
                  <li>Track your application status in real-time</li>
                  <li>Connect directly with hiring managers and recruiters</li>
                  <li>Build a professional profile that showcases your expertise</li>
                </ul>
              </div>
              
              <div style="text-align: center; margin: 32px 0;">
                <a href="${env.NEXTAUTH_URL}/jobs" 
                   style="display: inline-block; background: #3b82f6; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 500; font-size: 16px; margin-right: 12px;">
                  Browse Jobs
                </a>
                <a href="${env.NEXTAUTH_URL}/profile" 
                   style="display: inline-block; background: #f8fafc; color: #334155; text-decoration: none; padding: 12px 24px; border-radius: 6px; font-weight: 500; font-size: 16px; border: 1px solid #e2e8f0;">
                  Complete Profile
                </a>
              </div>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;">
              
              <p style="color: #94a3b8; font-size: 12px; text-align: center; margin: 0;">
                © 2025 Sourceflex. Professional recruitment platform.
              </p>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
Welcome to Sourceflex!

Hi ${userName}!

Welcome to Sourceflex, the premier platform connecting talented candidates with leading companies. We're excited to help you find your next career opportunity!

What you can do on Sourceflex:
- Browse and apply to thousands of job opportunities
- Get matched with positions that fit your skills  
- Track your application status in real-time
- Connect directly with hiring managers and recruiters
- Build a professional profile that showcases your expertise

Get started by browsing jobs: ${env.NEXTAUTH_URL}/jobs
Complete your profile: ${env.NEXTAUTH_URL}/profile

© 2025 Sourceflex
    `.trim()
  }),
};

// Email sending functions
export async function sendPasswordResetEmail(email: string, resetToken: string): Promise<boolean> {
  const resetLink = `${env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`;
  const template = emailTemplates.passwordReset(resetLink, email);

  return await sendEmail({
    to: email,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

export async function sendUserInvitationEmail(
  email: string,
  invitationToken: string,
  organizationName: string,
  inviterName: string,
  role: string
): Promise<boolean> {
  const invitationLink = `${env.NEXTAUTH_URL}/auth/accept-invitation?token=${invitationToken}`;
  const template = emailTemplates.userInvitation(invitationLink, organizationName, inviterName, email, role);

  return await sendEmail({
    to: email,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

export async function sendEmailVerification(email: string, verificationCode: string, userName?: string): Promise<boolean> {
  const template = emailTemplates.emailVerification(verificationCode, email, userName || '');

  return await sendEmail({
    to: email,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

export async function sendTwoFactorEmail(email: string, verificationCode: string, userName?: string, deviceInfo?: string): Promise<boolean> {
  const template = emailTemplates.twoFactorEmail(verificationCode, email, userName || '', deviceInfo);

  return await sendEmail({
    to: email,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

export async function sendWelcomeEmail(email: string, name: string): Promise<boolean> {
  const template = emailTemplates.welcomeCandidate(email, name);

  return await sendEmail({
    to: email,
    subject: template.subject,
    html: template.html,
    text: template.text,
  });
}

// Generic email sending function
export async function sendEmail(options: {
  to: string;
  subject: string;
  html: string;
  text: string;
  from?: string;
}): Promise<boolean> {
  // In development, log emails to console if Resend is not configured
  if (!resend) {
    console.log('\n📧 Email would be sent (Resend not configured):');
    console.log('From:', options.from || env.FROM_EMAIL);
    console.log('To:', options.to);
    console.log('Subject:', options.subject);
    console.log('Text Version:');
    console.log('─'.repeat(50));
    console.log(options.text);
    console.log('─'.repeat(50));
    console.log('💡 To send real emails, set RESEND_API_KEY in your .env file');
    return true;
  }

  // Log email attempts in development for debugging
  if (env.NODE_ENV === 'development') {
    console.log('\n📧 Sending email via Resend:');
    console.log('From:', options.from || env.FROM_EMAIL);
    console.log('To:', options.to);
    console.log('Subject:', options.subject);
  }

  try {
    const result = await resend.emails.send({
      from: options.from || env.FROM_EMAIL,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    });

    if (result.error) {
      console.error('❌ Email sending failed:', result.error);
      return false;
    }

    console.log('✅ Email sent successfully:', result.data?.id);
    return true;
  } catch (error) {
    console.error('❌ Email sending error:', error);
    return false;
  }
}

// Email validation helper
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Business email validation (excludes common personal domains)
export function isBusinessEmail(email: string): boolean {
  if (!isValidEmail(email)) {
    return false;
  }

  const domain = email.split('@')[1]?.toLowerCase();
  const personalDomains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'icloud.com',
    'aol.com',
    'protonmail.com',
    'mail.com',
  ];

  return !personalDomains.includes(domain || '');
}