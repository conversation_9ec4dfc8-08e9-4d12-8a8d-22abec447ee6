import { db } from "./db"
import { type SecurityPolicy, type Organization } from "@prisma/client"
import { z } from "zod"

export const securityPolicySchema = z.object({
  // Password policies
  minPasswordLength: z.number().min(8).max(50).default(8),
  requireUppercase: z.boolean().default(true),
  requireLowercase: z.boolean().default(true),
  requireNumbers: z.boolean().default(true),
  requireSpecialChars: z.boolean().default(true),
  passwordHistoryCount: z.number().min(0).max(24).default(5),
  passwordExpiryDays: z.number().min(30).max(365).nullable().default(null),
  
  // 2FA policies
  require2FA: z.boolean().default(false),
  require2FAForAdmins: z.boolean().default(true),
  allowSMS2FA: z.boolean().default(true),
  allowTOTP2FA: z.boolean().default(true),
  allowWebAuthn: z.boolean().default(true),
  
  // Session policies
  maxConcurrentSessions: z.number().min(1).max(50).default(5),
  sessionTimeoutMinutes: z.number().min(15).max(1440).default(480), // 8 hours
  requireReauthForSensitive: z.boolean().default(true),
  
  // IP and access control
  allowedIpRanges: z.array(z.string()).default([]),
  blockedIpRanges: z.array(z.string()).default([]),
  
  // Brute force protection
  maxFailedAttempts: z.number().min(3).max(20).default(5),
  lockoutDurationMinutes: z.number().min(5).max(1440).default(30),
})

export type SecurityPolicyInput = z.infer<typeof securityPolicySchema>

export class SecurityPolicyService {
  /**
   * Get security policy for an organization (with fallback to default)
   */
  static async getPolicy(organizationId: string): Promise<SecurityPolicy> {
    let policy = await db.securityPolicy.findUnique({
      where: { organizationId }
    })

    if (!policy) {
      // Create default policy for organization
      policy = await this.createDefaultPolicy(organizationId)
    }

    return policy
  }

  /**
   * Get default security policy (for system-wide defaults)
   */
  static getDefaultPolicyValues(): SecurityPolicyInput {
    return securityPolicySchema.parse({})
  }

  /**
   * Create default security policy for an organization
   */
  static async createDefaultPolicy(organizationId: string): Promise<SecurityPolicy> {
    const defaults = this.getDefaultPolicyValues()
    
    return await db.securityPolicy.create({
      data: {
        organizationId,
        ...defaults,
      }
    })
  }

  /**
   * Update security policy for an organization
   */
  static async updatePolicy(
    organizationId: string,
    updates: Partial<SecurityPolicyInput>,
    updatedBy: string
  ): Promise<SecurityPolicy> {
    const validatedUpdates = securityPolicySchema.partial().parse(updates)
    
    // Get existing policy or create default
    const existingPolicy = await this.getPolicy(organizationId)
    
    const updatedPolicy = await db.securityPolicy.update({
      where: { organizationId },
      data: validatedUpdates
    })

    // Log the policy change
    await db.auditLog.create({
      data: {
        userId: updatedBy,
        organizationId,
        action: "ORGANIZATION_SETTINGS_UPDATED",
        resourceType: "ORGANIZATION",
        resourceId: organizationId,
        oldValues: existingPolicy,
        newValues: updatedPolicy,
        success: true,
      }
    })

    return updatedPolicy
  }

  /**
   * Validate password against organization policy
   */
  static async validatePassword(
    password: string,
    organizationId: string,
    userId?: string
  ): Promise<{ valid: boolean; errors: string[] }> {
    const policy = await this.getPolicy(organizationId)
    const errors: string[] = []

    // Length check
    if (password.length < policy.minPasswordLength) {
      errors.push(`Password must be at least ${policy.minPasswordLength} characters long`)
    }

    // Character requirements
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter")
    }

    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter")
    }

    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push("Password must contain at least one number")
    }

    if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push("Password must contain at least one special character")
    }

    // Check password history if user is provided
    if (userId && policy.passwordHistoryCount > 0) {
      const isReused = await this.checkPasswordHistory(userId, password, policy.passwordHistoryCount)
      if (isReused) {
        errors.push(`Password cannot be one of your last ${policy.passwordHistoryCount} passwords`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Check if IP address is allowed by organization policy
   */
  static async isIpAddressAllowed(
    ipAddress: string,
    organizationId: string
  ): Promise<{ allowed: boolean; reason?: string }> {
    const policy = await this.getPolicy(organizationId)

    // Check blocked ranges first
    if (policy.blockedIpRanges.length > 0) {
      for (const range of policy.blockedIpRanges) {
        if (this.ipInRange(ipAddress, range)) {
          return { allowed: false, reason: "IP address is blocked" }
        }
      }
    }

    // Check allowed ranges (if any are defined)
    if (policy.allowedIpRanges.length > 0) {
      let isInAllowedRange = false
      for (const range of policy.allowedIpRanges) {
        if (this.ipInRange(ipAddress, range)) {
          isInAllowedRange = true
          break
        }
      }
      
      if (!isInAllowedRange) {
        return { allowed: false, reason: "IP address not in allowed range" }
      }
    }

    return { allowed: true }
  }

  /**
   * Check if user needs 2FA based on organization policy
   */
  static async requires2FA(userId: string, organizationId: string): Promise<boolean> {
    const policy = await this.getPolicy(organizationId)
    
    if (policy.require2FA) {
      return true
    }

    if (policy.require2FAForAdmins) {
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { role: true }
      })

      if (user && ["SUPER_ADMIN", "ORG_ADMIN"].includes(user.role)) {
        return true
      }
    }

    return false
  }

  /**
   * Get session timeout for user based on organization policy
   */
  static async getSessionTimeout(userId: string): Promise<number> {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { 
        sessionTimeoutMinutes: true,
        organizationId: true 
      }
    })

    if (!user) return 480 // Default 8 hours

    // User-specific setting takes precedence
    if (user.sessionTimeoutMinutes !== 480) { // If not default
      return user.sessionTimeoutMinutes
    }

    // Fall back to organization policy
    if (user.organizationId) {
      const policy = await this.getPolicy(user.organizationId)
      return policy.sessionTimeoutMinutes
    }

    return 480 // Default 8 hours
  }

  /**
   * Private helper: Check if password was used recently
   */
  private static async checkPasswordHistory(
    userId: string,
    newPassword: string,
    historyCount: number
  ): Promise<boolean> {
    // This would require storing password hashes in a separate table
    // For now, we'll implement this as a placeholder
    // In production, you'd store bcrypt hashes of previous passwords
    
    const passwordHistory = await db.auditLog.findMany({
      where: {
        userId,
        action: "PASSWORD_RESET_COMPLETED",
        createdAt: { gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) } // Last year
      },
      orderBy: { createdAt: "desc" },
      take: historyCount
    })

    // In a real implementation, you'd compare bcrypt hashes
    // For now, this is a placeholder
    return false
  }

  /**
   * Private helper: Check if IP is in CIDR range
   */
  private static ipInRange(ipAddress: string, cidrRange: string): boolean {
    try {
      const { Address4, Address6 } = require("ip-address")
      
      // Try IPv4 first
      try {
        const ip = new Address4(ipAddress)
        const range = new Address4(cidrRange)
        return ip.isInSubnet(range)
      } catch {
        // Try IPv6
        const ip = new Address6(ipAddress)
        const range = new Address6(cidrRange)
        return ip.isInSubnet(range)
      }
    } catch {
      // If IP parsing fails, deny access
      return false
    }
  }

  /**
   * Apply security policies to user during login
   */
  static async applyPolicesToUser(
    userId: string,
    organizationId: string
  ): Promise<void> {
    const policy = await this.getPolicy(organizationId)
    
    await db.user.update({
      where: { id: userId },
      data: {
        maxConcurrentSessions: policy.maxConcurrentSessions,
        sessionTimeoutMinutes: policy.sessionTimeoutMinutes,
      }
    })
  }

  /**
   * Get security recommendations for an organization
   */
  static async getSecurityRecommendations(
    organizationId: string
  ): Promise<Array<{ type: string; priority: "high" | "medium" | "low"; message: string }>> {
    const policy = await this.getPolicy(organizationId)
    const recommendations: Array<{ type: string; priority: "high" | "medium" | "low"; message: string }> = []

    // Check for weak password policies
    if (policy.minPasswordLength < 12) {
      recommendations.push({
        type: "password_policy",
        priority: "medium",
        message: "Consider increasing minimum password length to 12 characters for better security"
      })
    }

    if (!policy.require2FA && !policy.require2FAForAdmins) {
      recommendations.push({
        type: "2fa_policy",
        priority: "high",
        message: "Enable two-factor authentication for administrators to improve security"
      })
    }

    if (policy.maxFailedAttempts > 10) {
      recommendations.push({
        type: "brute_force",
        priority: "medium",
        message: "Consider reducing maximum failed login attempts to prevent brute force attacks"
      })
    }

    if (policy.sessionTimeoutMinutes > 720) { // More than 12 hours
      recommendations.push({
        type: "session_timeout",
        priority: "medium",
        message: "Consider reducing session timeout for better security"
      })
    }

    return recommendations
  }
}