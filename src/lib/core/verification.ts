import { db } from "./db"

// Generate a 6-digit verification code
function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Create a verification code
export async function createVerificationCode(email: string): Promise<string> {
  // Generate a 6-digit verification code
  const code = generateVerificationCode()
  
  // Code expires in 15 minutes for security
  const expires = new Date(Date.now() + 15 * 60 * 1000)
  
  // Delete any existing verification codes for this email
  await db.verificationToken.deleteMany({
    where: { identifier: email }
  })
  
  // Create new verification code
  await db.verificationToken.create({
    data: {
      identifier: email,
      token: code,
      expires,
    }
  })
  
  return code
}

// Verify email with code
export async function verifyEmailCode(email: string, code: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Find the verification code for this email
    const verificationToken = await db.verificationToken.findFirst({
      where: { 
        identifier: email,
        token: code 
      }
    })
    
    if (!verificationToken) {
      return { success: false, error: "Invalid verification code" }
    }
    
    // Check if code has expired
    if (verificationToken.expires < new Date()) {
      // Delete expired code
      await db.verificationToken.deleteMany({
        where: { identifier: email }
      })
      return { success: false, error: "Verification code has expired. Please request a new one." }
    }
    
    // Update user's emailVerified field
    await db.user.update({
      where: { email },
      data: { emailVerified: new Date() }
    })
    
    // Delete the used code
    await db.verificationToken.deleteMany({
      where: { identifier: email }
    })
    
    return { success: true }
  } catch (error) {
    console.error("Email verification error:", error)
    return { success: false, error: "An error occurred during verification" }
  }
}

// Resend verification code
export async function canResendVerification(email: string): Promise<{ canResend: boolean; waitMinutes?: number }> {
  // Check for existing verification code
  const existingToken = await db.verificationToken.findFirst({
    where: { identifier: email },
    orderBy: { expires: 'desc' }
  })
  
  if (!existingToken) {
    return { canResend: true }
  }
  
  // Allow resending if code is older than 2 minutes (to prevent spam)
  const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000)
  const codeCreatedAt = new Date(existingToken.expires.getTime() - 15 * 60 * 1000) // Code expires in 15 min
  
  if (codeCreatedAt < twoMinutesAgo) {
    return { canResend: true }
  }
  
  const waitMinutes = Math.ceil((2 * 60 * 1000 - (Date.now() - codeCreatedAt.getTime())) / (60 * 1000))
  return { canResend: false, waitMinutes }
}

// Check if user's email is verified
export async function isEmailVerified(email: string): Promise<boolean> {
  const user = await db.user.findUnique({
    where: { email },
    select: { emailVerified: true }
  })
  
  return !!user?.emailVerified
}