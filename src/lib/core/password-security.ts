import crypto from "crypto"
import { SecurityPolicyService } from "./security-policy"

export interface PasswordBreachResult {
  isBreached: boolean
  breachCount?: number
  error?: string
}

export interface PasswordStrengthResult {
  score: number // 0-100
  feedback: string[]
  isStrong: boolean
}

export interface PasswordValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  strength: PasswordStrengthResult
  breach: PasswordBreachResult
}

export class PasswordSecurityService {
  private static readonly HIBP_API_URL = "https://api.pwnedpasswords.com/range"
  private static readonly CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours
  private static cache = new Map<string, { result: PasswordBreachResult; timestamp: number }>()

  /**
   * Check if password has been compromised in data breaches
   */
  static async checkPasswordBreach(password: string): Promise<PasswordBreachResult> {
    try {
      // Generate SHA-1 hash of password
      const hash = crypto.createHash("sha1").update(password).digest("hex").toUpperCase()
      const hashPrefix = hash.substring(0, 5)
      const hashSuffix = hash.substring(5)

      // Check cache first
      const cacheKey = hashPrefix
      const cached = this.cache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return this.findHashInResponse(cached.result as any, hashSuffix)
      }

      // Query HaveIBeenPwned API
      const response = await fetch(`${this.HIBP_API_URL}/${hashPrefix}`, {
        headers: {
          "User-Agent": "Sourceflex-Password-Check",
        },
        // Add timeout
        signal: AbortSignal.timeout(5000),
      })

      if (!response.ok) {
        if (response.status === 429) {
          return {
            isBreached: false,
            error: "Rate limited by breach detection service",
          }
        }
        throw new Error(`HTTP ${response.status}`)
      }

      const responseText = await response.text()
      
      // Cache the response
      this.cache.set(cacheKey, {
        result: responseText as any,
        timestamp: Date.now(),
      })

      return this.findHashInResponse(responseText, hashSuffix)
    } catch (error) {
      console.error("Password breach check failed:", error)
      return {
        isBreached: false,
        error: "Failed to check password against breach database",
      }
    }
  }

  /**
   * Analyze password strength
   */
  static analyzePasswordStrength(password: string): PasswordStrengthResult {
    const feedback: string[] = []
    let score = 0

    // Length scoring (0-40 points)
    if (password.length >= 12) {
      score += 25
    } else if (password.length >= 8) {
      score += 15
    } else if (password.length >= 6) {
      score += 5
      feedback.push("Password should be at least 8 characters long")
    } else {
      feedback.push("Password is too short (minimum 6 characters)")
    }

    // Character variety (0-30 points)
    let varietyScore = 0
    if (/[a-z]/.test(password)) varietyScore += 5
    if (/[A-Z]/.test(password)) varietyScore += 5
    if (/\d/.test(password)) varietyScore += 5
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) varietyScore += 10
    if (/[^\w\s]/.test(password)) varietyScore += 5 // Other special characters

    score += varietyScore
    if (varietyScore < 20) {
      feedback.push("Use a mix of uppercase, lowercase, numbers, and special characters")
    }

    // Pattern detection (-10 to +10 points)
    if (this.hasCommonPatterns(password)) {
      score -= 10
      feedback.push("Avoid common patterns like '123', 'abc', or keyboard sequences")
    }

    if (this.hasRepeatingCharacters(password)) {
      score -= 5
      feedback.push("Avoid repeating characters")
    }

    // Dictionary word detection (-15 points)
    if (this.containsCommonWords(password)) {
      score -= 15
      feedback.push("Avoid using common words or personal information")
    }

    // Entropy bonus (0-20 points)
    const entropy = this.calculateEntropy(password)
    if (entropy >= 60) {
      score += 20
    } else if (entropy >= 40) {
      score += 10
    } else if (entropy >= 25) {
      score += 5
    }

    // Cap score at 100
    score = Math.min(100, Math.max(0, score))

    const isStrong = score >= 70

    if (!isStrong && feedback.length === 0) {
      feedback.push("Consider making your password longer and more complex")
    }

    return {
      score,
      feedback,
      isStrong,
    }
  }

  /**
   * Comprehensive password validation
   */
  static async validatePassword(
    password: string,
    organizationId: string,
    userId?: string
  ): Promise<PasswordValidationResult> {
    const errors: string[] = []
    const warnings: string[] = []

    // Check against organization policy
    const policyValidation = await SecurityPolicyService.validatePassword(
      password,
      organizationId,
      userId
    )

    if (!policyValidation.valid) {
      errors.push(...policyValidation.errors)
    }

    // Check password strength
    const strength = this.analyzePasswordStrength(password)
    if (!strength.isStrong) {
      warnings.push("Password strength could be improved")
    }

    // Check for breaches
    const breach = await this.checkPasswordBreach(password)
    if (breach.isBreached) {
      errors.push(
        `This password has been found in ${breach.breachCount} data breaches and should not be used`
      )
    } else if (breach.error) {
      warnings.push("Could not verify password against breach database")
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      strength,
      breach,
    }
  }

  /**
   * Generate a secure password suggestion
   */
  static generateSecurePassword(length: number = 16): string {
    const lowercase = "abcdefghijklmnopqrstuvwxyz"
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    const numbers = "0123456789"
    const symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    const allChars = lowercase + uppercase + numbers + symbols
    let password = ""

    // Ensure at least one character from each set
    password += this.getRandomChar(lowercase)
    password += this.getRandomChar(uppercase)
    password += this.getRandomChar(numbers)
    password += this.getRandomChar(symbols)

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += this.getRandomChar(allChars)
    }

    // Shuffle the password
    return password
      .split("")
      .sort(() => 0.5 - Math.random())
      .join("")
  }

  /**
   * Private helper: Find hash in HaveIBeenPwned response
   */
  private static findHashInResponse(
    responseText: string,
    hashSuffix: string
  ): PasswordBreachResult {
    const lines = responseText.split("\n")
    
    for (const line of lines) {
      const [suffix, count] = line.split(":")
      if (suffix === hashSuffix) {
        return {
          isBreached: true,
          breachCount: parseInt(count.trim(), 10),
        }
      }
    }

    return { isBreached: false }
  }

  /**
   * Private helper: Check for common patterns
   */
  private static hasCommonPatterns(password: string): boolean {
    const patterns = [
      /123/,
      /abc/i,
      /qwerty/i,
      /password/i,
      /admin/i,
      /login/i,
      /user/i,
      /test/i,
      /guest/i,
      /welcome/i,
      /dragon/i,
      /monkey/i,
      /letmein/i,
      /football/i,
      /basketball/i,
      /baseball/i,
      /soccer/i,
    ]

    return patterns.some(pattern => pattern.test(password))
  }

  /**
   * Private helper: Check for repeating characters
   */
  private static hasRepeatingCharacters(password: string): boolean {
    for (let i = 0; i < password.length - 2; i++) {
      if (password[i] === password[i + 1] && password[i] === password[i + 2]) {
        return true
      }
    }
    return false
  }

  /**
   * Private helper: Check for common words
   */
  private static containsCommonWords(password: string): boolean {
    const commonWords = [
      "password", "admin", "user", "login", "guest", "welcome", "test",
      "dragon", "monkey", "letmein", "football", "basketball", "baseball",
      "soccer", "hockey", "tennis", "golf", "swimming", "running",
      "love", "hate", "happy", "sad", "angry", "excited", "tired",
      "january", "february", "march", "april", "may", "june", "july",
      "august", "september", "october", "november", "december",
      "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
    ]

    const lowerPassword = password.toLowerCase()
    return commonWords.some(word => lowerPassword.includes(word))
  }

  /**
   * Private helper: Calculate password entropy
   */
  private static calculateEntropy(password: string): number {
    const charset = new Set(password).size
    return Math.log2(Math.pow(charset, password.length))
  }

  /**
   * Private helper: Get random character from set
   */
  private static getRandomChar(charset: string): string {
    const randomIndex = crypto.randomInt(0, charset.length)
    return charset[randomIndex]
  }

  /**
   * Clean up old cache entries
   */
  static cleanupCache(): void {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.CACHE_TTL) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * Generate password reset token with enhanced security
   */
  static generateResetToken(): { token: string; hashedToken: string; expires: Date } {
    const token = crypto.randomBytes(32).toString("hex")
    const hashedToken = crypto.createHash("sha256").update(token).digest("hex")
    const expires = new Date(Date.now() + 15 * 60 * 1000) // 15 minutes

    return { token, hashedToken, expires }
  }

  /**
   * Verify password reset token
   */
  static verifyResetToken(token: string, hashedToken: string): boolean {
    const computedHash = crypto.createHash("sha256").update(token).digest("hex")
    return crypto.timingSafeEqual(
      Buffer.from(hashedToken, "hex"),
      Buffer.from(computedHash, "hex")
    )
  }
}