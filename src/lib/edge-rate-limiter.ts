import { type NextRequest } from "next/server"

export interface EdgeRateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (req: NextRequest) => string
}

export interface EdgeRateLimitResult {
  allowed: boolean
  limit: number
  remaining: number
  resetTime: Date
  retryAfter?: number
}

// In-memory storage for edge runtime (no database access)
const rateLimitStore = new Map<string, { count: number; windowStart: number }>()

export class EdgeRateLimiter {
  private config: EdgeRateLimitConfig

  constructor(config: EdgeRateLimitConfig) {
    this.config = {
      keyGenerator: (req) => this.getClientIdentifier(req),
      ...config
    }
  }

  /**
   * Check if request is within rate limits (Edge Runtime compatible)
   */
  checkLimit(req: NextRequest, route: string): EdgeRateLimitResult {
    const identifier = this.config.keyGenerator!(req)
    const key = `${identifier}:${route}`
    const now = Date.now()
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs

    // Clean up old entries periodically
    this.cleanup()

    // Get or create entry
    const existing = rateLimitStore.get(key)
    let count = 1

    if (existing && existing.windowStart === windowStart) {
      count = existing.count + 1
      rateLimitStore.set(key, { count, windowStart })
    } else {
      rateLimitStore.set(key, { count: 1, windowStart })
    }

    const resetTime = new Date(windowStart + this.config.windowMs)
    const remaining = Math.max(0, this.config.maxRequests - count)
    const allowed = count <= this.config.maxRequests

    const result: EdgeRateLimitResult = {
      allowed,
      limit: this.config.maxRequests,
      remaining,
      resetTime
    }

    if (!allowed) {
      result.retryAfter = Math.ceil((resetTime.getTime() - now) / 1000)
    }

    return result
  }

  /**
   * Get client identifier for rate limiting
   */
  private getClientIdentifier(req: NextRequest): string {
    // Try to get real IP from headers (for proxy/load balancer setups)
    const forwardedFor = req.headers.get("x-forwarded-for")
    const realIp = req.headers.get("x-real-ip")
    const cfConnectingIp = req.headers.get("cf-connecting-ip") // Cloudflare
    
    return forwardedFor?.split(",")[0]?.trim() || 
           realIp || 
           cfConnectingIp || 
           "unknown"
  }

  /**
   * Clean up old entries to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now()
    const cutoff = now - (this.config.windowMs * 2) // Keep entries for 2 windows

    for (const [key, entry] of rateLimitStore.entries()) {
      if (entry.windowStart < cutoff) {
        rateLimitStore.delete(key)
      }
    }
  }
}

// Predefined edge-compatible rate limiters
export const edgeRateLimiters = {
  // Authentication endpoints - stricter limits
  auth: new EdgeRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10 // 10 attempts per 15 minutes (more lenient than DB version)
  }),

  // General API endpoints
  api: new EdgeRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100 // 100 requests per 15 minutes
  }),

  // Public endpoints (job listings, etc.)
  public: new EdgeRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 300 // 300 requests per 15 minutes
  })
}

// Helper function for middleware
export function applyEdgeRateLimit(
  req: NextRequest,
  limiterName: keyof typeof edgeRateLimiters,
  route: string
): EdgeRateLimitResult {
  const limiter = edgeRateLimiters[limiterName]
  return limiter.checkLimit(req, route)
}