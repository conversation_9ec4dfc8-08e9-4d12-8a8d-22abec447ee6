import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import { EnhancedRateLimiter, RateLimitConfig } from "./enhanced-rate-limiter"
import { DistributedRateLimiter, createRateLimiter } from "./distributed-rate-limiter"
import { <PERSON>tLogger } from "./audit"

export interface RateLimitMiddlewareConfig extends RateLimitConfig {
  useDistributed?: boolean
  skipSuccessful?: boolean
  skipFailed?: boolean
  bypassForRoles?: string[]
  customIdentifier?: (req: NextRequest, token?: any) => string
}

export interface RateLimitResponse {
  allowed: boolean
  headers: Record<string, string>
  status?: number
  body?: any
}

/**
 * Enhanced rate limiting middleware for API routes with adaptive behavior analysis
 */
export class RateLimitMiddleware {
  private rateLimiter: EnhancedRateLimiter | DistributedRateLimiter
  private config: RateLimitMiddlewareConfig

  constructor(config: RateLimitMiddlewareConfig) {
    this.config = {
      useDistributed: process.env.NODE_ENV === 'production',
      skipSuccessful: false,
      skipFailed: false,
      bypassForRoles: ['SUPER_ADMIN'],
      ...config
    }

    // Create appropriate rate limiter based on configuration
    if (this.config.useDistributed) {
      this.rateLimiter = createRateLimiter({
        ...config,
        useDatabase: true,
        fallbackToLocal: true,
        syncInterval: 30000
      }) as DistributedRateLimiter
    } else {
      this.rateLimiter = new EnhancedRateLimiter(config)
    }
  }

  /**
   * Apply rate limiting to a request
   */
  async applyRateLimit(
    request: NextRequest,
    endpoint: string,
    responseSuccess?: boolean
  ): Promise<RateLimitResponse> {
    try {
      // Get authentication token
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET
      })

      // Check if user should bypass rate limiting
      if (this.shouldBypassRateLimit(token)) {
        return {
          allowed: true,
          headers: {
            'X-RateLimit-Bypassed': 'true',
            'X-RateLimit-Reason': 'privileged_user'
          }
        }
      }

      // Skip counting if configured
      if (responseSuccess !== undefined) {
        if (responseSuccess && this.config.skipSuccessful) {
          return { allowed: true, headers: {} }
        }
        if (!responseSuccess && this.config.skipFailed) {
          return { allowed: true, headers: {} }
        }
      }

      // Determine identifier for rate limiting
      const identifier = this.getIdentifier(request, token)

      // Create context for rate limiting
      const context = {
        userId: token?.sub,
        success: responseSuccess,
        organizationId: token?.organizationId
      }

      // Apply rate limiting
      const result = await this.rateLimiter.checkLimit(request, endpoint, context)

      // Prepare response headers
      const headers = this.createRateLimitHeaders(result)

      if (!result.allowed) {
        // Create rate limit exceeded response
        return {
          allowed: false,
          headers,
          status: 429,
          body: {
            error: "Rate limit exceeded",
            message: result.blockedReason || "Too many requests",
            retryAfter: result.retryAfter,
            riskScore: result.riskScore
          }
        }
      }

      return { allowed: true, headers }

    } catch (error) {
      // Log rate limiting error
      await this.logRateLimitError(request, endpoint, error)

      // Fail open - allow request but without rate limit headers
      return {
        allowed: true,
        headers: {
          'X-RateLimit-Error': 'true'
        }
      }
    }
  }

  /**
   * Wrapper for API route handlers
   */
  withRateLimit<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>,
    endpoint?: string
  ) {
    return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
      const routeEndpoint = endpoint || this.extractEndpoint(request)

      // Apply rate limiting before handler
      const rateLimitResult = await this.applyRateLimit(request, routeEndpoint)

      // If rate limited, return rate limit response
      if (!rateLimitResult.allowed) {
        return NextResponse.json(rateLimitResult.body, {
          status: rateLimitResult.status,
          headers: rateLimitResult.headers
        })
      }

      try {
        // Call the original handler
        const response = await handler(request, ...args)

        // Determine if response was successful
        const success = response.status < 400

        // Apply post-request rate limiting (for success/failure tracking)
        const postRateLimitResult = await this.applyRateLimit(
          request,
          routeEndpoint,
          success
        )

        // Add rate limit headers to response
        const headers = new Headers(response.headers)
        Object.entries({
          ...rateLimitResult.headers,
          ...postRateLimitResult.headers
        }).forEach(([key, value]) => {
          headers.set(key, value)
        })

        return new NextResponse(response.body, {
          status: response.status,
          headers
        })

      } catch (error) {
        // Apply post-request rate limiting for error
        await this.applyRateLimit(request, routeEndpoint, false)
        throw error
      }
    }
  }

  /**
   * Check if user should bypass rate limiting
   */
  private shouldBypassRateLimit(token?: any): boolean {
    if (!token || !this.config.bypassForRoles) return false

    return this.config.bypassForRoles.includes(token.role)
  }

  /**
   * Get identifier for rate limiting
   */
  private getIdentifier(request: NextRequest, token?: any): string {
    if (this.config.customIdentifier) {
      return this.config.customIdentifier(request, token)
    }

    // Prefer user ID for authenticated requests
    if (token?.sub) {
      return `user:${token.sub}`
    }

    // Fall back to IP address
    return this.getClientIP(request)
  }

  /**
   * Extract endpoint name from request
   */
  private extractEndpoint(request: NextRequest): string {
    // Remove query parameters and normalize
    const pathname = request.nextUrl.pathname
    return pathname.replace(/\?.*$/, '')
  }

  /**
   * Get client IP from request headers
   */
  private getClientIP(request: NextRequest): string {
    const forwardedFor = request.headers.get("x-forwarded-for")
    const realIp = request.headers.get("x-real-ip")
    const cfConnectingIp = request.headers.get("cf-connecting-ip")
    
    return forwardedFor?.split(",")[0]?.trim() || 
           realIp || 
           cfConnectingIp || 
           "unknown"
  }

  /**
   * Create rate limit headers for response
   */
  private createRateLimitHeaders(result: any): Record<string, string> {
    const headers: Record<string, string> = {
      'X-RateLimit-Limit': result.limit?.toString() || '0',
      'X-RateLimit-Remaining': result.remaining?.toString() || '0',
      'X-RateLimit-Reset': result.resetTime?.toISOString() || new Date().toISOString()
    }

    if (result.retryAfter) {
      headers['Retry-After'] = result.retryAfter.toString()
    }

    if (result.adaptiveLimit && result.adaptiveLimit !== result.limit) {
      headers['X-RateLimit-Adaptive'] = result.adaptiveLimit.toString()
    }

    if (result.riskScore !== undefined) {
      headers['X-RateLimit-Risk-Score'] = result.riskScore.toFixed(2)
    }

    if (result.blockedReason) {
      headers['X-RateLimit-Block-Reason'] = result.blockedReason
    }

    return headers
  }

  /**
   * Log rate limiting errors
   */
  private async logRateLimitError(
    request: NextRequest,
    endpoint: string,
    error: any
  ): Promise<void> {
    try {
      const auditor = new AuditLogger({
        ipAddress: this.getClientIP(request),
        userAgent: request.headers.get('user-agent') || undefined
      })

      await auditor.log({
        action: 'RATE_LIMIT_MIDDLEWARE_ERROR',
        resourceType: 'SYSTEM',
        resourceId: endpoint,
        details: {
          endpoint,
          error: error instanceof Error ? error.message : 'Unknown error',
          useDistributed: this.config.useDistributed
        },
        severity: 'medium'
      })
    } catch (logError) {
      console.error('Failed to log rate limit middleware error:', logError)
    }
  }
}

/**
 * Predefined rate limit configurations for common use cases
 */
export const rateLimitConfigs = {
  // Authentication endpoints - very strict
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    burstAllowance: 2,
    skipSuccessful: true,
    useDistributed: true
  },

  // Password reset - strict daily limits  
  passwordReset: {
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    maxRequests: 3,
    burstAllowance: 1,
    skipSuccessful: false,
    useDistributed: true
  },

  // API endpoints - moderate limits
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    burstAllowance: 20,
    skipSuccessful: false,
    useDistributed: true
  },

  // File upload - strict limits
  upload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10,
    burstAllowance: 3,
    skipSuccessful: false,
    useDistributed: true
  },

  // Public endpoints - lenient limits
  public: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 300,
    burstAllowance: 50,
    skipSuccessful: false,
    useDistributed: false // Use local for better performance
  }
}

/**
 * Factory functions for creating rate limiters
 */
export const createAuthRateLimit = () => 
  new RateLimitMiddleware(rateLimitConfigs.auth)

export const createAPIRateLimit = () => 
  new RateLimitMiddleware(rateLimitConfigs.api)

export const createUploadRateLimit = () => 
  new RateLimitMiddleware(rateLimitConfigs.upload)

export const createPublicRateLimit = () => 
  new RateLimitMiddleware(rateLimitConfigs.public)

export const createPasswordResetRateLimit = () => 
  new RateLimitMiddleware(rateLimitConfigs.passwordReset)

/**
 * Convenience decorator for API routes
 */
export function rateLimit(config: RateLimitMiddlewareConfig | keyof typeof rateLimitConfigs) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const rateLimitConfig = typeof config === 'string' ? rateLimitConfigs[config] : config
    const middleware = new RateLimitMiddleware(rateLimitConfig)

    descriptor.value = middleware.withRateLimit(originalMethod, propertyKey)
    return descriptor
  }
}

/**
 * Utility function to apply rate limiting to existing handlers
 */
export function withEnhancedRateLimit(
  handler: (request: NextRequest) => Promise<NextResponse>,
  config: RateLimitMiddlewareConfig | keyof typeof rateLimitConfigs
): (request: NextRequest) => Promise<NextResponse> {
  const rateLimitConfig = typeof config === 'string' ? rateLimitConfigs[config] : config
  const middleware = new RateLimitMiddleware(rateLimitConfig)
  
  return middleware.withRateLimit(handler)
}