/**
 * CRM Module Permissions
 * 
 * Defines CRM-specific permissions and access controls
 */

import { ModulePermissions } from "@/lib/permissions/rbac"

export const CRM_PERMISSIONS = {
  // Client management
  CLIENTS: {
    CREATE: "crm.clients.create",
    READ: "crm.clients.read", 
    UPDATE: "crm.clients.update",
    DELETE: "crm.clients.delete",
    EXPORT: "crm.clients.export"
  },

  // Lead management
  LEADS: {
    CREATE: "crm.leads.create",
    READ: "crm.leads.read",
    UPDATE: "crm.leads.update", 
    DELETE: "crm.leads.delete",
    ASSIGN: "crm.leads.assign",
    QUALIFY: "crm.leads.qualify"
  },

  // Opportunity management
  OPPORTUNITIES: {
    CREATE: "crm.opportunities.create",
    READ: "crm.opportunities.read",
    UPDATE: "crm.opportunities.update",
    DELETE: "crm.opportunities.delete",
    CLOSE: "crm.opportunities.close",
    FORECAST: "crm.opportunities.forecast"
  },

  // Contact management
  CONTACTS: {
    CREATE: "crm.contacts.create",
    READ: "crm.contacts.read",
    UPDATE: "crm.contacts.update",
    DELETE: "crm.contacts.delete",
    MERGE: "crm.contacts.merge"
  },

  // Pipeline management
  PIPELINE: {
    READ: "crm.pipeline.read",
    UPDATE: "crm.pipeline.update",
    MANAGE: "crm.pipeline.manage"
  },

  // Reporting
  REPORTS: {
    READ: "crm.reports.read",
    CREATE: "crm.reports.create",
    EXPORT: "crm.reports.export"
  }
} as const

/**
 * CRM Role-specific permission sets
 */
export const CRM_ROLE_PERMISSIONS = {
  SALES_REP: [
    // Own clients and leads
    CRM_PERMISSIONS.CLIENTS.READ,
    CRM_PERMISSIONS.LEADS.CREATE,
    CRM_PERMISSIONS.LEADS.READ,
    CRM_PERMISSIONS.LEADS.UPDATE,
    
    // Opportunities management
    CRM_PERMISSIONS.OPPORTUNITIES.CREATE,
    CRM_PERMISSIONS.OPPORTUNITIES.READ,
    CRM_PERMISSIONS.OPPORTUNITIES.UPDATE,
    CRM_PERMISSIONS.OPPORTUNITIES.CLOSE,
    
    // Contact management
    CRM_PERMISSIONS.CONTACTS.CREATE,
    CRM_PERMISSIONS.CONTACTS.READ,
    CRM_PERMISSIONS.CONTACTS.UPDATE,
    
    // Pipeline access
    CRM_PERMISSIONS.PIPELINE.READ,
    CRM_PERMISSIONS.PIPELINE.UPDATE
  ],

  SALES_MANAGER: [
    // All sales rep permissions plus
    ...CRM_ROLE_PERMISSIONS.SALES_REP,
    
    // Client management
    CRM_PERMISSIONS.CLIENTS.CREATE,
    CRM_PERMISSIONS.CLIENTS.UPDATE,
    CRM_PERMISSIONS.CLIENTS.DELETE,
    
    // Lead assignment
    CRM_PERMISSIONS.LEADS.ASSIGN,
    CRM_PERMISSIONS.LEADS.DELETE,
    
    // Advanced opportunity management
    CRM_PERMISSIONS.OPPORTUNITIES.DELETE,
    CRM_PERMISSIONS.OPPORTUNITIES.FORECAST,
    
    // Pipeline management
    CRM_PERMISSIONS.PIPELINE.MANAGE,
    
    // Reporting
    CRM_PERMISSIONS.REPORTS.READ,
    CRM_PERMISSIONS.REPORTS.CREATE,
    CRM_PERMISSIONS.REPORTS.EXPORT
  ]
} as const

/**
 * CRM-specific permission helpers
 */
export class CRMPermissions {
  /**
   * Check if user can access client
   */
  static canAccessClient(userRole: string, clientAssignedTo: string, userId: string): boolean {
    // Managers can access all clients
    if (["ORG_ADMIN", "SUPER_ADMIN", "SALES_MANAGER"].includes(userRole)) {
      return true
    }
    
    // Sales reps can only access assigned clients
    if (userRole === "SALES_REP") {
      return clientAssignedTo === userId
    }
    
    return false
  }

  /**
   * Check if user can modify opportunity
   */
  static canModifyOpportunity(userRole: string, opportunityOwnerId: string, userId: string): boolean {
    // Admins can modify all
    if (["ORG_ADMIN", "SUPER_ADMIN"].includes(userRole)) {
      return true
    }
    
    // Owner or sales manager can modify
    return opportunityOwnerId === userId || userRole === "SALES_MANAGER"
  }

  /**
   * Get accessible CRM modules for role
   */
  static getAccessibleModules(userRole: string): string[] {
    switch (userRole) {
      case "SUPER_ADMIN":
      case "ORG_ADMIN":
        return ["clients", "leads", "opportunities", "contacts", "pipeline", "reports"]
      
      case "SALES_MANAGER":
        return ["clients", "leads", "opportunities", "contacts", "pipeline", "reports"]
      
      case "SALES_REP":
        return ["leads", "opportunities", "contacts", "pipeline"]
      
      default:
        return []
    }
  }
}