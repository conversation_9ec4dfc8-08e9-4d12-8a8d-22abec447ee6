# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*.local
.env

# Allow environment templates and configuration
!.env.example
!.env.development
!.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# testing
/test-results/
/playwright-report/
/playwright/.cache/

# database
/prisma/migrations/migration_lock.toml

# IDE
.vscode/settings.json
.idea/
*.swp
*.swo

# Logs
logs
*.log

# Temporary folders
tmp/
temp/

# Coverage directory
coverage
