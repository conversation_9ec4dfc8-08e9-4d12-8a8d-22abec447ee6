import { PrismaClient, UserRole, JobStatus, LocationType, EmploymentType, ExperienceLevel, ApplicationStatus, ApplicationSource } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create Demo Organization
  const organization = await prisma.organization.upsert({
    where: { slug: 'demo-company' },
    update: {},
    create: {
      name: 'Demo Company',
      slug: 'demo-company',
      domain: 'demo.sourceflex.com',
      settings: {
        branding: {
          primaryColor: '#2563eb',
          logo: '/logo.png',
        },
        features: {
          jobBoard: true,
          crm: true,
          vms: true,
          ats: true,
          socialMedia: true,
        },
      },
    },
  });

  console.log('✅ Created organization:', organization.name);

  // Create Super Admin (Platform Administrator)
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Super Administrator',
      role: UserRole.SUPER_ADMIN,
      // Note: No password set - use the admin signup page to create with proper password
    },
  });

  // Create Demo Users
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      role: UserRole.ORG_ADMIN,
      organizationId: organization.id,
    },
  });

  const hiringManager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Sarah Johnson',
      role: UserRole.HIRING_MANAGER,
      organizationId: organization.id,
    },
  });

  const recruiter = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Mike Chen',
      role: UserRole.RECRUITER,
      organizationId: organization.id,
    },
  });

  const salesRep = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Lisa Rodriguez',
      role: UserRole.SALES_REP,
      organizationId: organization.id,
    },
  });

  console.log('✅ Created users:', { 
    superAdmin: superAdmin.name, 
    admin: adminUser.name, 
    hiring: hiringManager.name, 
    recruiter: recruiter.name, 
    sales: salesRep.name 
  });

  // Create Demo Jobs
  const seniorDeveloperJob = await prisma.job.create({
    data: {
      title: 'Senior Full Stack Developer',
      description: `We are looking for an experienced Full Stack Developer to join our growing engineering team. 

You will be responsible for designing, developing, and maintaining web applications using modern technologies. The ideal candidate has experience with React, Node.js, and PostgreSQL.

Key Responsibilities:
- Develop and maintain scalable web applications
- Collaborate with cross-functional teams
- Write clean, maintainable code
- Participate in code reviews and technical discussions
- Mentor junior developers

What We Offer:
- Competitive salary and equity
- Flexible work arrangements
- Health and dental insurance
- Professional development budget
- Modern tech stack and tools`,
      requirements: [
        '5+ years of experience in full-stack development',
        'Proficiency in React, TypeScript, and Node.js',
        'Experience with PostgreSQL or similar databases',
        'Knowledge of modern development practices (Git, CI/CD, testing)',
        'Strong problem-solving and communication skills',
        'Bachelor\'s degree in Computer Science or equivalent experience',
      ],
      benefits: [
        'Competitive salary: $120,000 - $160,000',
        'Equity package',
        'Health, dental, and vision insurance',
        'Flexible PTO policy',
        'Remote work options',
        '$2,000 professional development budget',
        'Latest MacBook Pro and equipment',
      ],
      locationType: LocationType.HYBRID,
      city: 'San Francisco',
      country: 'US',
      employmentType: EmploymentType.FULL_TIME,
      experience: ExperienceLevel.SENIOR_LEVEL,
      salaryMin: 120000,
      salaryMax: 160000,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEAR',
      status: JobStatus.ACTIVE,
      publishedAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      organizationId: organization.id,
    },
  });

  const productManagerJob = await prisma.job.create({
    data: {
      title: 'Senior Product Manager',
      description: `Join our product team as a Senior Product Manager and help shape the future of our platform.

You will work closely with engineering, design, and business teams to define product strategy, gather requirements, and deliver features that delight our users.

Key Responsibilities:
- Define product roadmap and strategy
- Work with engineering teams on feature development
- Conduct user research and analyze product metrics
- Collaborate with design team on user experience
- Communicate with stakeholders across the organization

What We Offer:
- Opportunity to make significant impact
- Work with cutting-edge technology
- Collaborative and innovative culture
- Comprehensive benefits package`,
      requirements: [
        '4+ years of product management experience',
        'Experience with B2B SaaS products',
        'Strong analytical and problem-solving skills',
        'Excellent communication and leadership abilities',
        'Experience with agile development methodologies',
        'Technical background preferred but not required',
      ],
      benefits: [
        'Competitive salary: $140,000 - $180,000',
        'Equity package',
        'Comprehensive health benefits',
        'Flexible work arrangements',
        'Learning and development opportunities',
      ],
      locationType: LocationType.REMOTE,
      country: 'US',
      employmentType: EmploymentType.FULL_TIME,
      experience: ExperienceLevel.SENIOR_LEVEL,
      salaryMin: 140000,
      salaryMax: 180000,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEAR',
      status: JobStatus.ACTIVE,
      publishedAt: new Date(),
      expiresAt: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
      organizationId: organization.id,
    },
  });

  const frontendDeveloperJob = await prisma.job.create({
    data: {
      title: 'Frontend Developer (React/TypeScript)',
      description: `We're seeking a talented Frontend Developer to help build and enhance our user-facing applications.

You'll work with our design and backend teams to create responsive, accessible, and performant web applications using React and TypeScript.

This is a great opportunity for a mid-level developer looking to grow their skills in a supportive environment.`,
      requirements: [
        '2-4 years of frontend development experience',
        'Strong knowledge of React and TypeScript',
        'Experience with modern CSS and styling frameworks',
        'Understanding of responsive design principles',
        'Knowledge of web accessibility standards',
        'Experience with Git and collaborative development',
      ],
      benefits: [
        'Competitive salary: $90,000 - $120,000',
        'Stock options',
        'Health and dental insurance',
        'Flexible work schedule',
        'Professional development support',
      ],
      locationType: LocationType.ONSITE,
      city: 'Austin',
      country: 'US',
      employmentType: EmploymentType.FULL_TIME,
      experience: ExperienceLevel.MID_LEVEL,
      salaryMin: 90000,
      salaryMax: 120000,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEAR',
      status: JobStatus.ACTIVE,
      publishedAt: new Date(),
      expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
      organizationId: organization.id,
    },
  });

  console.log('✅ Created jobs:', { 
    senior: seniorDeveloperJob.title, 
    pm: productManagerJob.title, 
    frontend: frontendDeveloperJob.title 
  });

  // Create Demo Applications
  const application1 = await prisma.application.create({
    data: {
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '******-0123',
      resumeUrl: 'https://example.com/resumes/john-smith.pdf',
      coverLetter: 'I am very interested in the Senior Full Stack Developer position. With over 6 years of experience in full-stack development, I believe I would be a great fit for your team.',
      status: ApplicationStatus.REVIEWING,
      stage: 'technical-review',
      jobId: seniorDeveloperJob.id,
      source: ApplicationSource.DIRECT,
    },
  });

  const application2 = await prisma.application.create({
    data: {
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '******-0124',
      resumeUrl: 'https://example.com/resumes/emily-davis.pdf',
      coverLetter: 'As a seasoned product manager with 5 years of experience in B2B SaaS, I am excited about the opportunity to join your product team.',
      status: ApplicationStatus.INTERVIEWING,
      stage: 'final-interview',
      jobId: productManagerJob.id,
      source: ApplicationSource.REFERRAL,
      referrer: 'LinkedIn',
    },
  });

  const application3 = await prisma.application.create({
    data: {
      firstName: 'Alex',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '******-0125',
      resumeUrl: 'https://example.com/resumes/alex-johnson.pdf',
      coverLetter: 'I am a passionate frontend developer with 3 years of experience in React and TypeScript. I would love to contribute to your team.',
      status: ApplicationStatus.PENDING,
      stage: 'initial-review',
      jobId: frontendDeveloperJob.id,
      source: ApplicationSource.JOB_BOARD,
    },
  });

  console.log('✅ Created applications:', { 
    count: 3,
    applicants: [application1.firstName + ' ' + application1.lastName, 
                application2.firstName + ' ' + application2.lastName,
                application3.firstName + ' ' + application3.lastName]
  });

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📊 Summary:');
  console.log(`- Organization: ${organization.name}`);
  console.log(`- Users: 5 (Super Admin, Org Admin, Hiring Manager, Recruiter, Sales Rep)`);
  console.log(`- Jobs: 3 (Senior Dev, Product Manager, Frontend Dev)`);
  console.log(`- Applications: 3`);
  console.log('\n🔐 To access admin dashboard:');
  console.log('1. Go to: http://localhost:3001/auth/admin/signup');
  console.log('2. Use admin key: super-secret-admin-key-2024');
  console.log('3. Create your SUPER_ADMIN account');
  console.log('4. Sign in and access: http://localhost:3001/admin/dashboard');
  console.log('\n🚀 You can now start the development server with: npm run dev');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Error seeding database:', e);
    await prisma.$disconnect();
    process.exit(1);
  });