// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Authentication and User Management
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  
  // Enhanced session tracking
  deviceId     String?
  deviceName   String?
  deviceType   DeviceType?
  browser      String?
  os           String?
  ipAddress    String?
  location     String? // City, Country
  
  // Session security
  isActive     Boolean  @default(true)
  lastActivity DateTime @default(now())
  
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isActive])
  @@index([lastActivity])
  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?   // For credentials authentication
  role          UserRole  @default(CANDIDATE)
  
  // Multi-tenant support
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  
  // Two-Factor Authentication
  twoFactorEnabled Boolean @default(false)
  twoFactorSecret  String? // TOTP secret
  twoFactorBackupCodes String[] // Emergency backup codes (renamed from backupCodes)
  
  // Password reset functionality
  resetToken       String?
  resetTokenExpiry DateTime?
  
  // User invitation functionality
  invitationToken  String?
  invitationExpiry DateTime?
  
  // Enhanced security fields
  maxConcurrentSessions Int @default(5)
  sessionTimeoutMinutes Int @default(480) // 8 hours default
  failedLoginAttempts   Int @default(0)
  accountLockedUntil    DateTime?
  lastLoginAt           DateTime?
  lastLoginIp           String?
  
  // SMS 2FA support
  phoneNumber           String?
  phoneVerified         Boolean @default(false)
  smsEnabled            Boolean @default(false)
  
  // WebAuthn support
  webAuthnCredentials   WebAuthnCredential[]
  
  // Authentication
  accounts Account[]
  sessions Session[]
  
  // Enhanced role system
  roleAssignments UserRoleAssignment[]
  
  // Audit logging
  auditLogs AuditLog[]
  
  // Account linking
  pendingAccountLinks PendingAccountLink[]
  
  // Activity tracking
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model VerificationToken {
  id         String   @id @default(cuid())
  identifier String
  token      String   @unique
  expires    DateTime
  
  // Enhanced security metadata
  metadata   Json?    // Store attempt count, IP addresses, timestamps
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([identifier, token])
  @@index([identifier, expires])
  @@index([expires]) // For cleanup queries
  @@map("verification_tokens")
}

// Admin Security Management
model AdminInvitation {
  id              String   @id @default(cuid())
  
  // Invitation details
  email           String   // Email of invited admin
  name            String   // Name of invited admin
  tokenHash       String   // Hashed invitation token
  
  // Invitation metadata
  invitedBy       String   // Who created the invitation
  organizationId  String?  // Optional organization context
  
  // Usage tracking
  used            Boolean  @default(false)
  usedAt          DateTime?
  createdUserId   String?  // ID of created user (when used)
  
  // Revocation support
  revokedBy       String?
  revokedAt       DateTime?
  
  // Security tracking
  ipAddress       String?  // IP address of invitation request
  userAgent       String?  // User agent of invitation request
  
  // Timestamps
  createdAt       DateTime @default(now())
  expiresAt       DateTime // Invitation expires in 24 hours
  
  @@index([email])
  @@index([tokenHash])
  @@index([expiresAt])
  @@map("admin_invitations")
}

// Account Linking for OAuth Security
model PendingAccountLink {
  id                String   @id @default(cuid())
  
  // OAuth account information
  oauthProvider     String   // "google", "github", etc.
  oauthAccountId    String   // Provider's account ID
  oauthEmail        String   // Email from OAuth provider
  oauthName         String?  // Name from OAuth provider
  oauthImage        String?  // Profile image from OAuth provider
  
  // Existing user account to link to
  existingUserId    String   // User ID to link the OAuth account to
  existingUser      User     @relation(fields: [existingUserId], references: [id], onDelete: Cascade)
  
  // Link verification
  verificationToken String   @unique // Secure token for verification
  verificationSent  Boolean  @default(false)
  isVerified        Boolean  @default(false)
  
  // Security tracking
  requestIpAddress  String?  // IP address of link request
  requestUserAgent  String?  // User agent of link request
  
  // Expiration and timestamps
  expiresAt         DateTime // Link expires in 15 minutes
  createdAt         DateTime @default(now())
  verifiedAt        DateTime?
  
  @@index([verificationToken])
  @@index([existingUserId])
  @@index([oauthProvider, oauthAccountId])
  @@map("pending_account_links")
}

// Core Business Models
model Organization {
  id       String @id @default(cuid())
  name     String
  slug     String @unique
  domain   String? // Custom domain for job board
  
  // Settings and configuration
  settings Json?
  
  // Relationships
  users User[]
  jobs  Job[]
  
  // Security
  securityPolicy SecurityPolicy?
  customRoles    Role[]
  
  // Audit logging
  auditLogs AuditLog[]
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("organizations")
}

model Job {
  id          String    @id @default(cuid())
  title       String    @db.VarChar(100)
  description String    @db.Text
  requirements String[]
  benefits    String[]
  
  // Location information
  locationType LocationType
  city         String?
  country      String       @default("US")
  
  // Job details
  employmentType EmploymentType @default(FULL_TIME)
  experience     ExperienceLevel @default(MID_LEVEL)
  
  // Salary information (optional)
  salaryMin    Decimal? @db.Decimal(10, 2)
  salaryMax    Decimal? @db.Decimal(10, 2)
  salaryCurrency String?  @db.VarChar(3)
  salaryPeriod SalaryPeriod?
  
  // Job status and lifecycle
  status       JobStatus @default(DRAFT)
  publishedAt  DateTime?
  expiresAt    DateTime?
  
  // Multi-tenant support
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  
  // Relationships
  applications Application[]
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@index([organizationId, status])
  @@index([status, publishedAt])
  @@index([locationType, city, country])
  @@map("jobs")
}

model Application {
  id          String            @id @default(cuid())
  
  // Candidate information
  firstName   String
  lastName    String
  email       String
  phone       String?
  
  // Application data
  resumeUrl   String?
  coverLetter String?           @db.Text
  
  // Application status
  status      ApplicationStatus @default(PENDING)
  stage       String?           // Current pipeline stage
  
  // Job relationship
  jobId       String
  job         Job               @relation(fields: [jobId], references: [id])
  
  // Source tracking
  source      ApplicationSource @default(DIRECT)
  referrer    String? // URL or identifier of referral source
  
  // Timestamps
  appliedAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Indexes
  @@index([jobId, status])
  @@index([email])
  @@map("applications")
}

// Enums
enum UserRole {
  SUPER_ADMIN
  ORG_ADMIN
  HIRING_MANAGER
  RECRUITER
  SALES_REP
  VENDOR
  CANDIDATE
  USER
}

enum JobStatus {
  DRAFT
  PUBLISHED
  ACTIVE
  PAUSED
  CLOSED
  EXPIRED
}

enum LocationType {
  REMOTE
  HYBRID
  ONSITE
}

enum EmploymentType {
  FULL_TIME
  PART_TIME
  CONTRACT
  TEMPORARY
  INTERNSHIP
}

enum ExperienceLevel {
  ENTRY_LEVEL
  MID_LEVEL
  SENIOR_LEVEL
  EXECUTIVE
}

enum SalaryPeriod {
  HOUR
  MONTH
  YEAR
}

enum ApplicationStatus {
  PENDING
  REVIEWING
  INTERVIEWING
  OFFERED
  HIRED
  REJECTED
  WITHDRAWN
}

enum ApplicationSource {
  DIRECT
  VENDOR
  REFERRAL
  SOCIAL_MEDIA
  JOB_BOARD
}

// Audit Logging
model AuditLog {
  id        String   @id @default(cuid())
  
  // Who performed the action
  userId    String?
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  userEmail String?  // Store email in case user is deleted
  userName  String?  // Store name in case user is deleted
  
  // Organization context
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: SetNull)
  
  // Action details
  action       AuditAction
  resourceType AuditResourceType
  resourceId   String?
  
  // What changed
  oldValues Json?
  newValues Json?
  
  // Request context
  ipAddress   String?
  userAgent   String?
  sessionId   String?
  
  // Additional metadata
  metadata    Json?
  success     Boolean @default(true)
  errorMessage String?
  
  // Timestamps
  createdAt   DateTime @default(now())
  
  // Indexes for performance
  @@index([userId, createdAt])
  @@index([organizationId, createdAt])
  @@index([action, createdAt])
  @@index([resourceType, resourceId])
  @@index([createdAt])
  @@map("audit_logs")
}

enum AuditAction {
  // Authentication
  USER_LOGIN
  USER_LOGOUT
  USER_LOGIN_FAILED
  PASSWORD_RESET_REQUESTED
  PASSWORD_RESET_COMPLETED
  TWO_FACTOR_ENABLED
  TWO_FACTOR_DISABLED
  
  // User Management
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  USER_ROLE_CHANGED
  USER_INVITED
  USER_INVITATION_ACCEPTED
  
  // Organization Management
  ORGANIZATION_CREATED
  ORGANIZATION_UPDATED
  ORGANIZATION_DELETED
  ORGANIZATION_SETTINGS_UPDATED
  
  // Job Management
  JOB_CREATED
  JOB_UPDATED
  JOB_DELETED
  JOB_PUBLISHED
  JOB_CLOSED
  JOB_BULK_UPDATED
  
  // Application Management
  APPLICATION_CREATED
  APPLICATION_UPDATED
  APPLICATION_STATUS_CHANGED
  APPLICATION_BULK_UPDATED
  
  // Admin Actions
  ADMIN_ACCESS_GRANTED
  ADMIN_ACCESS_REVOKED
  SYSTEM_SETTINGS_UPDATED
  DATA_EXPORT_REQUESTED
  DATA_IMPORT_COMPLETED
  
  // Security Events
  UNAUTHORIZED_ACCESS_ATTEMPTED
  SUSPICIOUS_ACTIVITY_DETECTED
  API_RATE_LIMIT_EXCEEDED
  
  // OAuth Security Events
  OAUTH_LOGIN_SUCCESS
  OAUTH_LOGIN_FAILED
  OAUTH_LINK_VERIFICATION_REQUIRED
  OAUTH_LINK_VERIFICATION_FAILED
  OAUTH_LINK_VERIFIED_SUCCESS
  OAUTH_NEW_USER_CREATED
  OAUTH_CLEANUP_EXPIRED_LINKS
  
  // Admin Security Events
  ADMIN_INVITATION_CREATED
  ADMIN_INVITATION_FAILED
  ADMIN_INVITATION_VERIFIED
  ADMIN_INVITATION_VERIFICATION_FAILED
  ADMIN_INVITATION_VERIFICATION_ERROR
  ADMIN_INVITATION_REVOKED
  ADMIN_INVITATION_ERROR
  ADMIN_ACCOUNT_CREATED
  ADMIN_ACCOUNT_CREATION_ERROR
  ADMIN_INVITATIONS_CLEANUP
  
  // CSRF Security Events
  CSRF_VIOLATION
  CSRF_MISSING_TOKEN
  CSRF_INVALID_TOKEN
  CSRF_TOKEN_GENERATED
  CSRF_TOKEN_VALIDATED
  
  // 2FA Security Events
  TWO_FACTOR_EMAIL_SENT
  TWO_FACTOR_EMAIL_VERIFIED
  TWO_FACTOR_EMAIL_FAILED
  TWO_FACTOR_TOTP_VERIFIED
  TWO_FACTOR_TOTP_FAILED
  TWO_FACTOR_SESSION_CREATED
  TWO_FACTOR_SESSION_CONSUMED
  TWO_FACTOR_SESSION_EXPIRED
  
  // Password Security Events
  PASSWORD_VALIDATION_SUCCESS
  PASSWORD_VALIDATION_FAILED
  PASSWORD_STRENGTH_WEAK
  PASSWORD_BREACH_DETECTED
  PASSWORD_POLICY_VIOLATION
  PASSWORD_HISTORY_VIOLATION
  PASSWORD_EXPIRED
  PASSWORD_STRENGTH_IMPROVED
  
  // Session Security Events
  SESSION_REGENERATED
  SESSION_REGENERATION_FAILED
  SESSION_REGENERATION_ERROR
  SESSION_INVALIDATED
  SESSION_INVALIDATION_BULK
  SESSION_HIJACKING_DETECTED
  SESSION_ROLE_CHANGE_REGENERATION
  SESSION_VALIDATION_FAILED
  SESSION_TIMEOUT
  SESSION_SECURITY_VIOLATION
  
  // Email Verification Security Events
  EMAIL_VERIFICATION_TOKEN_CREATED
  EMAIL_VERIFICATION_TOKEN_ERROR
  EMAIL_VERIFICATION_TOKEN_NOT_FOUND
  EMAIL_VERIFICATION_TOKEN_EXPIRED
  EMAIL_VERIFICATION_SUCCESS
  EMAIL_VERIFICATION_INVALID_TOKEN
  EMAIL_VERIFICATION_ATTEMPTS_EXCEEDED
  EMAIL_VERIFICATION_RATE_LIMITED
  EMAIL_VERIFICATION_TOO_FREQUENT
  EMAIL_VERIFICATION_ERROR
  
  // Enhanced Rate Limiting Events
  RATE_LIMIT_BURST_DETECTED
  RATE_LIMIT_SUSPICIOUS_PATTERN
  RATE_LIMIT_ADAPTIVE_APPLIED
  RATE_LIMIT_IP_BLOCKED
  RATE_LIMIT_IP_UNBLOCKED
  RATE_LIMIT_BEHAVIOR_ANALYZED
  DISTRIBUTED_RATE_LIMIT_EXCEEDED
  DISTRIBUTED_RATE_LIMIT_ERROR
  RATE_LIMIT_CLEANUP_COMPLETED
}

enum AuditResourceType {
  USER
  ORGANIZATION
  JOB
  APPLICATION
  SYSTEM
  AUTHENTICATION
}

// Enhanced Security Models
model WebAuthnCredential {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  credentialId      String   @unique // Base64 encoded credential ID
  publicKey         String   // Base64 encoded public key
  counter           Int      @default(0)
  deviceType        String   // "platform" or "cross-platform"
  name              String   // User-friendly name for the device
  
  createdAt         DateTime @default(now())
  lastUsedAt        DateTime?
  
  @@map("webauthn_credentials")
}

model SecurityPolicy {
  id               String       @id @default(cuid())
  organizationId   String
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Password policies
  minPasswordLength       Int     @default(8)
  requireUppercase        Boolean @default(true)
  requireLowercase        Boolean @default(true)
  requireNumbers          Boolean @default(true)
  requireSpecialChars     Boolean @default(true)
  passwordHistoryCount    Int     @default(5)
  passwordExpiryDays      Int?    // null = no expiry
  
  // 2FA policies
  require2FA              Boolean @default(false)
  require2FAForAdmins     Boolean @default(true)
  allowSMS2FA             Boolean @default(true)
  allowTOTP2FA            Boolean @default(true)
  allowWebAuthn           Boolean @default(true)
  
  // Session policies
  maxConcurrentSessions   Int     @default(5)
  sessionTimeoutMinutes   Int     @default(480)
  requireReauthForSensitive Boolean @default(true)
  
  // IP and access control
  allowedIpRanges         String[] // CIDR notation
  blockedIpRanges         String[] // CIDR notation
  
  // Brute force protection
  maxFailedAttempts       Int     @default(5)
  lockoutDurationMinutes  Int     @default(30)
  
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
  
  @@unique([organizationId])
  @@map("security_policies")
}

model Permission {
  id          String @id @default(cuid())
  name        String @unique // e.g., "jobs.create", "users.delete", "organization.settings"
  description String
  category    String // e.g., "jobs", "users", "organization"
  
  rolePermissions RolePermission[]
  
  @@map("permissions")
}

model Role {
  id             String @id @default(cuid())
  name           String
  description    String?
  isSystemRole   Boolean @default(false) // true for built-in roles
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  permissions    RolePermission[]
  userRoles      UserRoleAssignment[]
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@unique([name, organizationId])
  @@map("roles")
}

model RolePermission {
  id           String     @id @default(cuid())
  roleId       String
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permissionId String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserRoleAssignment {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  roleId String
  role   Role   @relation(fields: [roleId], references: [id], onDelete: Cascade)
  
  grantedAt DateTime @default(now())
  grantedBy String? // userId who granted this role
  
  @@unique([userId, roleId])
  @@map("user_role_assignments")
}

model LoginAttempt {
  id            String   @id @default(cuid())
  email         String
  ipAddress     String
  userAgent     String?
  success       Boolean
  failureReason String?
  location      String? // City, Country
  
  createdAt     DateTime @default(now())
  
  @@index([email, createdAt])
  @@index([ipAddress, createdAt])
  @@map("login_attempts")
}

model RateLimitEntry {
  id         String   @id @default(cuid())
  identifier String   // IP address, user ID, or custom identifier
  route      String   // API route or action
  count      Int      @default(1)
  windowStart DateTime
  
  // Enhanced fields for distributed rate limiting
  metadata   Json?    // Behavior data, risk scores, adaptive limits
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  @@unique([identifier, route, windowStart], name: "identifier_route_windowStart")
  @@index([windowStart])
  @@index([identifier, updatedAt])
  @@index([updatedAt]) // For cleanup operations
  @@map("rate_limit_entries")
}

// Updated enums
enum DeviceType {
  DESKTOP
  MOBILE
  TABLET
  UNKNOWN
}