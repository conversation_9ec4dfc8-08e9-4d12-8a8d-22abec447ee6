-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "AuditAction" AS ENUM ('USER_LOGIN', 'USER_LOGOUT', 'USER_LO<PERSON>N_FAILED', 'PASSWORD_RESET_REQUESTED', 'PASSWORD_RESET_COMPLETED', 'TWO_FACTOR_ENABLED', 'TWO_FACTOR_DISABLED', 'USER_CREATED', 'USER_UPDATED', 'USER_DELETED', 'USER_ROLE_CHANGED', 'USER_INVITED', 'USER_INVITATION_ACCEPTED', 'ORGANIZATION_CREATED', 'OR<PERSON><PERSON>ZATION_UPDATED', 'ORGANI<PERSON>ATION_DELETED', 'OR<PERSON><PERSON>ZATION_SETTINGS_UPDATED', 'JOB_CREATED', 'JOB_UPDATED', 'JOB_DELETED', 'JOB_PUBLISHED', 'JOB_CLOSED', 'JOB_BULK_UPDATED', 'APPLICATION_CREATED', 'APPLICATION_UPDATED', 'APPLICATION_STATUS_CHANGED', 'APPLICATION_BULK_UPDATED', 'ADMIN_ACCESS_GRANTED', 'ADMIN_ACCESS_REVOKED', 'SYSTEM_SETTINGS_UPDATED', 'DATA_EXPORT_REQUESTED', 'DATA_IMPORT_COMPLETED', 'UNAUTHORIZED_ACCESS_ATTEMPTED', 'SUSPICIOUS_ACTIVITY_DETECTED', 'API_RATE_LIMIT_EXCEEDED');

-- CreateEnum
CREATE TYPE "AuditResourceType" AS ENUM ('USER', 'ORGANIZATION', 'JOB', 'APPLICATION', 'SYSTEM', 'AUTHENTICATION');

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "userEmail" TEXT,
    "userName" TEXT,
    "organizationId" TEXT,
    "action" "AuditAction" NOT NULL,
    "resourceType" "AuditResourceType" NOT NULL,
    "resourceId" TEXT,
    "oldValues" JSONB,
    "newValues" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "sessionId" TEXT,
    "metadata" JSONB,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "audit_logs_userId_createdAt_idx" ON "audit_logs"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "audit_logs_organizationId_createdAt_idx" ON "audit_logs"("organizationId", "createdAt");

-- CreateIndex
CREATE INDEX "audit_logs_action_createdAt_idx" ON "audit_logs"("action", "createdAt");

-- CreateIndex
CREATE INDEX "audit_logs_resourceType_resourceId_idx" ON "audit_logs"("resourceType", "resourceId");

-- CreateIndex
CREATE INDEX "audit_logs_createdAt_idx" ON "audit_logs"("createdAt");

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
