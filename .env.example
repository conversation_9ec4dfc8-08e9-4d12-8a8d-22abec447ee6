# =============================================================================
# Sourceflex Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
# PostgreSQL database connection URL
DATABASE_URL="postgresql://developer:dev_password@localhost:5432/sourceflex"
DIRECT_URL="postgresql://developer:dev_password@localhost:5432/sourceflex"

# -----------------------------------------------------------------------------
# NextAuth.js Configuration
# -----------------------------------------------------------------------------
# Secret for JWT signing (minimum 32 characters)
# Generate with: openssl rand -base64 32
NEXTAUTH_SECRET="your-nextauth-secret-key-minimum-32-characters-required"

# Application URL for NextAuth callbacks
NEXTAUTH_URL="http://localhost:3000"

# -----------------------------------------------------------------------------
# Session Security
# -----------------------------------------------------------------------------
# HMAC secret for session security (minimum 32 characters)
# Generate with: node -p "crypto.randomBytes(32).toString('hex')"
SESSION_HMAC_SECRET="your-session-hmac-secret-key-minimum-32-characters-required"

# -----------------------------------------------------------------------------
# Application Settings
# -----------------------------------------------------------------------------
NODE_ENV="development"
APP_URL="http://localhost:3000"

# -----------------------------------------------------------------------------
# OAuth Providers (Optional)
# -----------------------------------------------------------------------------
# Google OAuth
# GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
# GOOGLE_CLIENT_SECRET="your-google-client-secret"

# LinkedIn OAuth
# LINKEDIN_CLIENT_ID="your-linkedin-client-id"
# LINKEDIN_CLIENT_SECRET="your-linkedin-client-secret"

# -----------------------------------------------------------------------------
# Email Service Configuration
# -----------------------------------------------------------------------------
# Resend API key for email sending
# RESEND_API_KEY="re_your-resend-api-key"

# Default from email address
FROM_EMAIL="<EMAIL>"

# -----------------------------------------------------------------------------
# Admin Security
# -----------------------------------------------------------------------------
# Key required for creating admin accounts
# ADMIN_CREATION_KEY="your-secure-admin-creation-key"

# -----------------------------------------------------------------------------
# Quick Setup Instructions
# -----------------------------------------------------------------------------
# 1. Copy this file to .env.local: cp .env.example .env.local
# 2. Replace all placeholder values with actual configuration
# 3. Generate secure secrets:
#    - NEXTAUTH_SECRET: openssl rand -base64 32
#    - SESSION_HMAC_SECRET: node -p "crypto.randomBytes(32).toString('hex')"
# 4. Set up PostgreSQL database and update DATABASE_URL
# 5. Configure OAuth providers if needed
# 6. Set up email service (Resend) if needed
# 7. Run: npm run db:migrate to set up database schema
# 8. Run: npm run dev to start development server