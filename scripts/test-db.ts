import { db } from '../src/lib/db';

async function testConnection() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test basic connection
    await db.$connect();
    console.log('✅ Database connection successful');
    
    // Test query
    const organizationCount = await db.organization.count();
    const userCount = await db.user.count();
    const jobCount = await db.job.count();
    const applicationCount = await db.application.count();
    
    console.log('📊 Database status:');
    console.log(`- Organizations: ${organizationCount}`);
    console.log(`- Users: ${userCount}`);
    console.log(`- Jobs: ${jobCount}`);
    console.log(`- Applications: ${applicationCount}`);
    
    // Test a more complex query
    const activeJobs = await db.job.findMany({
      where: { status: 'ACTIVE' },
      include: {
        organization: true,
        _count: {
          select: { applications: true }
        }
      }
    });
    
    console.log('\n🎯 Active Jobs:');
    activeJobs.forEach(job => {
      console.log(`- ${job.title} (${job.organization.name}) - ${job._count.applications} applications`);
    });
    
    console.log('\n🎉 All database tests passed!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

testConnection();