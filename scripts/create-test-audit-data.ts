import { PrismaClient, AuditAction, AuditResourceType } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestAuditData() {
  console.log('🔄 Creating test audit data...');

  // Get some users to create audit logs for
  const users = await prisma.user.findMany({
    take: 3,
    include: { organization: true }
  });

  if (users.length === 0) {
    console.log('❌ No users found. Please run npm run dev:seed first.');
    return;
  }

  const testEvents = [
    {
      action: AuditAction.USER_LOGIN,
      resourceType: AuditResourceType.AUTHENTICATION,
      success: true,
    },
    {
      action: AuditAction.USER_CREATED,
      resourceType: AuditResourceType.USER,
      success: true,
    },
    {
      action: AuditAction.JOB_CREATED,
      resourceType: AuditResourceType.JOB,
      success: true,
    },
    {
      action: AuditAction.USER_LOGIN_FAILED,
      resourceType: AuditResourceType.AUTHENTICATION,
      success: false,
    },
    {
      action: AuditAction.JOB_UPDATED,
      resourceType: AuditResourceType.JOB,
      success: true,
    },
    {
      action: AuditAction.ORGANIZATION_SETTINGS_UPDATED,
      resourceType: AuditResourceType.ORGANIZATION,
      success: true,
    },
    {
      action: AuditAction.APPLICATION_CREATED,
      resourceType: AuditResourceType.APPLICATION,
      success: true,
    },
    {
      action: AuditAction.USER_LOGOUT,
      resourceType: AuditResourceType.AUTHENTICATION,
      success: true,
    },
  ];

  // Create audit logs for different times
  const now = new Date();
  const hoursAgo = (hours: number) => new Date(now.getTime() - hours * 60 * 60 * 1000);

  for (let i = 0; i < 20; i++) {
    const user = users[i % users.length];
    const event = testEvents[i % testEvents.length];
    
    // Create audit log directly in database
    await prisma.auditLog.create({
      data: {
        action: event.action,
        resourceType: event.resourceType,
        resourceId: `test-resource-${i + 1}`,
        userId: user.id,
        userEmail: user.email,
        userName: user.name,
        organizationId: user.organizationId,
        success: event.success,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: "Mozilla/5.0 (Test Browser) AppleWebKit/537.36",
        metadata: {
          testData: true,
          eventNumber: i + 1,
        },
        createdAt: hoursAgo(Math.floor(Math.random() * 72)), // Random time within last 3 days
      },
    });
  }

  console.log('✅ Created 20 test audit log entries');
  
  // Show summary
  const totalLogs = await prisma.auditLog.count();
  const recentLogs = await prisma.auditLog.count({
    where: {
      createdAt: {
        gte: hoursAgo(24), // Last 24 hours
      }
    }
  });

  console.log(`📊 Total audit logs: ${totalLogs}`);
  console.log(`📈 Logs in last 24h: ${recentLogs}`);
  console.log('🎉 Test audit data creation completed!');
}

createTestAuditData()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ Error creating test audit data:', e);
    await prisma.$disconnect();
    process.exit(1);
  });