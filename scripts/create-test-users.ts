import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Creating test users...')

  // Create test candidate
  const candidatePassword = await hash('TestPassword123!', 12)
  const candidate = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test Candidate',
      password: candidatePassword,
      role: 'CANDIDATE',
      emailVerified: new Date(),
    }
  })
  console.log('✅ Created candidate:', candidate.email)

  // Create test business user with organization
  const businessPassword = await hash('TestPassword123!', 12)
  
  // First create an organization
  const org = await prisma.organization.upsert({
    where: { slug: 'test-company' },
    update: {},
    create: {
      name: 'Test Company',
      slug: 'test-company',
      domain: 'testcompany.com',
    }
  })
  console.log('✅ Created organization:', org.name)

  const businessUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test Business User',
      password: businessPassword,
      role: 'ORG_ADMIN',
      organizationId: org.id,
      emailVerified: new Date(),
    }
  })
  console.log('✅ Created business user:', businessUser.email)

  // Create test admin
  const adminPassword = await hash('AdminTestPassword123!', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test Admin',
      password: adminPassword,
      role: 'SUPER_ADMIN',
      emailVerified: new Date(),
      twoFactorEnabled: false, // For testing, we'll disable 2FA initially
    }
  })
  console.log('✅ Created admin:', admin.email)

  console.log('\n📋 Test credentials:')
  console.log('Candidate: <EMAIL> / TestPassword123!')
  console.log('Business: <EMAIL> / TestPassword123!')
  console.log('Admin: <EMAIL> / AdminTestPassword123!')
}

main()
  .catch((e) => {
    console.error('Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })