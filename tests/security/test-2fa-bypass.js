/**
 * 2FA Bypass Prevention Test
 * 
 * This script tests that the 2FA bypass vulnerability has been fixed
 */

const BASE_URL = 'http://localhost:3000';

async function test2FABypassPrevention() {
  console.log('🔐 Testing 2FA Bypass Prevention\n');

  // Test 1: Try to authenticate with hardcoded "EMAIL_VERIFIED" token
  console.log('Test 1: Attempting 2FA bypass with hardcoded token');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'correct_password',
        userType: 'admin',
        twoFactorCode: 'EMAIL_VERIFIED' // This should NOT work anymore
      })
    });

    console.log(`Response Status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('✅ Hardcoded bypass blocked - 2FA bypass vulnerability fixed\n');
    } else {
      console.log('❌ Hardcoded bypass still works - vulnerability NOT fixed\n');
    }
  } catch (error) {
    console.error('Error in test 1:', error);
  }

  // Test 2: Try to authenticate without any 2FA session
  console.log('Test 2: Attempting auth without valid 2FA session');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'correct_password',
        userType: 'admin',
        email2FASessionId: 'fake_session_id',
        email2FASessionToken: 'fake_session_token'
      })
    });

    console.log(`Response Status: ${response.status}`);
    
    if (response.status === 401) {
      console.log('✅ Fake session blocked - proper session validation working\n');
    } else {
      console.log('❌ Fake session accepted - session validation vulnerability\n');
    }
  } catch (error) {
    console.error('Error in test 2:', error);
  }

  // Test 3: Try to access 2FA verification endpoint without rate limiting
  console.log('Test 3: Testing 2FA rate limiting');
  let rateLimitHit = false;
  
  try {
    for (let i = 0; i < 6; i++) {
      const response = await fetch(`${BASE_URL}/api/auth/2fa/email/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'test_user_id',
          code: '123456'
        })
      });

      console.log(`Attempt ${i + 1}: Status ${response.status}`);
      
      if (response.status === 429) {
        rateLimitHit = true;
        break;
      }
    }

    if (rateLimitHit) {
      console.log('✅ Rate limiting working - excessive attempts blocked\n');
    } else {
      console.log('❌ Rate limiting not working - potential brute force vulnerability\n');
    }
  } catch (error) {
    console.error('Error in test 3:', error);
  }

  // Test 4: Verify CSRF protection is active on 2FA endpoints
  console.log('Test 4: Testing CSRF protection on 2FA endpoints');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/2fa/email/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // No CSRF token
      },
      body: JSON.stringify({
        userId: 'test_user_id',
        deviceInfo: 'Test device'
      })
    });

    console.log(`Response Status: ${response.status}`);
    
    if (response.status === 403) {
      console.log('✅ CSRF protection active - requests without token blocked\n');
    } else {
      console.log('❌ CSRF protection not working on 2FA endpoints\n');
    }
  } catch (error) {
    console.error('Error in test 4:', error);
  }

  console.log('🔐 2FA Bypass Testing Complete!');
  
  console.log('\n📋 Summary:');
  console.log('- The original bypass used hardcoded "EMAIL_VERIFIED" token');
  console.log('- New implementation requires cryptographic session tokens');
  console.log('- Sessions are one-time use and expire after 15 minutes');
  console.log('- Rate limiting prevents brute force attacks');
  console.log('- CSRF protection prevents cross-site attacks');
  console.log('- All 2FA events are comprehensively audited');
}

// Run the tests
test2FABypassPrevention();