/**
 * CSRF Protection Test Script
 * 
 * This script tests that CSRF protection is working correctly on our auth endpoints
 */

const BASE_URL = 'http://localhost:3000';

async function testCSRFProtection() {
  console.log('🔒 Testing CSRF Protection Implementation\n');

  // Test 1: Try to make request without CSRF token
  console.log('Test 1: POST request without CSRF token');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/signup/candidate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'TestPassword123!'
      })
    });

    console.log(`Response Status: ${response.status}`);
    const data = await response.json();
    console.log('Response:', data);
    
    if (response.status === 403) {
      console.log('✅ CSRF protection working - request blocked without token\n');
    } else {
      console.log('❌ CSRF protection may not be working correctly\n');
    }
  } catch (error) {
    console.error('Error:', error);
  }

  // Test 2: Try to make request from different origin
  console.log('Test 2: POST request from different origin');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://malicious-site.com',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      })
    });

    console.log(`Response Status: ${response.status}`);
    const data = await response.json();
    console.log('Response:', data);
    
    if (response.status === 403) {
      console.log('✅ Origin validation working - cross-origin request blocked\n');
    } else {
      console.log('❌ Origin validation may not be working correctly\n');
    }
  } catch (error) {
    console.error('Error:', error);
  }

  // Test 3: Check that GET requests don't require CSRF
  console.log('Test 3: GET request (should not require CSRF)');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/check-email?email=<EMAIL>`);
    
    console.log(`Response Status: ${response.status}`);
    
    if (response.status !== 403) {
      console.log('✅ GET requests allowed without CSRF token\n');
    } else {
      console.log('❌ GET requests incorrectly blocked\n');
    }
  } catch (error) {
    console.error('Error:', error);
  }

  console.log('🔒 CSRF Protection Testing Complete!');
}

// Run the tests
testCSRFProtection();