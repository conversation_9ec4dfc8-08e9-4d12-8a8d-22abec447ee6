const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { SessionSecurityManager } = require('../../src/lib/session-security')
const { db } = require('../../src/lib/db')
const { AuditLogger } = require('../../src/lib/audit')

// Mock dependencies
jest.mock('../../src/lib/db')
jest.mock('../../src/lib/audit')

describe('SessionSecurityManager', () => {
  let mockSession, mockUser, mockAuditLogger

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Mock user
    mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'CANDIDATE',
      organizationId: 'org-123',
      sessionTimeoutMinutes: 480,
      maxConcurrentSessions: 5
    }

    // Mock session
    mockSession = {
      id: 'session-123',
      sessionToken: 'valid-token',
      userId: 'user-123',
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      isActive: true,
      lastActivity: new Date(),
      ipAddress: '***********',
      browser: 'Chrome 120.0',
      user: mockUser
    }

    // Mock audit logger
    mockAuditLogger = {
      log: jest.fn().mockResolvedValue(true)
    }
    AuditLogger.mockImplementation(() => mockAuditLogger)

    // Set environment variable for tests
    process.env.SESSION_HMAC_SECRET = 'test-secret-key'
  })

  afterEach(() => {
    delete process.env.SESSION_HMAC_SECRET
  })

  describe('generateSecureSessionToken', () => {
    it('should generate a valid session token', () => {
      const token = SessionSecurityManager.generateSecureSessionToken()
      
      expect(token).toBeTruthy()
      expect(typeof token).toBe('string')
      expect(token.split('.')).toHaveLength(3)
    })

    it('should generate unique tokens each time', () => {
      const token1 = SessionSecurityManager.generateSecureSessionToken()
      const token2 = SessionSecurityManager.generateSecureSessionToken()
      
      expect(token1).not.toBe(token2)
    })
  })

  describe('validateSessionToken', () => {
    it('should validate a properly formatted token', () => {
      const token = SessionSecurityManager.generateSecureSessionToken()
      const isValid = SessionSecurityManager.validateSessionToken(token)
      
      expect(isValid).toBe(true)
    })

    it('should reject malformed tokens', () => {
      const malformedTokens = [
        'invalid',
        'invalid.token',
        'invalid.token.signature.extra',
        '',
        null,
        undefined
      ]

      malformedTokens.forEach(token => {
        expect(SessionSecurityManager.validateSessionToken(token)).toBe(false)
      })
    })

    it('should reject tokens with invalid signatures', () => {
      const validToken = SessionSecurityManager.generateSecureSessionToken()
      const parts = validToken.split('.')
      const tamperedToken = `${parts[0]}.${parts[1]}.invalid-signature`
      
      expect(SessionSecurityManager.validateSessionToken(tamperedToken)).toBe(false)
    })
  })

  describe('regenerateSession', () => {
    beforeEach(() => {
      db.session.findUnique.mockResolvedValue(mockSession)
      db.session.update.mockResolvedValue({ ...mockSession, sessionToken: 'new-token' })
      db.session.updateMany.mockResolvedValue({ count: 2 })
    })

    it('should successfully regenerate session for valid session', async () => {
      const result = await SessionSecurityManager.regenerateSession(
        'valid-token',
        { reason: 'login' },
        { userId: 'user-123', ipAddress: '***********' }
      )

      expect(result.success).toBe(true)
      expect(result.newSessionToken).toBeTruthy()
      expect(db.session.update).toHaveBeenCalled()
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'SESSION_REGENERATED',
        resourceType: 'AUTHENTICATION',
        resourceId: expect.any(String),
        details: expect.objectContaining({
          reason: 'login'
        }),
        severity: 'low'
      })
    })

    it('should invalidate other sessions when requested', async () => {
      await SessionSecurityManager.regenerateSession(
        'valid-token',
        { reason: 'login', invalidateOtherSessions: true },
        { userId: 'user-123' }
      )

      expect(db.session.updateMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-123',
          sessionToken: { not: 'valid-token' },
          isActive: true
        },
        data: { isActive: false }
      })
    })

    it('should handle session not found error', async () => {
      db.session.findUnique.mockResolvedValue(null)

      const result = await SessionSecurityManager.regenerateSession(
        'invalid-token',
        { reason: 'login' },
        { userId: 'user-123' }
      )

      expect(result.success).toBe(false)
      expect(result.error).toBe('Session not found or inactive')
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'SESSION_REGENERATION_FAILED',
        resourceType: 'AUTHENTICATION',
        resourceId: 'invalid-token',
        details: expect.objectContaining({
          reason: 'session_not_found'
        }),
        severity: 'medium'
      })
    })
  })

  describe('validateSession', () => {
    beforeEach(() => {
      jest.spyOn(SessionSecurityManager, 'validateSessionToken').mockReturnValue(true)
      db.session.findUnique.mockResolvedValue(mockSession)
      db.session.update.mockResolvedValue(mockSession)
    })

    it('should validate active session within timeout', async () => {
      const result = await SessionSecurityManager.validateSession(
        'valid-token',
        { ipAddress: '***********', userAgent: 'Chrome/120.0' }
      )

      expect(result.valid).toBe(true)
      expect(result.session).toBeTruthy()
      expect(db.session.update).toHaveBeenCalled()
    })

    it('should reject expired sessions', async () => {
      const expiredSession = {
        ...mockSession,
        expires: new Date(Date.now() - 60000) // Expired 1 minute ago
      }
      db.session.findUnique.mockResolvedValue(expiredSession)

      const result = await SessionSecurityManager.validateSession('expired-token', {})

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Session expired')
    })

    it('should reject timed out sessions', async () => {
      const timedOutSession = {
        ...mockSession,
        lastActivity: new Date(Date.now() - 9 * 60 * 60 * 1000), // 9 hours ago
        user: { ...mockUser, sessionTimeoutMinutes: 480 } // 8 hours timeout
      }
      db.session.findUnique.mockResolvedValue(timedOutSession)

      const result = await SessionSecurityManager.validateSession('timed-out-token', {})

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Session timed out')
    })

    it('should detect session hijacking on IP change', async () => {
      // Mock recent sessions with multiple IPs
      const recentSessions = [
        { ipAddress: '***********' },
        { ipAddress: '********' },
        { ipAddress: '**********' },
        { ipAddress: '***********' }
      ]
      db.session.findMany.mockResolvedValue(recentSessions)
      
      const result = await SessionSecurityManager.validateSession(
        'hijacked-token',
        { ipAddress: '************' } // Different IP
      )

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Session security violation detected')
      expect(result.requiresRegeneration).toBe(true)
    })

    it('should reject invalid session tokens', async () => {
      jest.spyOn(SessionSecurityManager, 'validateSessionToken').mockReturnValue(false)

      const result = await SessionSecurityManager.validateSession('invalid-format', {})

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Invalid session token format')
    })
  })

  describe('invalidateSession', () => {
    beforeEach(() => {
      db.session.findUnique.mockResolvedValue(mockSession)
      db.session.update.mockResolvedValue({ ...mockSession, isActive: false })
    })

    it('should successfully invalidate session', async () => {
      const result = await SessionSecurityManager.invalidateSession(
        'valid-token',
        'logout',
        { ipAddress: '***********' }
      )

      expect(result).toBe(true)
      expect(db.session.update).toHaveBeenCalledWith({
        where: { sessionToken: 'valid-token' },
        data: { isActive: false }
      })
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'SESSION_INVALIDATED',
        resourceType: 'AUTHENTICATION',
        resourceId: 'session-123',
        details: { reason: 'logout', sessionId: 'session-123' },
        severity: 'low'
      })
    })

    it('should handle session not found', async () => {
      db.session.findUnique.mockResolvedValue(null)

      const result = await SessionSecurityManager.invalidateSession('nonexistent-token', 'logout')

      expect(result).toBe(false)
    })

    it('should use high severity for security violations', async () => {
      await SessionSecurityManager.invalidateSession(
        'valid-token',
        'security_violation',
        { ipAddress: '***********' }
      )

      expect(mockAuditLogger.log).toHaveBeenCalledWith(
        expect.objectContaining({
          severity: 'high'
        })
      )
    })
  })

  describe('handleRoleChange', () => {
    beforeEach(() => {
      const sessions = [
        { ...mockSession, sessionToken: 'token1' },
        { ...mockSession, sessionToken: 'token2' }
      ]
      db.session.findMany.mockResolvedValue(sessions)
      
      jest.spyOn(SessionSecurityManager, 'regenerateSession')
        .mockResolvedValue({ success: true, newSessionToken: 'new-token' })
    })

    it('should regenerate all user sessions on role change', async () => {
      const result = await SessionSecurityManager.handleRoleChange(
        'user-123',
        'HIRING_MANAGER',
        'admin-456',
        { ipAddress: '***********' }
      )

      expect(result.success).toBe(true)
      expect(result.affectedSessions).toBe(2)
      expect(SessionSecurityManager.regenerateSession).toHaveBeenCalledTimes(2)
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'SESSION_ROLE_CHANGE_REGENERATION',
        resourceType: 'AUTHENTICATION',
        resourceId: 'user-123',
        details: {
          newRole: 'HIRING_MANAGER',
          changedBy: 'admin-456',
          sessionsRegenerated: 2
        },
        severity: 'medium'
      })
    })

    it('should handle regeneration failures gracefully', async () => {
      jest.spyOn(SessionSecurityManager, 'regenerateSession')
        .mockResolvedValueOnce({ success: true, newSessionToken: 'new-token1' })
        .mockResolvedValueOnce({ success: false, error: 'Failed' })

      const result = await SessionSecurityManager.handleRoleChange(
        'user-123',
        'HIRING_MANAGER',
        'admin-456',
        {}
      )

      expect(result.success).toBe(true)
      expect(result.affectedSessions).toBe(1) // Only one successful regeneration
    })
  })

  describe('cleanupSessions', () => {
    it('should cleanup expired and inactive sessions', async () => {
      db.session.deleteMany.mockResolvedValue({ count: 5 })
      db.user.findMany.mockResolvedValue([
        { id: 'user-1', sessionTimeoutMinutes: 480 },
        { id: 'user-2', sessionTimeoutMinutes: 120 }
      ])
      db.session.updateMany.mockResolvedValue({ count: 3 })

      const result = await SessionSecurityManager.cleanupSessions()

      expect(result.cleaned).toBe(8) // 5 deleted + 3 deactivated
      expect(result.errors).toBe(0)
      expect(db.session.deleteMany).toHaveBeenCalled()
      expect(db.session.updateMany).toHaveBeenCalledTimes(2) // Once per user
    })

    it('should handle cleanup errors gracefully', async () => {
      db.session.deleteMany.mockRejectedValue(new Error('Database error'))

      const result = await SessionSecurityManager.cleanupSessions()

      expect(result.cleaned).toBe(0)
      expect(result.errors).toBe(1)
    })
  })

  describe('Security Edge Cases', () => {
    it('should handle concurrent session token generation', () => {
      const tokens = Array.from({ length: 100 }, () => 
        SessionSecurityManager.generateSecureSessionToken()
      )
      
      const uniqueTokens = new Set(tokens)
      expect(uniqueTokens.size).toBe(100) // All tokens should be unique
    })

    it('should validate HMAC timing safety', () => {
      const validToken = SessionSecurityManager.generateSecureSessionToken()
      const parts = validToken.split('.')
      
      // Create tokens with slightly different signatures
      const similarToken1 = `${parts[0]}.${parts[1]}.${parts[2].slice(0, -1)}a`
      const similarToken2 = `${parts[0]}.${parts[1]}.${parts[2].slice(0, -1)}b`
      
      expect(SessionSecurityManager.validateSessionToken(similarToken1)).toBe(false)
      expect(SessionSecurityManager.validateSessionToken(similarToken2)).toBe(false)
    })

    it('should handle very long user agents without breaking', async () => {
      const longUserAgent = 'Mozilla/5.0'.repeat(1000)
      
      const result = await SessionSecurityManager.validateSession(
        'valid-token',
        { userAgent: longUserAgent }
      )
      
      // Should not throw errors and handle gracefully
      expect(typeof result.valid).toBe('boolean')
    })
  })

  describe('Integration with SessionManager', () => {
    it('should work with existing SessionManager patterns', () => {
      // Test that our new security manager can work alongside existing session manager
      const token = SessionSecurityManager.generateSecureSessionToken()
      expect(token).toBeTruthy()
      
      // Verify token format is compatible
      expect(token.split('.')).toHaveLength(3)
      expect(SessionSecurityManager.validateSessionToken(token)).toBe(true)
    })
  })
})