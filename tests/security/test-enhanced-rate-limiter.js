const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { EnhancedRateLimiter } = require('../../src/lib/enhanced-rate-limiter')
const { DistributedRateLimiter } = require('../../src/lib/distributed-rate-limiter')
const { db } = require('../../src/lib/db')
const { AuditLogger } = require('../../src/lib/audit')

// Mock dependencies
jest.mock('../../src/lib/db')
jest.mock('../../src/lib/audit')

describe('EnhancedRateLimiter', () => {
  let rateLimiter, mockRequest, mockAuditLogger

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Clean up static state
    EnhancedRateLimiter.cleanup()

    // Create rate limiter with test config
    rateLimiter = new EnhancedRateLimiter({
      windowMs: 60000, // 1 minute
      maxRequests: 10,
      burstAllowance: 3,
      adaptiveFactor: 1.0,
      trustedUserMultiplier: 2.0,
      suspiciousUserDivisor: 2.0
    })

    // Mock request object
    mockRequest = {
      headers: {
        get: jest.fn((header) => {
          switch (header) {
            case 'x-forwarded-for': return '***********'
            case 'user-agent': return 'Mozilla/5.0 Test Browser'
            default: return null
          }
        })
      }
    }

    // Mock audit logger
    mockAuditLogger = {
      log: jest.fn().mockResolvedValue(true)
    }
    AuditLogger.mockImplementation(() => mockAuditLogger)
  })

  afterEach(() => {
    // Clean up after each test
    EnhancedRateLimiter.cleanup()
  })

  describe('Basic Rate Limiting', () => {
    it('should allow requests within limit', async () => {
      const result = await rateLimiter.checkLimit(mockRequest, '/api/test')

      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(9) // 10 - 1
      expect(result.limit).toBe(10)
      expect(result.resetTime).toBeInstanceOf(Date)
    })

    it('should block requests over limit', async () => {
      // Make 10 requests (the limit)
      for (let i = 0; i < 10; i++) {
        await rateLimiter.checkLimit(mockRequest, '/api/test')
      }

      // 11th request should be blocked
      const result = await rateLimiter.checkLimit(mockRequest, '/api/test')

      expect(result.allowed).toBe(false)
      expect(result.remaining).toBe(0)
      expect(result.retryAfter).toBeGreaterThan(0)
    })

    it('should reset limits after window expires', async () => {
      // Create rate limiter with very short window for testing
      const shortRateLimiter = new EnhancedRateLimiter({
        windowMs: 100, // 100ms
        maxRequests: 2
      })

      // Exhaust limit
      await shortRateLimiter.checkLimit(mockRequest, '/api/test')
      await shortRateLimiter.checkLimit(mockRequest, '/api/test')

      // Should be blocked
      const blockedResult = await shortRateLimiter.checkLimit(mockRequest, '/api/test')
      expect(blockedResult.allowed).toBe(false)

      // Wait for window to reset
      await new Promise(resolve => setTimeout(resolve, 150))

      // Should be allowed again
      const allowedResult = await shortRateLimiter.checkLimit(mockRequest, '/api/test')
      expect(allowedResult.allowed).toBe(true)
    })
  })

  describe('Adaptive Rate Limiting', () => {
    it('should increase limits for trusted users', async () => {
      const context = { userId: 'trusted-user', success: true }

      // Simulate trusted user behavior (high success rate, reasonable intervals)
      for (let i = 0; i < 5; i++) {
        await rateLimiter.checkLimit(mockRequest, '/api/test', context)
        // Add delay to simulate reasonable request intervals
        await new Promise(resolve => setTimeout(resolve, 10))
      }

      // Check if user becomes trusted and gets higher limits
      const result = await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      
      // Trusted users should get adaptive limits
      expect(result.adaptiveLimit).toBeGreaterThan(10) // Should be 20 with 2.0 multiplier
      expect(result.riskScore).toBeLessThan(0.3)
    })

    it('should decrease limits for suspicious users', async () => {
      const context = { userId: 'suspicious-user', success: false }

      // Simulate suspicious behavior (many failures, fast requests)
      for (let i = 0; i < 8; i++) {
        await rateLimiter.checkLimit(mockRequest, '/api/test', context)
        // No delay - simulates automated/bot behavior
      }

      const result = await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      
      // Suspicious users should get reduced limits
      expect(result.adaptiveLimit).toBeLessThan(10) // Should be 5 with 2.0 divisor
      expect(result.riskScore).toBeGreaterThan(0.6)
    })

    it('should calculate risk score based on behavior patterns', async () => {
      const context = { userId: 'test-user' }

      // Test high failure rate increases risk
      for (let i = 0; i < 5; i++) {
        await rateLimiter.checkLimit(mockRequest, '/api/test', { 
          ...context, 
          success: i === 0 // Only first request succeeds
        })
      }

      const result = await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      expect(result.riskScore).toBeGreaterThan(0.3) // High failure rate = higher risk
    })
  })

  describe('Burst Detection', () => {
    it('should detect and block burst activity', async () => {
      // Create rate limiter with small burst allowance
      const burstLimiter = new EnhancedRateLimiter({
        windowMs: 60000,
        maxRequests: 10,
        burstAllowance: 2 // Allow only 2 requests in burst window
      })

      // Make rapid requests to trigger burst detection
      const promises = []
      for (let i = 0; i < 5; i++) {
        promises.push(burstLimiter.checkLimit(mockRequest, '/api/test'))
      }

      const results = await Promise.all(promises)
      
      // Some requests should be blocked due to burst detection
      const blockedResults = results.filter(r => !r.allowed)
      expect(blockedResults.length).toBeGreaterThan(0)
      
      // Should have burst-related blocking reason
      const burstBlocked = blockedResults.find(r => r.blockedReason === 'burst_limit_exceeded')
      expect(burstBlocked).toBeTruthy()
    })

    it('should log burst violations', async () => {
      const context = { userId: 'burst-user' }

      // Simulate burst activity that triggers logging
      for (let i = 0; i < 5; i++) {
        await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      }

      // Should log burst detection
      expect(mockAuditLogger.log).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'RATE_LIMIT_BURST_DETECTED',
          resourceType: 'SYSTEM',
          resourceId: '/api/test',
          severity: 'high'
        })
      )
    })
  })

  describe('IP Blocking', () => {
    it('should block IPs after suspicious activity', async () => {
      // Simulate highly suspicious behavior
      const suspiciousContext = { success: false }
      
      for (let i = 0; i < 15; i++) {
        await rateLimiter.checkLimit(mockRequest, '/api/test', suspiciousContext)
      }

      // IP should eventually be blocked
      const result = await rateLimiter.checkLimit(mockRequest, '/api/test')
      
      // Check if IP gets blocked (this depends on risk score threshold)
      if (result.riskScore && result.riskScore > 0.9) {
        expect(result.allowed).toBe(false)
        expect(result.blockedReason).toBeTruthy()
      }
    })

    it('should unblock IPs after block duration expires', async () => {
      // Manually block IP for testing
      const ipAddress = '***********'
      EnhancedRateLimiter.manualBlockIP(ipAddress, 0.01, 'test') // Block for 0.6 seconds

      // Should be blocked initially
      const blockedResult = await rateLimiter.checkLimit(mockRequest, '/api/test')
      expect(blockedResult.allowed).toBe(false)

      // Wait for block to expire
      await new Promise(resolve => setTimeout(resolve, 100))

      // Should be allowed after block expires
      const allowedResult = await rateLimiter.checkLimit(mockRequest, '/api/test')
      expect(allowedResult.allowed).toBe(true)
    })
  })

  describe('Behavior Analysis', () => {
    it('should track user behavior patterns', async () => {
      const context = { userId: 'behavior-test-user' }

      // Simulate mixed behavior
      const behaviors = [
        { success: true, delay: 1000 },
        { success: true, delay: 2000 },
        { success: false, delay: 500 },
        { success: true, delay: 1500 }
      ]

      for (const behavior of behaviors) {
        await rateLimiter.checkLimit(mockRequest, '/api/test', { 
          ...context, 
          success: behavior.success 
        })
        await new Promise(resolve => setTimeout(resolve, behavior.delay))
      }

      const result = await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      
      // Should have calculated risk score based on behavior
      expect(result.riskScore).toBeDefined()
      expect(typeof result.riskScore).toBe('number')
      expect(result.riskScore).toBeGreaterThanOrEqual(0)
      expect(result.riskScore).toBeLessThanOrEqual(1)
    })

    it('should differentiate between endpoints', async () => {
      const context = { userId: 'multi-endpoint-user' }

      // Hit different endpoints
      await rateLimiter.checkLimit(mockRequest, '/api/auth', context)
      await rateLimiter.checkLimit(mockRequest, '/api/users', context)
      await rateLimiter.checkLimit(mockRequest, '/api/jobs', context)

      const result = await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      
      // Behavior should account for multiple endpoints
      expect(result.riskScore).toBeDefined()
    })
  })

  describe('Memory Management', () => {
    it('should cleanup old data to prevent memory leaks', () => {
      // Generate test data
      for (let i = 0; i < 100; i++) {
        const testRequest = {
          headers: {
            get: () => `192.168.1.${i}`
          }
        }
        rateLimiter.checkLimit(testRequest, '/api/test')
      }

      const initialStats = EnhancedRateLimiter.getStatistics()
      expect(initialStats.activeRateLimits).toBeGreaterThan(0)

      // Run cleanup
      const cleaned = EnhancedRateLimiter.cleanup()
      expect(cleaned.cleaned).toBeDefined()

      const afterStats = EnhancedRateLimiter.getStatistics()
      // Should have some cleanup (exact numbers depend on timing)
      expect(afterStats).toBeDefined()
    })

    it('should provide accurate statistics', async () => {
      // Generate some test activity
      await rateLimiter.checkLimit(mockRequest, '/api/test', { userId: 'stats-user' })
      EnhancedRateLimiter.manualBlockIP('*************', 60, 'test')

      const stats = EnhancedRateLimiter.getStatistics()
      
      expect(stats).toHaveProperty('activeRateLimits')
      expect(stats).toHaveProperty('blockedIPs')
      expect(stats).toHaveProperty('trustedUsers')
      expect(stats).toHaveProperty('suspiciousUsers')
      expect(stats).toHaveProperty('totalRequests')
      
      expect(typeof stats.activeRateLimits).toBe('number')
      expect(typeof stats.blockedIPs).toBe('number')
      expect(stats.blockedIPs).toBe(1) // One manually blocked IP
    })
  })

  describe('Admin Functions', () => {
    it('should allow manual IP blocking and unblocking', () => {
      const ipAddress = '*************'
      
      // Block IP
      EnhancedRateLimiter.manualBlockIP(ipAddress, 30, 'admin_action')
      
      // Verify IP is blocked
      mockRequest.headers.get = jest.fn(() => ipAddress)
      return rateLimiter.checkLimit(mockRequest, '/api/test').then(result => {
        expect(result.allowed).toBe(false)
        expect(result.blockedReason).toBe('admin_action')
        
        // Unblock IP
        const unblocked = EnhancedRateLimiter.unblockIP(ipAddress)
        expect(unblocked).toBe(true)
        
        // Verify IP is unblocked
        return rateLimiter.checkLimit(mockRequest, '/api/test')
      }).then(result => {
        expect(result.allowed).toBe(true)
      })
    })

    it('should allow resetting behavior patterns', async () => {
      const context = { userId: 'reset-test-user' }
      
      // Build up behavior pattern
      for (let i = 0; i < 5; i++) {
        await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      }

      const beforeReset = await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      expect(beforeReset.riskScore).toBeDefined()

      // Reset behavior pattern
      const reset = EnhancedRateLimiter.resetBehaviorPattern(`user:${context.userId}`)
      expect(reset).toBe(true)

      // Pattern should be reset (would need to rebuild)
      const afterReset = await rateLimiter.checkLimit(mockRequest, '/api/test', context)
      expect(afterReset.riskScore).toBeLessThanOrEqual(beforeReset.riskScore)
    })
  })
})

describe('DistributedRateLimiter', () => {
  let distributedLimiter, mockRequest

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock database operations
    db.rateLimitEntry.findUnique = jest.fn().mockResolvedValue(null)
    db.rateLimitEntry.upsert = jest.fn().mockResolvedValue({
      id: 'entry-123',
      identifier: 'user:test',
      route: '/api/test',
      count: 1,
      windowStart: new Date(),
      metadata: {},
      updatedAt: new Date()
    })
    db.rateLimitEntry.findMany = jest.fn().mockResolvedValue([])
    db.rateLimitEntry.count = jest.fn().mockResolvedValue(0)
    db.rateLimitEntry.deleteMany = jest.fn().mockResolvedValue({ count: 0 })

    distributedLimiter = new DistributedRateLimiter({
      windowMs: 60000,
      maxRequests: 10,
      useDatabase: true,
      fallbackToLocal: true,
      syncInterval: 1000 // 1 second for testing
    })

    mockRequest = {
      headers: {
        get: jest.fn((header) => {
          switch (header) {
            case 'x-forwarded-for': return '***********'
            case 'user-agent': return 'Mozilla/5.0 Test Browser'
            default: return null
          }
        })
      }
    }
  })

  describe('Database Integration', () => {
    it('should sync with database for distributed coordination', async () => {
      const result = await distributedLimiter.checkLimit(mockRequest, '/api/test', {
        userId: 'distributed-user'
      })

      expect(result.allowed).toBe(true)
      expect(db.rateLimitEntry.upsert).toHaveBeenCalled()
    })

    it('should fallback to local limiting on database errors', async () => {
      // Simulate database error
      db.rateLimitEntry.upsert.mockRejectedValue(new Error('Database connection failed'))

      const result = await distributedLimiter.checkLimit(mockRequest, '/api/test')

      // Should still work due to fallback
      expect(result.allowed).toBe(true)
      expect(result.limit).toBe(10)
    })

    it('should handle distributed behavior analysis', async () => {
      // Mock behavior data from database
      db.rateLimitEntry.findMany.mockResolvedValue([
        {
          identifier: 'user:behavior-test',
          route: '/api/test',
          count: 5,
          metadata: { riskScore: 0.3 }
        },
        {
          identifier: 'user:behavior-test',
          route: '/api/other',
          count: 8,
          metadata: { riskScore: 0.5 }
        }
      ])

      const result = await distributedLimiter.checkLimit(mockRequest, '/api/test', {
        userId: 'behavior-test'
      })

      expect(result.allowed).toBe(true)
      expect(db.rateLimitEntry.findMany).toHaveBeenCalled()
    })
  })

  describe('Distributed Cleanup', () => {
    it('should cleanup old distributed data', async () => {
      db.rateLimitEntry.deleteMany.mockResolvedValue({ count: 25 })

      const result = await DistributedRateLimiter.cleanup()

      expect(result.cleaned).toBeGreaterThan(0)
      expect(db.rateLimitEntry.deleteMany).toHaveBeenCalled()
    })

    it('should provide distributed statistics', async () => {
      db.rateLimitEntry.count
        .mockResolvedValueOnce(50) // database entries
      
      db.auditLog.count
        .mockResolvedValueOnce(5) // recent violations

      const stats = await DistributedRateLimiter.getStatistics()

      expect(stats).toHaveProperty('distributedEntries')
      expect(stats).toHaveProperty('cachedEntries')
      expect(stats).toHaveProperty('databaseEntries')
      expect(stats).toHaveProperty('recentViolations')
      expect(stats.databaseEntries).toBe(50)
      expect(stats.recentViolations).toBe(5)
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection failures gracefully', async () => {
      // Simulate complete database failure
      db.rateLimitEntry.findUnique.mockRejectedValue(new Error('Connection lost'))
      db.rateLimitEntry.upsert.mockRejectedValue(new Error('Connection lost'))

      const result = await distributedLimiter.checkLimit(mockRequest, '/api/test')

      // Should fallback and still function
      expect(result.allowed).toBe(true)
    })

    it('should log distributed rate limiting errors', async () => {
      db.rateLimitEntry.upsert.mockRejectedValue(new Error('Database error'))

      await distributedLimiter.checkLimit(mockRequest, '/api/test', {
        userId: 'error-test-user'
      })

      // Should log the error
      expect(mockAuditLogger.log).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'DISTRIBUTED_RATE_LIMIT_ERROR',
          severity: 'high'
        })
      )
    })
  })
})