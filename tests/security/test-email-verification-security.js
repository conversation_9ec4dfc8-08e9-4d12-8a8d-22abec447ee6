const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals')
const { EmailVerificationSecurityManager } = require('../../src/lib/email-verification-security')
const { EmailVerificationRateLimiter } = require('../../src/lib/email-verification-rate-limiter')
const { db } = require('../../src/lib/db')
const { AuditLogger } = require('../../src/lib/audit')

// Mock dependencies
jest.mock('../../src/lib/db')
jest.mock('../../src/lib/audit')

describe('EmailVerificationSecurityManager', () => {
  let mockAuditLogger

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Mock audit logger
    mockAuditLogger = {
      log: jest.fn().mockResolvedValue(true)
    }
    AuditLogger.mockImplementation(() => mockAuditLogger)

    // Mock database operations
    db.verificationToken.deleteMany = jest.fn().mockResolvedValue({ count: 0 })
    db.verificationToken.create = jest.fn().mockResolvedValue({
      id: 'token-123',
      identifier: '<EMAIL>',
      token: 'hashed-token',
      expires: new Date(Date.now() + 15 * 60 * 1000)
    })
    db.verificationToken.findFirst = jest.fn()
    db.verificationToken.update = jest.fn()
    db.user.update = jest.fn().mockResolvedValue(true)
    db.auditLog.count = jest.fn().mockResolvedValue(2)
    db.auditLog.findFirst = jest.fn().mockResolvedValue(null)

    // Set environment variable for tests
    process.env.EMAIL_VERIFICATION_HMAC_SECRET = 'test-secret-key'
  })

  afterEach(() => {
    delete process.env.EMAIL_VERIFICATION_HMAC_SECRET
  })

  describe('generateSecureToken', () => {
    it('should generate a 6-digit token', () => {
      const { token } = EmailVerificationSecurityManager.generateSecureToken()
      
      expect(token).toMatch(/^\d{6}$/)
      expect(token.length).toBe(6)
    })

    it('should generate unique tokens each time', () => {
      const tokens = Array.from({ length: 100 }, () => 
        EmailVerificationSecurityManager.generateSecureToken().token
      )
      
      const uniqueTokens = new Set(tokens)
      expect(uniqueTokens.size).toBeGreaterThan(90) // Allow for small chance of collision
    })

    it('should generate corresponding hash for each token', () => {
      const { token, hashedToken } = EmailVerificationSecurityManager.generateSecureToken()
      
      expect(token).toBeTruthy()
      expect(hashedToken).toBeTruthy()
      expect(hashedToken).toMatch(/^[a-f0-9]{64}$/) // SHA256 hex
      expect(hashedToken).not.toBe(token)
    })
  })

  describe('createVerificationToken', () => {
    const context = {
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0',
      userId: 'user-123'
    }

    it('should create token successfully', async () => {
      const result = await EmailVerificationSecurityManager.createVerificationToken(
        '<EMAIL>',
        context
      )

      expect(result.success).toBe(true)
      expect(result.token).toMatch(/^\d{6}$/)
      expect(db.verificationToken.deleteMany).toHaveBeenCalledWith({
        where: { identifier: '<EMAIL>' }
      })
      expect(db.verificationToken.create).toHaveBeenCalled()
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'EMAIL_VERIFICATION_TOKEN_CREATED',
        resourceType: 'AUTHENTICATION',
        resourceId: '<EMAIL>',
        details: expect.objectContaining({
          expiresAt: expect.any(String),
          hashedToken: expect.stringMatching(/^[a-f0-9]{8}\.\.\.$/),
        }),
        severity: 'low'
      })
    })

    it('should reject when daily limit exceeded', async () => {
      db.auditLog.count.mockResolvedValue(10) // MAX_DAILY_SENDS

      const result = await EmailVerificationSecurityManager.createVerificationToken(
        '<EMAIL>',
        context
      )

      expect(result.success).toBe(false)
      expect(result.error).toBe('Daily verification limit exceeded. Try again tomorrow.')
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'EMAIL_VERIFICATION_RATE_LIMITED',
        resourceType: 'AUTHENTICATION',
        resourceId: '<EMAIL>',
        details: {
          reason: 'daily_limit_exceeded',
          dailySends: 10,
          limit: 10
        },
        severity: 'medium'
      })
    })

    it('should reject when resend too frequent', async () => {
      const recentAuditLog = {
        createdAt: new Date(Date.now() - 60 * 1000) // 1 minute ago
      }
      db.auditLog.findFirst.mockResolvedValue(recentAuditLog)

      const result = await EmailVerificationSecurityManager.createVerificationToken(
        '<EMAIL>',
        context
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('Please wait')
      expect(result.waitMinutes).toBe(1)
    })

    it('should handle database errors gracefully', async () => {
      db.verificationToken.create.mockRejectedValue(new Error('Database error'))

      const result = await EmailVerificationSecurityManager.createVerificationToken(
        '<EMAIL>',
        context
      )

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to create verification token')
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'EMAIL_VERIFICATION_TOKEN_ERROR',
        resourceType: 'AUTHENTICATION',
        resourceId: '<EMAIL>',
        details: { error: 'Database error' },
        severity: 'high'
      })
    })
  })

  describe('verifyToken', () => {
    const context = {
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0',
      userId: 'user-123'
    }

    it('should verify valid token successfully', async () => {
      const { token, hashedToken } = EmailVerificationSecurityManager.generateSecureToken()
      
      const mockVerificationRecord = {
        id: 'token-123',
        token: hashedToken,
        expires: new Date(Date.now() + 15 * 60 * 1000),
        metadata: { attemptCount: 0 }
      }
      
      db.verificationToken.findFirst.mockResolvedValue(mockVerificationRecord)
      db.verificationToken.update.mockResolvedValue(mockVerificationRecord)

      const result = await EmailVerificationSecurityManager.verifyToken(
        '<EMAIL>',
        token,
        context
      )

      expect(result.success).toBe(true)
      expect(db.user.update).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
        data: { emailVerified: expect.any(Date) }
      })
      expect(db.verificationToken.deleteMany).toHaveBeenCalled()
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'EMAIL_VERIFICATION_SUCCESS',
        resourceType: 'AUTHENTICATION',
        resourceId: '<EMAIL>',
        details: { attemptCount: 1 },
        severity: 'low'
      })
    })

    it('should reject when token not found', async () => {
      db.verificationToken.findFirst.mockResolvedValue(null)

      const result = await EmailVerificationSecurityManager.verifyToken(
        '<EMAIL>',
        '123456',
        context
      )

      expect(result.success).toBe(false)
      expect(result.error).toBe('No verification code found. Please request a new one.')
      expect(result.requiresNewToken).toBe(true)
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'EMAIL_VERIFICATION_TOKEN_NOT_FOUND',
        resourceType: 'AUTHENTICATION',
        resourceId: '<EMAIL>',
        details: { providedToken: '123456' },
        severity: 'medium'
      })
    })

    it('should reject expired token', async () => {
      const mockVerificationRecord = {
        id: 'token-123',
        token: 'hashed-token',
        expires: new Date(Date.now() - 60 * 1000), // Expired 1 minute ago
        metadata: {}
      }
      
      db.verificationToken.findFirst.mockResolvedValue(mockVerificationRecord)

      const result = await EmailVerificationSecurityManager.verifyToken(
        '<EMAIL>',
        '123456',
        context
      )

      expect(result.success).toBe(false)
      expect(result.error).toBe('Verification code has expired. Please request a new one.')
      expect(result.requiresNewToken).toBe(true)
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'EMAIL_VERIFICATION_TOKEN_EXPIRED',
        resourceType: 'AUTHENTICATION',
        resourceId: '<EMAIL>',
        details: expect.objectContaining({
          expiredAt: expect.any(String),
          providedToken: '123456'
        }),
        severity: 'low'
      })
    })

    it('should enforce attempt limits', async () => {
      const mockVerificationRecord = {
        id: 'token-123',
        token: 'different-hash',
        expires: new Date(Date.now() + 15 * 60 * 1000),
        metadata: { attemptCount: 5 } // MAX_ATTEMPTS
      }
      
      db.verificationToken.findFirst.mockResolvedValue(mockVerificationRecord)

      const result = await EmailVerificationSecurityManager.verifyToken(
        '<EMAIL>',
        '123456',
        context
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('Too many failed attempts')
      expect(result.lockoutMinutes).toBe(30)
      expect(result.requiresNewToken).toBe(true)
      expect(mockAuditLogger.log).toHaveBeenCalledWith({
        action: 'EMAIL_VERIFICATION_ATTEMPTS_EXCEEDED',
        resourceType: 'AUTHENTICATION',
        resourceId: '<EMAIL>',
        details: {
          attemptCount: 6,
          maxAttempts: 5,
          lockoutMinutes: 30
        },
        severity: 'high'
      })
    })

    it('should track invalid attempts', async () => {
      const { hashedToken } = EmailVerificationSecurityManager.generateSecureToken()
      
      const mockVerificationRecord = {
        id: 'token-123',
        token: hashedToken,
        expires: new Date(Date.now() + 15 * 60 * 1000),
        metadata: { attemptCount: 2 }
      }
      
      db.verificationToken.findFirst.mockResolvedValue(mockVerificationRecord)
      db.verificationToken.update.mockResolvedValue(mockVerificationRecord)

      const result = await EmailVerificationSecurityManager.verifyToken(
        '<EMAIL>',
        '999999', // Wrong token
        context
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid verification code')
      expect(result.remainingAttempts).toBe(2) // 5 - 3
      expect(db.verificationToken.update).toHaveBeenCalledWith({
        where: { id: 'token-123' },
        data: {
          metadata: expect.objectContaining({
            attemptCount: 3,
            lastAttemptAt: expect.any(String),
            lastAttemptIP: '***********'
          })
        }
      })
    })
  })

  describe('getVerificationStatus', () => {
    it('should return no active token when none exists', async () => {
      db.verificationToken.findFirst.mockResolvedValue(null)

      const status = await EmailVerificationSecurityManager.getVerificationStatus(
        '<EMAIL>'
      )

      expect(status.hasActiveToken).toBe(false)
      expect(status.isLocked).toBe(false)
    })

    it('should return active token status', async () => {
      const mockToken = {
        expires: new Date(Date.now() + 15 * 60 * 1000),
        metadata: { attemptCount: 2 }
      }
      
      db.verificationToken.findFirst.mockResolvedValue(mockToken)

      const status = await EmailVerificationSecurityManager.getVerificationStatus(
        '<EMAIL>'
      )

      expect(status.hasActiveToken).toBe(true)
      expect(status.isLocked).toBe(false)
      expect(status.expiresAt).toEqual(mockToken.expires)
      expect(status.attemptsRemaining).toBe(3) // 5 - 2
    })

    it('should return lockout status', async () => {
      const mockLockout = {
        expires: new Date(Date.now() + 30 * 60 * 1000),
        metadata: { type: 'lockout', lockoutMinutes: 30 }
      }
      
      db.verificationToken.findFirst.mockResolvedValue(mockLockout)

      const status = await EmailVerificationSecurityManager.getVerificationStatus(
        '<EMAIL>'
      )

      expect(status.hasActiveToken).toBe(false)
      expect(status.isLocked).toBe(true)
      expect(status.lockoutMinutes).toBeGreaterThan(0)
    })
  })

  describe('detectSuspiciousActivity', () => {
    it('should detect excessive daily requests', async () => {
      db.auditLog.count.mockResolvedValue(10) // MAX_DAILY_SENDS

      const result = await EmailVerificationSecurityManager.detectSuspiciousActivity(
        '<EMAIL>',
        { ipAddress: '***********' }
      )

      expect(result.suspicious).toBe(true)
      expect(result.reasons).toContain('excessive_daily_requests')
    })

    it('should detect multiple IP failures', async () => {
      const failedAttempts = [
        { ipAddress: '***********' },
        { ipAddress: '***********' },
        { ipAddress: '***********' },
        { ipAddress: '***********' }
      ]
      db.auditLog.findMany.mockResolvedValue(failedAttempts)

      const result = await EmailVerificationSecurityManager.detectSuspiciousActivity(
        '<EMAIL>',
        { ipAddress: '***********' }
      )

      expect(result.suspicious).toBe(true)
      expect(result.reasons).toContain('multiple_ip_failures')
    })

    it('should return clean status for normal activity', async () => {
      db.auditLog.count.mockResolvedValue(3)
      db.auditLog.findMany.mockResolvedValue([
        { ipAddress: '***********' },
        { ipAddress: '***********' }
      ])

      const result = await EmailVerificationSecurityManager.detectSuspiciousActivity(
        '<EMAIL>',
        { ipAddress: '***********' }
      )

      expect(result.suspicious).toBe(false)
      expect(result.reasons).toEqual([])
    })
  })

  describe('cleanupExpiredTokens', () => {
    it('should cleanup expired tokens', async () => {
      db.verificationToken.deleteMany.mockResolvedValue({ count: 5 })

      const result = await EmailVerificationSecurityManager.cleanupExpiredTokens()

      expect(result.cleaned).toBe(5)
      expect(db.verificationToken.deleteMany).toHaveBeenCalledWith({
        where: { expires: { lt: expect.any(Date) } }
      })
    })
  })
})

describe('EmailVerificationRateLimiter', () => {
  beforeEach(() => {
    // Clear rate limiter state before each test
    EmailVerificationRateLimiter.cleanup()
  })

  describe('checkEmailRateLimit', () => {
    it('should allow requests within limit', () => {
      const result = EmailVerificationRateLimiter.checkEmailRateLimit('<EMAIL>')

      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(9) // 10 - 1
      expect(result.resetTime).toBeInstanceOf(Date)
    })

    it('should reject requests over limit', () => {
      const email = '<EMAIL>'
      
      // Make 10 requests (the limit)
      for (let i = 0; i < 10; i++) {
        EmailVerificationRateLimiter.checkEmailRateLimit(email)
      }
      
      // 11th request should be rejected
      const result = EmailVerificationRateLimiter.checkEmailRateLimit(email)

      expect(result.allowed).toBe(false)
      expect(result.remaining).toBe(0)
      expect(result.retryAfter).toBeGreaterThan(0)
    })
  })

  describe('checkSuspiciousActivity', () => {
    it('should detect excessive IP requests', () => {
      const ip = '***********'
      
      // Simulate many requests from same IP
      for (let i = 0; i < 25; i++) {
        EmailVerificationRateLimiter.checkIPRateLimit(ip)
      }

      const result = EmailVerificationRateLimiter.checkSuspiciousActivity(ip, '<EMAIL>')

      expect(result.suspicious).toBe(true)
      expect(result.blocked).toBe(true)
      expect(result.reason).toBe('excessive_requests_from_ip')
    })

    it('should not flag normal activity as suspicious', () => {
      const result = EmailVerificationRateLimiter.checkSuspiciousActivity(
        '***********', 
        '<EMAIL>'
      )

      expect(result.suspicious).toBe(false)
      expect(result.blocked).toBe(false)
    })
  })

  describe('applyVerificationRateLimit', () => {
    const mockRequest = {
      headers: {
        get: jest.fn().mockReturnValue('***********')
      }
    }

    it('should allow valid verification requests', async () => {
      const result = await EmailVerificationRateLimiter.applyVerificationRateLimit(
        mockRequest,
        '<EMAIL>'
      )

      expect(result.allowed).toBe(true)
      expect(result.headers['X-RateLimit-Remaining']).toBeTruthy()
    })

    it('should reject when email limit exceeded', async () => {
      const email = '<EMAIL>'
      
      // Exhaust email limit
      for (let i = 0; i < 10; i++) {
        EmailVerificationRateLimiter.checkEmailRateLimit(email)
      }

      const result = await EmailVerificationRateLimiter.applyVerificationRateLimit(
        mockRequest,
        email
      )

      expect(result.allowed).toBe(false)
      expect(result.error).toContain('Too many verification requests for this email')
      expect(result.headers['Retry-After']).toBeTruthy()
    })
  })

  describe('blockIP and unblockIP', () => {
    it('should manually block and unblock IPs', () => {
      const ip = '***********'
      
      EmailVerificationRateLimiter.blockIP(ip, 60)
      
      const suspiciousCheck = EmailVerificationRateLimiter.checkSuspiciousActivity(ip, '<EMAIL>')
      expect(suspiciousCheck.blocked).toBe(true)
      
      const unblocked = EmailVerificationRateLimiter.unblockIP(ip)
      expect(unblocked).toBe(true)
      
      const checkAfterUnblock = EmailVerificationRateLimiter.checkSuspiciousActivity(ip, '<EMAIL>')
      expect(checkAfterUnblock.blocked).toBe(false)
    })
  })

  describe('getStatistics', () => {
    it('should return accurate statistics', () => {
      EmailVerificationRateLimiter.checkEmailRateLimit('<EMAIL>')
      EmailVerificationRateLimiter.checkEmailRateLimit('<EMAIL>')
      EmailVerificationRateLimiter.blockIP('***********')

      const stats = EmailVerificationRateLimiter.getStatistics()

      expect(stats.activeKeys).toBeGreaterThan(0)
      expect(stats.blockedIPs).toBe(1)
      expect(stats.totalRequests).toBeGreaterThan(0)
    })
  })
})