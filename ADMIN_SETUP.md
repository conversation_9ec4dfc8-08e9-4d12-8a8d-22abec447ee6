# 🚀 Sourceflex SUPER_ADMIN Setup Guide

This guide will help you create the first SUPER_ADMIN account and access the admin dashboard.

## 📋 Prerequisites

1. **Development Environment Ready**
   ```bash
   npm install
   npm run dev:db          # Start PostgreSQL
   npm run generate        # Generate Prisma client
   npm run dev:seed        # Seed database
   npm run dev             # Start server (likely on port 3001)
   ```

2. **Environment Variables Set**
   - Make sure `.env.local` includes: `ADMIN_CREATION_KEY="super-secret-admin-key-2024"`

## 🔐 Creating Your SUPER_ADMIN Account

### Step 1: Access Admin Signup Page
Visit: `http://localhost:3001/auth/admin/signup`

### Step 2: Fill Out the Form
- **Full Name**: Your name (e.g., "Platform Administrator")
- **Email**: Your admin email (e.g., "<EMAIL>")
- **Password**: Strong password (minimum 12 characters)
- **Confirm Password**: Same password
- **Admin Creation Key**: `super-secret-admin-key-2024`

### Step 3: Create Account
Click "Create Super Admin" - you'll see a success message and be redirected to sign in.

### Step 4: Sign In
Visit: `http://localhost:3001/auth/admin/signin`
- Use your email and password
- Complete 2FA setup if prompted

### Step 5: Access Admin Dashboard
Once signed in, you'll be redirected to: `http://localhost:3001/admin/dashboard`

## 🎛️ Admin Dashboard Features

Your admin dashboard includes:

### 📊 Main Dashboard (`/admin/dashboard`)
- Platform statistics (users, organizations, jobs, applications)
- Recent activity feed with audit logs
- Quick action buttons

### 👥 User Management (`/admin/users`)
- View all platform users
- Search and filter by role
- User actions (edit, email, delete)
- Pagination and sorting

### 🏢 Organization Management (`/admin/organizations`)
- View all client organizations
- Organization statistics
- Settings management
- CRUD operations

### 📝 Audit Logs (`/admin/audit`)
- Complete system activity monitoring
- Filter by action, resource type, success/failure
- Export functionality
- Detailed event tracking

### 🔍 System Health (`/admin/system-health`)
- Real-time system monitoring
- Performance metrics
- Active alerts
- Resource usage tracking

## 🔑 Important Notes

1. **Security**: Change the `ADMIN_CREATION_KEY` in production
2. **2FA**: Set up two-factor authentication immediately
3. **Backup**: The admin account has full platform access
4. **Audit**: All admin actions are logged in the audit system

## 🆘 Troubleshooting

### Can't Access Signup Page?
- Ensure dev server is running: `npm run dev`
- Check the correct port (likely 3001, not 3000)
- Verify database is seeded: `npm run dev:seed`

### Invalid Admin Key Error?
- Check `.env.local` has `ADMIN_CREATION_KEY="super-secret-admin-key-2024"`
- Restart the dev server after adding env variable

### Database Errors?
- Ensure PostgreSQL is running: `npm run dev:db`
- Reset database if needed: `npx prisma migrate reset`
- Re-seed: `npm run dev:seed`

## 🎯 Next Steps After Setup

1. **Configure 2FA**: Set up two-factor authentication
2. **Create Organizations**: Add your first client organizations
3. **Invite Users**: Create business users for organizations
4. **Review Settings**: Configure platform settings
5. **Monitor Activity**: Review audit logs regularly

## 🔒 Production Deployment Notes

For production deployment:

1. **Change Admin Key**: Use a secure, unique `ADMIN_CREATION_KEY`
2. **Disable Signup**: Consider removing the admin signup route after initial setup
3. **Enable HTTPS**: Ensure all traffic is encrypted
4. **Database Security**: Use secure database credentials
5. **Monitor Access**: Regularly review admin access logs

---

**Need Help?** Check the main `CLAUDE.md` file for detailed development instructions.