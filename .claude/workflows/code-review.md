# Code Review Workflow

## Review Process Overview

### Code Review Philosophy
- **Quality over Speed**: Thorough reviews prevent technical debt
- **Knowledge Sharing**: Reviews spread knowledge across the team
- **Mentorship**: Senior developers guide junior team members
- **Consistency**: Maintain coding standards and patterns

### Review Timeline
- **Initial Review**: Within 24 hours of PR creation
- **Follow-up**: Within 4 hours of updates
- **Final Approval**: Same day for small changes, within 2 days for large features

## Pre-Review Checklist (Author)

### Before Creating PR
- [ ] **Self-review completed** - Review your own changes first
- [ ] **All tests passing** - Unit, integration, and E2E tests
- [ ] **Code follows standards** - ESLint, Prettier, TypeScript strict
- [ ] **Documentation updated** - Comments, README, API docs
- [ ] **No debugging artifacts** - Remove console.logs, debugger statements
- [ ] **File length compliance** - All files under 300 lines
- [ ] **Agent guidelines followed** - Appropriate agent handled the changes

### PR Description Quality
```markdown
## Summary
Clear, concise description of what this PR accomplishes.

## Changes Made
- Bullet points of specific changes
- Link to relevant issues
- Mention breaking changes if any

## Testing
- How the changes were tested
- New test cases added
- Manual testing performed

## Screenshots/GIFs
For UI changes, include visual proof of functionality.

## Deployment Notes
Any special considerations for deployment.
```

## Review Guidelines (Reviewer)

### Review Priorities
1. **Functionality**: Does the code work as intended?
2. **Security**: Are there any security vulnerabilities?
3. **Performance**: Will this impact application performance?
4. **Maintainability**: Is the code easy to understand and modify?
5. **Standards**: Does it follow our coding standards?

### Code Quality Checklist
- [ ] **Logic correctness** - Code does what it's supposed to do
- [ ] **Error handling** - Proper error handling and edge cases
- [ ] **Type safety** - Proper TypeScript usage, no `any` types
- [ ] **Performance** - No unnecessary re-renders, efficient queries
- [ ] **Security** - No exposed secrets, proper input validation
- [ ] **Testing** - Adequate test coverage for new functionality

### Agent-Specific Review Focus

#### Database Agent Changes
- [ ] **Schema changes** are backward compatible
- [ ] **Migrations** are safe and reversible
- [ ] **Indexes** are properly defined for performance
- [ ] **Data integrity** constraints are in place
- [ ] **Query optimization** for complex operations

#### Frontend Agent Changes
- [ ] **Accessibility** compliance (WCAG guidelines)
- [ ] **Responsive design** works on all screen sizes
- [ ] **Performance** - bundle size, loading times
- [ ] **User experience** - intuitive and consistent
- [ ] **Error states** properly handled

#### API Agent Changes
- [ ] **Input validation** with Zod schemas
- [ ] **Error handling** with proper HTTP status codes
- [ ] **Rate limiting** for public endpoints
- [ ] **Documentation** - API endpoints documented
- [ ] **Backwards compatibility** for existing clients

#### Auth Agent Changes
- [ ] **Security best practices** followed
- [ ] **Permission checks** properly implemented
- [ ] **Session management** secure and efficient
- [ ] **Audit logging** for sensitive operations
- [ ] **OWASP guidelines** compliance

## Review Comments Guidelines

### Constructive Feedback
```markdown
# Good Examples

## Suggestion with reasoning
💡 Consider extracting this logic into a custom hook for reusability:
```typescript
const useJobFilters = () => {
  // extracted logic here
}
```

## Question for clarification
❓ Could you explain the reasoning behind using setTimeout here? 
Is there a specific timing requirement?

## Praise good practices
✅ Great job implementing proper error boundaries here!

# Avoid These

## Vague criticism
❌ "This doesn't look right"

## Nitpicking without value
❌ "Move this to line 15"

## Personal preferences without reasoning
❌ "I prefer using for loops instead of map"
```

### Comment Categories
- **🚨 Blocking**: Must be fixed before merge
- **💡 Suggestion**: Improvement idea, not mandatory
- **❓ Question**: Needs clarification
- **📚 Learning**: Educational comment for knowledge sharing
- **✅ Praise**: Acknowledging good work

## Review Types

### Lightweight Review (< 50 lines)
- **Focus**: Logic correctness, obvious bugs
- **Timeline**: Within 2 hours
- **Approval**: Single approval sufficient

### Standard Review (50-200 lines)
- **Focus**: Full checklist review
- **Timeline**: Within 24 hours
- **Approval**: Single approval, but encourage multiple reviewers

### Comprehensive Review (> 200 lines)
- **Focus**: Architecture, design patterns, comprehensive testing
- **Timeline**: 1-2 days
- **Approval**: Multiple approvals required
- **Recommendation**: Break into smaller PRs when possible

## Agent Collaboration in Reviews

### Cross-Agent Review Requirements
```typescript
// Example: Job creation feature touching multiple agents

// Database Agent reviewed schema changes
model Job {
  id          String   @id @default(cuid())
  title       String   @db.VarChar(100) // Length constraint
  salary      Decimal? @db.Decimal(10,2) // Proper decimal handling
  
  @@index([organizationId, status]) // Performance index
}

// API Agent reviewed business logic
export const createJob = protectedProcedure
  .input(createJobSchema) // Proper validation
  .mutation(async ({ ctx, input }) => {
    // Security: Check organization ownership
    if (input.organizationId !== ctx.user.organizationId) {
      throw new TRPCError({ code: 'FORBIDDEN' });
    }
    // Implementation...
  });

// Frontend Agent reviewed UI components
export function JobForm() {
  // Accessibility: proper labels and ARIA attributes
  // Performance: optimized re-renders
  // UX: proper loading and error states
}
```

### Review Assignment Strategy
- **Primary Reviewer**: Agent responsible for the main functionality
- **Secondary Reviewer**: Agent handling integration points
- **Security Reviewer**: Auth Agent for security-sensitive changes
- **Performance Reviewer**: Senior developer for performance-critical changes

## Common Review Scenarios

### New Feature Implementation
1. **Architecture Review**: Does the design fit our patterns?
2. **Integration Review**: How does it interact with existing systems?
3. **Testing Review**: Is test coverage adequate?
4. **Documentation Review**: Are changes properly documented?

### Bug Fix Review
1. **Root Cause**: Is the actual problem being solved?
2. **Testing**: Is there a test to prevent regression?
3. **Side Effects**: Could this fix break something else?
4. **Monitoring**: Do we need additional logging?

### Refactoring Review
1. **Scope**: Is the refactoring focused and manageable?
2. **Backwards Compatibility**: Are existing APIs preserved?
3. **Risk Assessment**: What could go wrong?
4. **Testing**: How do we verify nothing broke?

## Review Tools and Automation

### GitHub PR Templates
```markdown
<!-- .github/pull_request_template.md -->
## Agent Responsibility
- [ ] Database Agent (schema/migration changes)
- [ ] Frontend Agent (UI/UX changes)
- [ ] API Agent (business logic changes)
- [ ] Auth Agent (security changes)
- [ ] Testing Agent (test coverage)

## Review Checklist
- [ ] Code follows project standards
- [ ] All tests passing
- [ ] Documentation updated
- [ ] No breaking changes (or properly documented)
- [ ] Performance impact assessed
- [ ] Security implications considered

## Deployment Impact
- [ ] Requires database migration
- [ ] Requires environment variable changes
- [ ] Affects existing APIs
- [ ] No deployment impact
```

### Automated Review Checks
```yaml
# .github/workflows/pr-review.yml
name: PR Review Automation

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  automated-review:
    runs-on: ubuntu-latest
    steps:
      - name: File Length Check
        run: |
          # Check for files over 300 lines
          find src -name "*.ts" -o -name "*.tsx" | xargs wc -l | awk '$1 > 300 { print "❌ File too long: " $2 " (" $1 " lines)" }'

      - name: Security Scan
        run: npm audit --audit-level moderate

      - name: Performance Check
        run: |
          # Check bundle size impact
          npm run build:analyze
          
      - name: Test Coverage
        run: |
          # Ensure test coverage doesn't decrease
          npm run test:coverage
```

## Review Metrics and Quality

### Review Quality Indicators
- **Review Coverage**: Percentage of code changes reviewed
- **Review Turnaround**: Time from PR creation to approval
- **Defect Detection**: Issues caught in review vs. production
- **Knowledge Sharing**: Cross-team participation in reviews

### Continuous Improvement
- **Weekly Review Retrospectives**: What worked, what didn't?
- **Review Training**: Regular sessions on effective code review
- **Tool Evaluation**: Are our tools helping or hindering?
- **Process Refinement**: Adjust process based on team feedback

### Review Analytics
```typescript
// Example: Track review metrics
interface ReviewMetrics {
  prId: string;
  linesChanged: number;
  reviewers: string[];
  timeToFirstReview: number; // hours
  timeToApproval: number; // hours
  commentsCount: number;
  iterationsCount: number;
  defectsFound: number;
}

// Generate monthly review reports
function generateReviewReport(metrics: ReviewMetrics[]) {
  return {
    averageReviewTime: calculateAverage(metrics.map(m => m.timeToApproval)),
    reviewParticipation: calculateParticipation(metrics),
    defectDetectionRate: calculateDefectRate(metrics),
    recommendedImprovements: generateRecommendations(metrics),
  };
}
```

## Review Best Practices

### For Authors
- **Small PRs**: Keep changes focused and manageable
- **Clear Context**: Provide background and reasoning
- **Responsive**: Address feedback promptly and thoroughly
- **Learning**: Ask questions when feedback is unclear

### For Reviewers
- **Timely Reviews**: Don't let PRs sit idle
- **Constructive Feedback**: Focus on code, not person
- **Thorough Review**: Don't just approve to move things along
- **Mentorship**: Use reviews as teaching opportunities

### For Teams
- **Shared Standards**: Everyone understands and follows guidelines
- **Continuous Learning**: Regular review training and improvement
- **Tool Support**: Use tools to automate what can be automated
- **Culture**: Foster a culture of constructive feedback and learning