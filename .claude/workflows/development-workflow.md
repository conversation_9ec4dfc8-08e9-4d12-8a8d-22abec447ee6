# Development Workflow

## Daily Development Process

### 1. Setup & Environment Check
```bash
# Start daily development session
npm run dev:db          # Start local PostgreSQL
npm run dev:studio      # Open Prisma Studio (optional)
npm run dev            # Start Next.js development server
```

### 2. Feature Development Workflow
1. **Create Feature Branch**
   ```bash
   git checkout -b feature/user-authentication
   ```

2. **Use Appropriate Agent**
   - Consult relevant agent documentation
   - Follow agent-specific guidelines
   - Maintain agent responsibilities boundaries

3. **Development Standards**
   - File length limit: 300 lines max
   - Read entire file before modifications
   - Follow TypeScript strict mode
   - Use proper error handling

4. **Testing Requirements**
   ```bash
   npm run test           # Run unit tests
   npm run test:e2e       # Run E2E tests
   npm run type-check     # TypeScript validation
   npm run lint           # Code quality check
   ```

### 3. Code Review Process
1. **Self-Review Checklist**
   - [ ] All tests passing
   - [ ] TypeScript compilation successful
   - [ ] Code follows style guidelines
   - [ ] No console.log statements
   - [ ] Proper error handling implemented
   - [ ] File length within limits

2. **Create Pull Request**
   ```bash
   git push origin feature/user-authentication
   # Create PR via GitHub UI
   ```

3. **PR Requirements**
   - Clear description of changes
   - Reference related issues
   - Include screenshots for UI changes
   - All CI checks passing

## Agent Collaboration Workflow

### Inter-Agent Communication
```
Database Agent ← → API Agent ← → Frontend Agent
       ↓               ↓              ↓
   Auth Agent    Module Specialists  Testing Agent
```

### Task Delegation Rules
1. **Database Changes**: Always involve Database Agent
2. **UI Components**: Frontend Agent handles implementation
3. **Business Logic**: API Agent manages workflows
4. **Security**: Auth Agent reviews all auth-related changes
5. **Quality**: Testing Agent validates all implementations

### Conflict Resolution
- **Schema Changes**: Database Agent has final authority
- **API Design**: API Agent leads, with input from specialists
- **UI/UX Decisions**: Frontend Agent decides with stakeholder input
- **Security Policies**: Auth Agent has veto power

## Feature Implementation Workflow

### Planning Phase
1. **Requirements Analysis**
   - Review project requirements document
   - Identify affected modules and agents
   - Plan agent collaboration strategy

2. **Technical Design**
   - Database schema changes (Database Agent)
   - API endpoint design (API Agent)
   - UI component planning (Frontend Agent)
   - Authentication requirements (Auth Agent)

3. **Implementation Order**
   1. Database schema and migrations
   2. API endpoints and business logic
   3. Frontend components and pages
   4. Authentication and authorization
   5. Testing and validation

### Implementation Phase
```typescript
// Example: Job Posting Feature Implementation

// 1. Database Agent: Schema definition
model Job {
  id          String   @id @default(cuid())
  title       String
  description String
  // ... other fields
}

// 2. API Agent: tRPC router
export const jobRouter = createTRPCRouter({
  create: protectedProcedure
    .input(createJobSchema)
    .mutation(async ({ ctx, input }) => {
      // Implementation
    }),
});

// 3. Frontend Agent: React component
export function CreateJobForm() {
  const createJob = api.jobs.create.useMutation();
  // Component implementation
}

// 4. Testing Agent: Test suite
describe('Job Creation', () => {
  it('should create job with valid data', () => {
    // Test implementation
  });
});
```

## Quality Assurance Workflow

### Pre-commit Checks
```bash
# Automated via Husky hooks
npm run type-check     # TypeScript validation
npm run lint          # ESLint checks
npm run test:changed  # Test affected files
npm run format        # Prettier formatting
```

### Code Quality Gates
1. **File Length Validation**
   - Reject files > 300 lines
   - Suggest refactoring for files > 250 lines

2. **Type Safety**
   - Strict TypeScript compilation
   - No `any` types allowed
   - Proper error handling

3. **Performance**
   - Bundle size monitoring
   - Database query optimization
   - Core Web Vitals compliance

### Testing Strategy
```
Unit Tests (70%)
├── Pure functions
├── React components
├── Database operations
└── Business logic

Integration Tests (20%)
├── API endpoints
├── Database transactions
└── External service integrations

E2E Tests (10%)
├── Critical user workflows
├── Authentication flows
└── Cross-module interactions
```

## Deployment Workflow

### Pre-deployment Checklist
- [ ] All tests passing in CI/CD
- [ ] Security scan completed
- [ ] Performance benchmarks met
- [ ] Database migrations tested
- [ ] Environment variables configured

### Deployment Process
1. **Staging Deployment**
   ```bash
   git checkout staging
   git merge main
   # Automatic deployment to staging environment
   ```

2. **Production Deployment**
   ```bash
   git checkout main
   git merge staging
   # Automatic deployment to production
   ```

3. **Post-deployment Validation**
   - Health check endpoints
   - Critical path testing
   - Performance monitoring
   - Error rate monitoring

## Emergency Response Workflow

### Incident Response
1. **Detection**
   - Automated alerts from monitoring
   - User reports via support channels
   - Health check failures

2. **Assessment**
   - Determine severity level
   - Identify affected systems
   - Estimate impact and timeline

3. **Response**
   - Immediate mitigation steps
   - Communication to stakeholders
   - Fix implementation and deployment

4. **Recovery**
   - Service restoration verification
   - Data integrity checks
   - Post-incident review

### Rollback Procedures
```bash
# Database rollback
npm run rollback:migration -- migration_name

# Application rollback
vercel rollback --target production

# Emergency hotfix deployment
git checkout -b hotfix/critical-issue
# Implement fix
git checkout main
git merge hotfix/critical-issue
# Deploy immediately
```

## Documentation Workflow

### Code Documentation
- **Inline Comments**: For complex logic only
- **JSDoc**: For public APIs and utilities
- **README Updates**: For new features or setup changes
- **Agent Documentation**: Update relevant agent specs

### Knowledge Management
- Update architecture documents for structural changes
- Maintain coding standards for new patterns
- Document integration patterns and best practices
- Keep troubleshooting guides current

### Change Communication
- Update CHANGELOG.md for user-facing changes
- Notify team of breaking changes
- Document migration guides for major updates
- Maintain API documentation for external integrations