# Sourceflex System Architecture

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Public Users  │    │  Authenticated  │    │  Admin Users    │
│   (Job Seekers) │    │     Users       │    │  (Super Admin)  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Next.js 15 Application                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │   Public    │  │  Dashboard  │  │      Admin Panel        │  │
│  │  Job Board  │  │   (Multi-   │  │   (Platform Admin)      │  │
│  │             │  │   tenant)   │  │                         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────┬───────────────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                     tRPC API Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │  Job Board  │  │     CRM     │  │         VMS             │  │
│  │   Router    │  │   Router    │  │       Router            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │     ATS     │  │  Social     │  │        Auth             │  │
│  │   Router    │  │   Router    │  │       Router            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────┬───────────────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Data & Services Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │  Prisma ORM │  │  Auth.js    │  │    External APIs        │  │
│  │             │  │  Sessions   │  │  (Social Media, Email)  │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────┬───────────────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │  Supabase   │  │ Cloudflare  │  │       Vercel            │  │
│  │ PostgreSQL  │  │     R2      │  │    (Hosting)            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Multi-Tenant Architecture

### Organization Isolation Strategy
- **Row-Level Security**: Each record belongs to an organization
- **Schema Isolation**: Shared schema with organization_id filtering
- **Data Sovereignty**: Configurable data residency options

### Tenant Data Model
```typescript
interface Organization {
  id: string;
  name: string;
  slug: string; // URL-friendly identifier
  domain?: string; // Custom domain for job board
  settings: OrganizationSettings;
  subscription: SubscriptionPlan;
  createdAt: Date;
}

interface User {
  id: string;
  email: string;
  organizationId: string;
  role: UserRole;
  permissions: Permission[];
}
```

## Module Architecture

### Job Board Module
```
/src/app/(public)/
├── jobs/
│   ├── page.tsx          # Job listings
│   ├── [slug]/
│   │   └── page.tsx      # Individual job page
│   └── apply/
│       └── [jobId]/
│           └── page.tsx  # Application form

/src/app/(dashboard)/jobs/
├── page.tsx              # Job management dashboard
├── create/
│   └── page.tsx          # Create new job
└── [jobId]/
    ├── page.tsx          # Job details and applications
    └── edit/
        └── page.tsx      # Edit job posting
```

### Dashboard Architecture
```
/src/app/(dashboard)/
├── layout.tsx            # Dashboard layout with navigation
├── page.tsx              # Main dashboard
├── jobs/                 # Job management
├── candidates/           # ATS functionality
├── clients/              # CRM functionality
├── vendors/              # VMS functionality
├── social/               # Social media management
└── settings/             # Organization settings
```

## Data Flow Architecture

### Request Flow
1. **Client Request** → Next.js App Router
2. **Authentication** → Auth.js middleware validation
3. **Authorization** → tRPC context with user permissions
4. **Business Logic** → tRPC router procedures
5. **Data Access** → Prisma ORM queries
6. **Response** → Type-safe JSON response

### Real-time Updates
- **Server-Sent Events** for live dashboard updates
- **WebSocket connections** for real-time notifications
- **Optimistic updates** for better user experience

## Security Architecture

### Authentication Flow
```
User Login → OAuth Provider → Auth.js → JWT Token → Session Cookie
```

### Authorization Layers
1. **Route Protection**: Middleware-level authentication
2. **API Protection**: tRPC procedure-level authorization
3. **Data Protection**: Row-level security in database
4. **Feature Protection**: Role-based UI component rendering

### Data Security
- **Encryption at Rest**: Database-level encryption
- **Encryption in Transit**: HTTPS/TLS everywhere
- **API Security**: Rate limiting, CORS, CSP headers
- **Audit Logging**: Comprehensive activity tracking

## Performance Architecture

### Caching Strategy
- **Static Generation**: Public job pages
- **ISR (Incremental Static Regeneration)**: Frequently updated content
- **Server-Side Caching**: Redis for API responses
- **Client-Side Caching**: React Query for API data

### Optimization Techniques
- **Code Splitting**: Route-based and component-based
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Regular bundle size monitoring
- **Database Optimization**: Query optimization and indexing

## Scalability Considerations

### Horizontal Scaling
- **Stateless Application**: No server-side session storage
- **Database Connection Pooling**: Efficient connection management
- **CDN Integration**: Global content distribution
- **API Rate Limiting**: Prevent abuse and ensure fair usage

### Monitoring & Observability
- **Application Monitoring**: Error tracking and performance metrics
- **Database Monitoring**: Query performance and connection health
- **User Analytics**: Usage patterns and feature adoption
- **Alerting**: Automated alerts for critical issues

## Integration Architecture

### External Services
- **Social Media APIs**: LinkedIn, Twitter, Facebook integration
- **Email Services**: Transactional and marketing emails
- **File Storage**: Cloudflare R2 for resumes and documents
- **Payment Processing**: Subscription and billing management

### API Design
- **Internal APIs**: tRPC for type-safe client-server communication
- **Public APIs**: RESTful APIs for external integrations
- **Webhooks**: Event-driven notifications for third parties
- **GraphQL**: Flexible data queries for complex relationships