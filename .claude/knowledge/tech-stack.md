# Sourceflex Technology Stack

## Core Framework
- **Next.js 15**: React framework with App Router, Server Components
- **TypeScript**: Strict mode enabled for type safety
- **React 19**: Latest React features and concurrent rendering

## Styling & UI
- **Tailwind CSS**: Utility-first CSS framework
- **ShadCN/UI**: High-quality, accessible component library
- **Radix UI**: Headless UI primitives (via ShadCN)
- **Lucide React**: Icon library

## Backend & API
- **tRPC**: End-to-end typesafe APIs
- **Zod**: Runtime validation and type inference
- **Next.js API Routes**: Server-side API endpoints

## Database & ORM
- **PostgreSQL**: Primary database (Supabase hosted)
- **Prisma**: Type-safe database ORM and query builder
- **Supabase**: PostgreSQL hosting with additional features

## Authentication
- **Auth.js (NextAuth)**: Authentication library (stable version)
- **JWT**: Session management
- **OAuth Providers**: Google, LinkedIn, GitHub integration

## Development Tools
- **Vitest**: Unit and integration testing
- **Playwright**: End-to-end testing
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **Husky**: Git hooks for quality gates

## Deployment & Infrastructure
- **Vercel**: Application hosting and deployment
- **Cloudflare R2**: File storage (S3-compatible)
- **GitHub Actions**: CI/CD pipeline

## Key Dependencies
```json
{
  "dependencies": {
    "next": "^15.0.0",
    "react": "^19.0.0",
    "typescript": "^5.0.0",
    "@trpc/server": "^11.0.0",
    "@trpc/client": "^11.0.0",
    "@trpc/react-query": "^11.0.0",
    "prisma": "^5.0.0",
    "@prisma/client": "^5.0.0",
    "next-auth": "^5.0.0",
    "zod": "^3.22.0",
    "tailwindcss": "^3.4.0",
    "@radix-ui/react-*": "latest",
    "lucide-react": "latest"
  },
  "devDependencies": {
    "vitest": "^1.0.0",
    "playwright": "^1.40.0",
    "eslint": "^8.0.0",
    "prettier": "^3.0.0",
    "husky": "^8.0.0"
  }
}
```

## Environment Configuration
- **Development**: Local PostgreSQL + hot reload
- **Staging**: Supabase + Vercel preview deployments
- **Production**: Supabase + Vercel production

## File Structure Standards
- Maximum 300 lines per file (250 line soft limit)
- Feature-based organization over technical layers
- Shared components in `/src/components/ui/`
- Domain logic in `/src/lib/{domain}/`

## Performance Targets
- **Page Load**: < 2 seconds on mobile
- **Time to Interactive**: < 3 seconds
- **Core Web Vitals**: All green scores
- **Bundle Size**: < 500KB compressed

## Security Standards
- HTTPS everywhere (force SSL)
- CSP headers configured
- XSS and CSRF protection
- Secure authentication flows
- Data encryption at rest and in transit

## Browser Support
- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+
- **Progressive Enhancement**: Core functionality without JavaScript

## API Standards
- RESTful conventions for public APIs
- tRPC for internal client-server communication
- OpenAPI documentation for external integrations
- Rate limiting on all public endpoints

## Database Standards
- **Naming**: camelCase for fields, PascalCase for models
- **Indexing**: Composite indexes for common queries
- **Migrations**: Always reversible and safe
- **Relationships**: Proper foreign keys and constraints