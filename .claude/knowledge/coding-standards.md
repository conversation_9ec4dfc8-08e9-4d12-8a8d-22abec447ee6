# Sourceflex Coding Standards

## File Organization & Naming

### File Length Restrictions
- **Hard Limit**: 300 lines per file
- **Soft Limit**: 200-250 lines (refactor when approaching)
- **Refactoring Strategy**: Extract logical modules, not arbitrary splits
- **Naming**: Keep original file names, extract to logical sub-modules

### File Naming Conventions
```
// Good
user.service.ts
job.validation.ts
candidate.types.ts
dashboard.page.tsx

// Bad
userService.ts
JobValidation.ts
candidateTypes.ts
Dashboard.page.tsx
```

### Directory Structure
```
src/
├── app/                    # Next.js App Router
│   ├── (public)/          # Public routes
│   ├── (dashboard)/       # Protected dashboard routes
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # ShadCN/UI components
│   └── {domain}/         # Domain-specific components
├── lib/                  # Utility libraries
│   ├── {domain}/         # Domain-specific logic
│   ├── validations/      # Zod schemas
│   └── utils.ts          # Shared utilities
└── server/               # Server-side code
    └── api/              # tRPC routers
```

## TypeScript Standards

### Type Definitions
```typescript
// Always prefer interfaces for object shapes
interface User {
  id: string;
  email: string;
  name: string;
}

// Use types for unions and computed types
type UserRole = 'admin' | 'user' | 'vendor';
type UserWithRole = User & { role: UserRole };

// Generic constraints for better type safety
interface Repository<T extends { id: string }> {
  findById(id: string): Promise<T | null>;
  create(data: Omit<T, 'id'>): Promise<T>;
}
```

### Strict TypeScript Configuration
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## React Component Standards

### Component Structure
```typescript
// Component file structure
interface ComponentProps {
  // Props with proper TypeScript types
  required: string;
  optional?: number;
  children?: React.ReactNode;
}

export function Component({ 
  required, 
  optional = 0, 
  children 
}: ComponentProps) {
  // Hooks at the top
  const [state, setState] = useState<StateType>();
  const { data, isLoading } = useQuery();
  
  // Event handlers
  const handleClick = useCallback((event: MouseEvent) => {
    // Event handling logic
  }, [dependencies]);
  
  // Early returns for loading/error states
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  // Main render
  return (
    <div className="component-wrapper">
      {children}
    </div>
  );
}
```

### Component Patterns
- **Server Components**: Use by default, mark 'use client' only when needed
- **Client Components**: For interactivity, state, or browser APIs
- **Custom Hooks**: Extract reusable logic
- **Compound Components**: For complex UI patterns

### Styling Guidelines
```typescript
// Use Tailwind utility classes
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">

// Use CSS variables for dynamic values
<div 
  className="w-full bg-blue-500" 
  style={{ height: `${progress}%` }}
>

// Use ShadCN components for consistent design
import { Button } from "@/components/ui/button";
<Button variant="outline" size="sm">Click me</Button>
```

## API Standards (tRPC)

### Router Structure
```typescript
// Feature-based router organization
export const jobRouter = createTRPCRouter({
  // Queries (read operations)
  getAll: publicProcedure
    .input(getAllJobsSchema)
    .query(async ({ input, ctx }) => {
      return ctx.db.job.findMany({
        where: buildWhereClause(input),
        orderBy: { createdAt: 'desc' },
        take: input.limit ?? 10,
      });
    }),

  // Mutations (write operations)
  create: protectedProcedure
    .input(createJobSchema)
    .mutation(async ({ input, ctx }) => {
      return ctx.db.job.create({
        data: {
          ...input,
          organizationId: ctx.user.organizationId,
        },
      });
    }),
});
```

### Validation with Zod
```typescript
// Comprehensive input validation
export const createJobSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().min(50).max(5000),
  requirements: z.array(z.string()).min(1),
  location: z.object({
    type: z.enum(['remote', 'hybrid', 'onsite']),
    city: z.string().optional(),
    country: z.string(),
  }),
  salary: z.object({
    min: z.number().positive(),
    max: z.number().positive(),
    currency: z.string().length(3),
  }).optional(),
});

// Type inference from Zod schema
type CreateJobInput = z.infer<typeof createJobSchema>;
```

## Database Standards (Prisma)

### Schema Conventions
```prisma
// Model naming: PascalCase, singular
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  
  // Indexes for performance
  @@index([organizationId])
  @@index([email])
  @@map("users") // Table name: snake_case, plural
}
```

### Query Patterns
```typescript
// Always include error handling
async function getUser(id: string): Promise<User | null> {
  try {
    return await db.user.findUnique({
      where: { id },
      include: {
        organization: true,
      },
    });
  } catch (error) {
    logger.error('Failed to fetch user', { id, error });
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to fetch user',
    });
  }
}

// Use transactions for multi-table operations
async function createJobWithApplication(jobData: JobData, applicationData: ApplicationData) {
  return await db.$transaction(async (tx) => {
    const job = await tx.job.create({ data: jobData });
    const application = await tx.application.create({
      data: {
        ...applicationData,
        jobId: job.id,
      },
    });
    return { job, application };
  });
}
```

## Error Handling Standards

### tRPC Error Handling
```typescript
import { TRPCError } from '@trpc/server';

// Use appropriate error codes
throw new TRPCError({
  code: 'BAD_REQUEST',
  message: 'Invalid input provided',
  cause: originalError,
});

// Error types mapping
const errorCodeMap = {
  'BAD_REQUEST': 400,
  'UNAUTHORIZED': 401,
  'FORBIDDEN': 403,
  'NOT_FOUND': 404,
  'INTERNAL_SERVER_ERROR': 500,
} as const;
```

### Client-Side Error Handling
```typescript
// Use error boundaries for React errors
export function ErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundaryComponent
      fallback={<ErrorFallback />}
      onError={(error, errorInfo) => {
        logger.error('React error boundary caught error', { error, errorInfo });
      }}
    >
      {children}
    </ErrorBoundaryComponent>
  );
}

// Handle API errors gracefully
const { data, error, isLoading } = api.jobs.getAll.useQuery();

if (error) {
  return <ErrorMessage message={error.message} />;
}
```

## Testing Standards

### Unit Test Structure
```typescript
describe('JobService', () => {
  describe('createJob', () => {
    it('should create job with valid data', async () => {
      // Arrange
      const jobData = createMockJobData();
      const mockUser = createMockUser();
      
      // Act
      const result = await jobService.createJob(jobData, mockUser);
      
      // Assert
      expect(result).toMatchObject({
        id: expect.any(String),
        title: jobData.title,
        organizationId: mockUser.organizationId,
      });
    });

    it('should throw error with invalid data', async () => {
      // Arrange
      const invalidJobData = { title: '' }; // Invalid: empty title
      
      // Act & Assert
      await expect(
        jobService.createJob(invalidJobData, mockUser)
      ).rejects.toThrow('Title is required');
    });
  });
});
```

### E2E Test Structure
```typescript
test('user can create and publish job posting', async ({ page }) => {
  // Setup
  await loginAsHiringManager(page);
  
  // Navigate to job creation
  await page.goto('/dashboard/jobs/create');
  
  // Fill form
  await page.fill('[data-testid="job-title"]', 'Senior Developer');
  await page.fill('[data-testid="job-description"]', 'We are looking for...');
  
  // Submit and verify
  await page.click('[data-testid="publish-job"]');
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  
  // Verify job appears in listings
  await page.goto('/jobs');
  await expect(page.locator('text=Senior Developer')).toBeVisible();
});
```

## Performance Standards

### Code Splitting
```typescript
// Route-based code splitting (automatic with Next.js App Router)
const DashboardPage = lazy(() => import('./dashboard/page'));

// Component-based code splitting
const HeavyComponent = lazy(() => import('./heavy-component'));

// Use Suspense for loading states
<Suspense fallback={<LoadingSpinner />}>
  <HeavyComponent />
</Suspense>
```

### Database Optimization
```typescript
// Always use proper indexes
@@index([organizationId, status]) // Composite index for common queries

// Optimize queries with select and include
const jobs = await db.job.findMany({
  select: {
    id: true,
    title: true,
    createdAt: true,
    // Only select needed fields
  },
  where: {
    organizationId: user.organizationId,
    status: 'active',
  },
  orderBy: { createdAt: 'desc' },
  take: 20,
});
```

## Security Standards

### Input Validation
```typescript
// Always validate inputs with Zod
const userInputSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(100),
});

// Sanitize HTML content
import DOMPurify from 'dompurify';
const sanitizedContent = DOMPurify.sanitize(userInput);
```

### Authentication & Authorization
```typescript
// Always check permissions
export const protectedProcedure = publicProcedure.use(async ({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({ ctx: { ...ctx, user: ctx.user } });
});

// Row-level security
const jobs = await db.job.findMany({
  where: {
    organizationId: ctx.user.organizationId, // Always filter by organization
  },
});
```