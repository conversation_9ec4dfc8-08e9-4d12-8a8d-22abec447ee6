# Database Tools Configuration

## Primary Tools

### Prisma CLI
```bash
# Essential Prisma commands
npx prisma generate          # Generate Prisma client
npx prisma migrate dev       # Create and apply migration
npx prisma migrate deploy    # Deploy migrations to production
npx prisma db push          # Push schema to database (dev only)
npx prisma db seed          # Seed database with initial data
npx prisma studio           # Open Prisma Studio
```

### Prisma Studio
- **Purpose**: Visual database browser and editor
- **Usage**: Data exploration, manual data updates, schema visualization
- **Access**: `npx prisma studio` opens web interface on localhost:5555
- **Security**: Only use in development environment

### Database Migration Tools
```bash
# Migration workflow
npx prisma migrate dev --name add_user_roles
npx prisma migrate reset     # Reset database (dev only)
npx prisma migrate status    # Check migration status
npx prisma migrate resolve   # Mark migration as applied
```

## Development Database Setup

### Local PostgreSQL
```bash
# Docker setup for local development
docker run --name sourceflex-postgres \
  -e POSTGRES_DB=sourceflex \
  -e POSTGRES_USER=developer \
  -e POSTGRES_PASSWORD=dev_password \
  -p 5432:5432 \
  -d postgres:15
```

### Database Seeding
```typescript
// prisma/seed.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Seed organizations
  const org = await prisma.organization.create({
    data: {
      name: 'Demo Company',
      slug: 'demo-company',
    },
  });

  // Seed users
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Admin User',
      organizationId: org.id,
      role: 'ADMIN',
    },
  });
}

main()
  .catch((e) => console.error(e))
  .finally(async () => await prisma.$disconnect());
```

## Database Monitoring Tools

### Query Performance Analysis
```sql
-- Enable query logging in PostgreSQL
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 100; -- Log queries > 100ms

-- Analyze slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```

### Index Analysis
```sql
-- Find missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
  AND n_distinct > 100
  AND correlation < 0.1;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan ASC;
```

## Data Backup and Recovery

### Automated Backups
```bash
# Daily backup script
#!/bin/bash
BACKUP_DIR="/backups/sourceflex"
DATE=$(date +%Y%m%d_%H%M%S)

pg_dump -h localhost -U developer sourceflex > "$BACKUP_DIR/backup_$DATE.sql"

# Keep only last 7 days of backups
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```

### Point-in-Time Recovery
```bash
# Create base backup
pg_basebackup -h localhost -D /backup/base -U replication_user -v -P -W

# Restore from backup
pg_ctl stop -D /var/lib/postgresql/data
rm -rf /var/lib/postgresql/data/*
cp -r /backup/base/* /var/lib/postgresql/data/
pg_ctl start -D /var/lib/postgresql/data
```

## Database Testing Tools

### Test Database Setup
```typescript
// tests/setup/database.ts
import { PrismaClient } from '@prisma/client';

export async function setupTestDatabase() {
  const prisma = new PrismaClient({
    datasourceUrl: process.env.TEST_DATABASE_URL,
  });

  // Clean database before tests
  await prisma.$executeRaw`TRUNCATE TABLE "User", "Organization" CASCADE`;
  
  // Seed test data
  await seedTestData(prisma);
  
  return prisma;
}
```

### Database Testing Utilities
```typescript
// tests/utils/database.ts
export function createTestOrganization(data = {}) {
  return {
    name: 'Test Organization',
    slug: 'test-org',
    ...data,
  };
}

export function createTestUser(organizationId: string, data = {}) {
  return {
    email: '<EMAIL>',
    name: 'Test User',
    organizationId,
    role: 'USER',
    ...data,
  };
}
```

## Performance Optimization Tools

### Connection Pool Configuration
```typescript
// lib/db.ts
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const db = globalForPrisma.prisma ?? new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db;
```

### Query Optimization
```typescript
// Use select to reduce data transfer
const users = await db.user.findMany({
  select: {
    id: true,
    name: true,
    email: true,
  },
  where: {
    organizationId: orgId,
  },
});

// Use include for related data
const userWithPosts = await db.user.findUnique({
  where: { id },
  include: {
    posts: {
      take: 10,
      orderBy: { createdAt: 'desc' },
    },
  },
});
```

## Environment-Specific Configurations

### Development Environment
```env
# .env.development
DATABASE_URL="postgresql://developer:dev_password@localhost:5432/sourceflex"
DIRECT_URL="postgresql://developer:dev_password@localhost:5432/sourceflex"
```

### Testing Environment
```env
# .env.test
DATABASE_URL="postgresql://test_user:test_password@localhost:5433/sourceflex_test"
```

### Production Environment
```env
# .env.production
DATABASE_URL="postgresql://user:<EMAIL>:5432/postgres"
DIRECT_URL="postgresql://user:<EMAIL>:5432/postgres"
```

## Troubleshooting Tools

### Connection Debugging
```typescript
// scripts/test-connection.ts
import { PrismaClient } from '@prisma/client';

async function testConnection() {
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    const result = await prisma.$queryRaw`SELECT version()`;
    console.log('Database version:', result);
    
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
```

### Migration Troubleshooting
```bash
# Check migration status
npx prisma migrate status

# Resolve failed migration
npx prisma migrate resolve --applied "20231201000000_migration_name"

# Reset migrations (development only)
npx prisma migrate reset

# Force schema push (development only)
npx prisma db push --force-reset
```