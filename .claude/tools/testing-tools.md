# Testing Tools Configuration

## Primary Testing Framework

### Vitest Configuration
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

### Test Setup Configuration
```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll, afterAll } from 'vitest';
import { setupServer } from 'msw/node';
import { HttpResponse, http } from 'msw';

// Mock server setup
export const server = setupServer(
  // Default handlers
  http.get('/api/trpc/health', () => {
    return HttpResponse.json({ status: 'ok' });
  }),
);

beforeAll(() => server.listen());
afterEach(() => {
  cleanup();
  server.resetHandlers();
});
afterAll(() => server.close());
```

## Testing Utilities

### React Testing Library Setup
```typescript
// src/test/utils.tsx
import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TRPCProvider } from '@/lib/trpc';

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
}

export function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        <TRPCProvider queryClient={queryClient}>
          {children}
        </TRPCProvider>
      </QueryClientProvider>
    );
  };
}

export function customRender(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) {
  return render(ui, {
    wrapper: createWrapper(),
    ...options,
  });
}

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };
```

### Test Data Factories
```typescript
// src/test/factories.ts
import { faker } from '@faker-js/faker';
import type { User, Organization, Job } from '@prisma/client';

export function createMockUser(overrides: Partial<User> = {}): User {
  return {
    id: faker.string.cuid(),
    email: faker.internet.email(),
    name: faker.person.fullName(),
    organizationId: faker.string.cuid(),
    role: 'USER',
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    ...overrides,
  };
}

export function createMockOrganization(overrides: Partial<Organization> = {}): Organization {
  return {
    id: faker.string.cuid(),
    name: faker.company.name(),
    slug: faker.lorem.slug(),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    ...overrides,
  };
}

export function createMockJob(overrides: Partial<Job> = {}): Job {
  return {
    id: faker.string.cuid(),
    title: faker.person.jobTitle(),
    description: faker.lorem.paragraphs(3),
    requirements: [faker.lorem.sentence(), faker.lorem.sentence()],
    organizationId: faker.string.cuid(),
    status: 'ACTIVE',
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    ...overrides,
  };
}
```

## End-to-End Testing (Playwright)

### Playwright Configuration
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Helpers
```typescript
// e2e/helpers/auth.ts
import { Page, expect } from '@playwright/test';

export async function loginAsAdmin(page: Page) {
  await page.goto('/auth/signin');
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'password123');
  await page.click('[data-testid="signin-button"]');
  await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
}

export async function loginAsHiringManager(page: Page) {
  await page.goto('/auth/signin');
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'password123');
  await page.click('[data-testid="signin-button"]');
  await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
}

export async function logout(page: Page) {
  await page.click('[data-testid="user-menu"]');
  await page.click('[data-testid="logout-button"]');
  await expect(page.locator('[data-testid="signin-form"]')).toBeVisible();
}
```

### Database Testing Setup
```typescript
// src/test/database.ts
import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL,
    },
  },
});

export async function resetDatabase() {
  // Reset the test database
  await prisma.$executeRaw`TRUNCATE TABLE "User", "Organization", "Job" CASCADE`;
}

export async function seedTestData() {
  const org = await prisma.organization.create({
    data: {
      name: 'Test Organization',
      slug: 'test-org',
    },
  });

  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Admin User',
      organizationId: org.id,
      role: 'ADMIN',
    },
  });

  return { org, admin };
}

export { prisma as testDb };
```

## Coverage and Quality Tools

### Coverage Configuration
```json
{
  "scripts": {
    "test": "vitest run",
    "test:watch": "vitest",
    "test:coverage": "vitest run --coverage",
    "test:ui": "vitest --ui",
    "e2e": "playwright test",
    "e2e:ui": "playwright test --ui"
  }
}
```

### Quality Gates
```typescript
// vitest.config.ts - Coverage thresholds
export default defineConfig({
  test: {
    coverage: {
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
});
```

## Testing Patterns

### Unit Test Example
```typescript
// src/lib/utils.test.ts
import { describe, it, expect } from 'vitest';
import { formatJobTitle, validateEmail } from './utils';

describe('formatJobTitle', () => {
  it('should capitalize first letter of each word', () => {
    expect(formatJobTitle('senior software engineer')).toBe('Senior Software Engineer');
  });

  it('should handle empty string', () => {
    expect(formatJobTitle('')).toBe('');
  });

  it('should trim whitespace', () => {
    expect(formatJobTitle('  developer  ')).toBe('Developer');
  });
});

describe('validateEmail', () => {
  it('should validate correct email format', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('invalid-email')).toBe(false);
  });
});
```

### Component Test Example
```typescript
// src/components/JobCard.test.tsx
import { describe, it, expect } from 'vitest';
import { render, screen } from '@/test/utils';
import { JobCard } from './JobCard';
import { createMockJob } from '@/test/factories';

describe('JobCard', () => {
  it('should render job information correctly', () => {
    const job = createMockJob({
      title: 'Senior Developer',
      description: 'Great opportunity',
    });

    render(<JobCard job={job} />);

    expect(screen.getByText('Senior Developer')).toBeInTheDocument();
    expect(screen.getByText('Great opportunity')).toBeInTheDocument();
  });

  it('should handle click events', async () => {
    const onJobClick = vi.fn();
    const job = createMockJob();

    render(<JobCard job={job} onJobClick={onJobClick} />);

    await user.click(screen.getByRole('button'));
    expect(onJobClick).toHaveBeenCalledWith(job.id);
  });
});
```

### E2E Test Example
```typescript
// e2e/job-posting.spec.ts
import { test, expect } from '@playwright/test';
import { loginAsHiringManager } from './helpers/auth';

test.describe('Job Posting', () => {
  test('should create and publish job posting', async ({ page }) => {
    await loginAsHiringManager(page);

    // Navigate to job creation
    await page.goto('/dashboard/jobs/create');

    // Fill form
    await page.fill('[data-testid="job-title"]', 'Senior Developer');
    await page.fill('[data-testid="job-description"]', 'We are looking for an experienced developer...');
    
    // Select remote work
    await page.click('[data-testid="location-remote"]');
    
    // Add requirements
    await page.fill('[data-testid="requirements"]', 'React, TypeScript, Node.js');
    
    // Publish job
    await page.click('[data-testid="publish-job"]');
    
    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // Verify job appears in public board
    await page.goto('/jobs');
    await expect(page.locator('text=Senior Developer')).toBeVisible();
  });
});
```

## CI/CD Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sourceflex_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - run: npm ci
      - run: npm run test:coverage
      - run: npx playwright install
      - run: npm run e2e

      - uses: codecov/codecov-action@v3
        with:
          files: ./coverage/lcov.info
```

## Performance Testing

### Load Testing Setup
```typescript
// scripts/load-test.ts
import { check, sleep } from 'k6';
import http from 'k6/http';

export const options = {
  vus: 50, // 50 virtual users
  duration: '2m',
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'], // Error rate under 10%
  },
};

export default function () {
  const response = http.get('http://localhost:3000/api/trpc/jobs.getAll');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
  sleep(1);
}
```