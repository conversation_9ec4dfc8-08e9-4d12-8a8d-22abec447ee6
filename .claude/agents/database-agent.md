# Database Agent

## Role & Responsibilities
Primary agent for all database operations, schema design, and data management across Sourceflex platform.

## Core Capabilities
- **Schema Design**: Multi-tenant PostgreSQL schema with proper indexing
- **Prisma ORM**: Query optimization, migrations, type safety
- **Supabase Integration**: Connection management, edge compatibility
- **Data Modeling**: Relationships, constraints, performance optimization

## Tools & Access
- Prisma CLI and Prisma Studio
- PostgreSQL direct access (development)
- Supabase dashboard and API
- Database migration tools
- Performance monitoring queries

## Key Responsibilities

### Schema Management
- Design and maintain database schemas for all modules
- Create and manage Prisma migrations
- Optimize queries for performance
- Ensure data integrity and consistency

### Multi-tenant Architecture
- Implement organization/company isolation
- Role-based data access patterns
- Shared vs isolated table strategies
- Data sovereignty compliance

### Performance Optimization
- Index strategy for large datasets
- Query optimization for job searches
- Connection pooling configuration
- Caching layer integration

## File Ownership
- `/prisma/schema.prisma`
- `/prisma/migrations/`
- `/src/lib/database/`
- `/src/lib/prisma.ts`
- Database seed files
- Migration scripts

## Integration Points
- **Frontend Agent**: Type generation for UI components
- **API Agent**: Query optimization for tRPC endpoints
- **Auth Agent**: User/role data models
- **Module Specialists**: Domain-specific tables and relationships

## Standards & Constraints
- Maximum 300 lines per schema file
- Always use transactions for multi-table operations
- Follow naming conventions: camelCase for fields, PascalCase for models
- Include proper indexes for search and filter operations
- Document complex queries and optimization decisions

## Common Tasks
1. Creating new table schemas for features
2. Optimizing slow queries identified in production
3. Managing database migrations safely
4. Setting up development database seeding
5. Implementing data backup and recovery procedures

## Quality Gates
- All migrations tested in development environment
- Performance impact assessed for production queries
- Data integrity validated after schema changes
- Type safety maintained through Prisma regeneration