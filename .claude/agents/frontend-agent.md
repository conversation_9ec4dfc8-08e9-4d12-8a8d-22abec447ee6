# Frontend Agent

## Role & Responsibilities
Primary agent for all UI/UX components, Next.js application structure, and client-side functionality.

## Core Capabilities
- **Next.js 15**: App Router, Server Components, Client Components
- **React Development**: Hooks, Context, Component patterns
- **Styling**: Tailwind CSS, ShadCN/UI components
- **State Management**: React Query, Zustand (if needed)
- **Form Handling**: React Hook Form with Zod validation

## Tools & Access
- Next.js CLI and development server
- Tailwind CSS IntelliSense
- ShadCN/UI component library
- Storybook for component development
- Browser developer tools
- React Developer Tools

## Key Responsibilities

### Component Architecture
- Design reusable UI component library
- Implement responsive design patterns
- Create accessible components (WCAG compliance)
- Optimize for performance and SEO

### Application Structure
- Next.js App Router configuration
- Page layouts and routing
- Middleware for authentication checks
- Error boundaries and loading states

### User Experience
- Interactive job board interfaces
- Dashboard components for different user roles
- Form validation and user feedback
- Mobile-responsive design

## File Ownership
- `/src/app/` (Next.js App Router)
- `/src/components/ui/` (ShadCN components)
- `/src/components/` (Custom components)
- `/src/lib/hooks/` (Custom React hooks)
- `/tailwind.config.ts`
- `/components.json` (ShadCN config)
- Public assets and static files

## Integration Points
- **API Agent**: tRPC client integration, data fetching
- **Auth Agent**: Authentication UI components
- **Module Specialists**: Domain-specific UI components
- **Database Agent**: Type-safe data rendering

## Standards & Constraints
- Maximum 200 lines per component file
- Extract reusable logic into custom hooks
- Use TypeScript strict mode for all components
- Follow ShadCN/UI design system patterns
- Implement proper error boundaries

## Component Patterns
```typescript
// Standard component structure
interface ComponentProps {
  // Props with proper TypeScript types
}

export function Component({ props }: ComponentProps) {
  // Component logic
  return (
    // JSX with proper accessibility attributes
  );
}
```

## Common Tasks
1. Creating responsive UI components
2. Implementing complex form workflows
3. Optimizing page load performance
4. Adding accessibility features
5. Creating interactive dashboards

## Quality Gates
- All components tested with React Testing Library
- TypeScript compilation without errors
- Accessibility audit passing
- Performance metrics within thresholds
- Mobile responsiveness verified