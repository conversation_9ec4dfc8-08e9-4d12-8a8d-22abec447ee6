# Sourceflex - Multi-Channel Resume Sourcing & Collaboration Platform

## Project Overview
Sourceflex is a comprehensive recruitment platform that combines Job Board functionality, CRM for sales teams, VMS for vendor management, and ATS for application tracking with social media integration and automation.

## Quick Start Commands

### Development Environment
```bash
# Setup development environment
npm install
npm run dev:db          # Start PostgreSQL database
npm run generate        # Generate Prisma client
npm run dev:seed        # Seed database with test data
npm run dev             # Start development server

# Common development tasks
npm run dev:studio      # Open Prisma Studio
npm run type-check      # TypeScript validation
npm run lint            # Code linting
npm run test            # Run test suite
```

### Key File Locations
- **Database Schema**: `prisma/schema.prisma`
- **API Routes**: `src/server/api/routers/`
- **Components**: `src/components/`
- **Pages**: `src/app/`
- **Validations**: `src/lib/validations/`
- **Utilities**: `src/lib/utils/`

## Architecture & Tech Stack

### Core Technologies
- **Framework**: Next.js 15 with App Router and TypeScript
- **Database**: PostgreSQL with Prisma ORM (hosted on Supabase)
- **API**: tRPC for type-safe client-server communication
- **Authentication**: Auth.js (NextAuth) stable version
- **Styling**: Tailwind CSS with ShadCN/UI components
- **Deployment**: Vercel with Cloudflare R2 for file storage

### Project Structure
```
sourceflex/
├── .claude/                    # Claude AI agent configurations
│   ├── agents/                # Specialized AI agents
│   ├── knowledge/             # Project documentation
│   ├── tools/                 # Development tools config
│   └── workflows/             # Development workflows
├── prisma/                    # Database schema and migrations
├── src/
│   ├── app/                   # Next.js App Router pages
│   ├── components/            # React components
│   ├── lib/                   # Utilities and configurations
│   └── server/                # tRPC API routes
└── tests/                     # Test suites
```

## Claude AI Agent System

### Functional Agents
- **Database Agent**: Schema design, migrations, query optimization
- **Frontend Agent**: UI components, user experience, responsive design
- **API Agent**: Business logic, tRPC endpoints, integrations
- **Auth Agent**: Authentication, authorization, security
- **Testing Agent**: Test strategies, quality assurance, CI/CD

### Module Specialists
- **Job Board Specialist**: Job posting, search, application workflows
- **CRM Specialist**: Client management, sales pipeline, analytics
- **VMS Specialist**: Vendor management, performance tracking
- **ATS Specialist**: Candidate tracking, interview scheduling
- **Social Media Specialist**: Platform integration, automated posting

### Agent Collaboration Rules
1. **Database changes**: Always involve Database Agent
2. **UI modifications**: Frontend Agent leads implementation
3. **Business logic**: API Agent manages workflows with module specialists
4. **Security concerns**: Auth Agent reviews all authentication-related changes
5. **Quality assurance**: Testing Agent validates all implementations

## Development Standards

### File Management
- **Maximum file length**: 300 lines (hard limit)
- **Soft limit**: 250 lines (consider refactoring)
- **Refactoring strategy**: Extract logical modules, maintain original filenames
- **Read entire file**: Always understand full context before modifications

### Code Quality
- **TypeScript**: Strict mode enabled, no `any` types
- **Testing**: Minimum 80% coverage for critical paths
- **Linting**: ESLint with Prettier formatting
- **Error handling**: Comprehensive error boundaries and validation

### Git Workflow
- **Branch naming**: `feature/`, `bugfix/`, `hotfix/` prefixes
- **Commit messages**: Conventional commits format
- **Pull requests**: Required for all changes, minimum 1 approval
- **Pre-commit hooks**: Type checking, linting, testing

## Core Features

### Job Board Module
- Public job listings with SEO optimization
- Advanced search and filtering capabilities
- Mobile-responsive job application forms
- Company-branded job pages
- Application tracking for candidates

### CRM Module
- Client relationship management
- Sales pipeline tracking and analytics
- Email integration and communication history
- Lead qualification and nurturing workflows
- Performance dashboards for sales teams

### VMS Module
- Vendor registration and onboarding
- Automated job distribution to qualified vendors
- Performance scorecards and analytics
- Contract and compliance management
- Payment processing and invoicing

### ATS Module
- Candidate profile management
- Resume parsing and data extraction
- Multi-stage hiring pipeline workflows
- Interview scheduling and coordination
- Collaborative candidate evaluation

### Social Media Integration
- Automated job posting to LinkedIn, Twitter, Facebook
- Social media candidate sourcing
- Engagement tracking and analytics
- Employer branding content management
- Platform-specific content optimization

## Database Schema

### Core Entities
```prisma
model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  settings    Json?
  users       User[]
  jobs        Job[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model User {
  id             String       @id @default(cuid())
  email          String       @unique
  name           String?
  role           UserRole
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
}

model Job {
  id             String       @id @default(cuid())
  title          String
  description    String
  requirements   String[]
  status         JobStatus    @default(DRAFT)
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  applications   Application[]
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
}
```

## API Architecture

### tRPC Router Structure
```typescript
// Main API router
export const appRouter = createTRPCRouter({
  auth: authRouter,        // Authentication and user management
  jobs: jobRouter,         // Job posting and management
  candidates: candidateRouter, // ATS functionality
  clients: clientRouter,   // CRM functionality
  vendors: vendorRouter,   // VMS functionality
  social: socialRouter,    // Social media integration
});

// Example job router
export const jobRouter = createTRPCRouter({
  getAll: publicProcedure
    .input(getAllJobsSchema)
    .query(async ({ ctx, input }) => {
      // Implementation with proper filtering and pagination
    }),
    
  create: protectedProcedure
    .input(createJobSchema)
    .mutation(async ({ ctx, input }) => {
      // Implementation with organization isolation
    }),
});
```

## Authentication & Authorization

### Multi-tenant Security
- **Organization isolation**: All data filtered by organization ID
- **Role-based access control**: Granular permissions per module
- **Session management**: Secure JWT tokens with Auth.js
- **API protection**: All endpoints require proper authentication

### User Roles
- `SUPER_ADMIN`: Platform administration
- `ORG_ADMIN`: Organization management
- `HIRING_MANAGER`: Job posting and candidate management
- `RECRUITER`: Candidate sourcing and communication
- `SALES_REP`: CRM and client management
- `VENDOR`: External vendor access
- `CANDIDATE`: Job applicant access

## Testing Strategy

### Test Structure
```
Unit Tests (70%)
├── Components: React Testing Library
├── Utilities: Pure function testing
├── API Routes: tRPC procedure testing
└── Database: Prisma operation testing

Integration Tests (20%)
├── API Endpoints: Full request/response cycle
├── Database Transactions: Multi-table operations
└── Authentication: Login/logout flows

E2E Tests (10%)
├── Critical User Workflows: Job application process
├── Multi-module Interactions: CRM to Job posting
└── Cross-device Testing: Mobile and desktop
```

### Test Commands
```bash
npm run test              # Unit tests
npm run test:coverage     # Coverage report
npm run test:watch        # Watch mode
npm run e2e              # End-to-end tests
npm run e2e:ui           # E2E with UI
```

## Deployment & Infrastructure

### Environment Configuration
```bash
# Development
DATABASE_URL="postgresql://dev_user:password@localhost:5432/sourceflex"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="development-secret-key"

# Production
DATABASE_URL="postgresql://user:<EMAIL>:5432/postgres"
NEXTAUTH_URL="https://sourceflex.com"
NEXTAUTH_SECRET="production-secret-key"
```

### Deployment Process
1. **Staging**: Automatic deployment on `staging` branch
2. **Production**: Automatic deployment on `main` branch
3. **Database migrations**: Run automatically via Prisma
4. **Health checks**: Automated monitoring and alerting

## Performance Optimization

### Key Metrics
- **Page Load Time**: < 2 seconds on mobile
- **Time to Interactive**: < 3 seconds
- **Core Web Vitals**: All green scores
- **Bundle Size**: < 500KB compressed
- **Database Queries**: < 100ms average response time

### Optimization Strategies
- **Code splitting**: Route and component-based
- **Image optimization**: Next.js Image component
- **Database indexing**: Optimized for common queries
- **Caching**: Multi-layer caching with React Query
- **CDN integration**: Cloudflare for global performance

## Troubleshooting

### Common Issues
```bash
# Database connection issues
npm run dev:db           # Ensure PostgreSQL is running
npx prisma migrate reset # Reset database (development only)

# Build failures
npm run type-check       # Check TypeScript errors
npm run lint:fix         # Fix linting issues

# Test failures
npm run test:debug       # Debug mode for tests
npm run e2e:debug        # Debug E2E tests
```

### Debug Commands
```bash
# Database debugging
npx prisma studio        # Visual database browser
npx prisma migrate status # Check migration status

# Development debugging
npm run dev -- --turbo  # Enable Turbo mode
npm run analyze          # Analyze bundle size
```

## Security Considerations

### Security Checklist
- [ ] **Input validation**: All inputs validated with Zod
- [ ] **Authentication**: Secure session management
- [ ] **Authorization**: Role-based access control
- [ ] **Data protection**: Encryption at rest and in transit
- [ ] **Audit logging**: Comprehensive activity tracking
- [ ] **Rate limiting**: API endpoint protection
- [ ] **Security headers**: CSP, HSTS, XSS protection

### Security Tools
```bash
npm audit                # Check for vulnerabilities
npm run security:check   # Custom security validation
npm run test:security    # Security-focused tests
```

## Contributing

### Before Starting Development
1. **Read agent documentation**: Understand your role's responsibilities
2. **Follow coding standards**: File length limits, TypeScript strict mode
3. **Set up environment**: Local database, development tools
4. **Run tests**: Ensure everything works before making changes

### Development Process
1. **Create feature branch**: `git checkout -b feature/your-feature`
2. **Follow agent guidelines**: Use appropriate agent for your changes
3. **Write tests**: Maintain test coverage requirements
4. **Create PR**: Clear description, all checks passing
5. **Address feedback**: Respond to code review comments

### Getting Help
- **Agent documentation**: `.claude/agents/` for specific guidance
- **Architecture docs**: `.claude/knowledge/` for system understanding
- **Workflows**: `.claude/workflows/` for process guidance
- **Issues**: Create GitHub issues for bugs or feature requests

## Monitoring & Analytics

### Application Monitoring
- **Error tracking**: Comprehensive error reporting
- **Performance monitoring**: Real-time performance metrics
- **User analytics**: Usage patterns and feature adoption
- **Business metrics**: Conversion rates, user engagement

### Health Checks
```bash
# Application health
curl https://sourceflex.com/api/health

# Database health
npm run db:health

# External service health
npm run services:health
```

---

**Last Updated**: Phase 0 Setup Complete
**Next Phase**: Initialize Next.js 15 project and basic configuration