{"name": "sourceflex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "dev:db": "docker-compose up -d postgres", "dev:seed": "npm run db:seed", "dev:reset": "prisma migrate reset && npm run dev:seed", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@prisma/client": "^5.22.0", "@radix-ui/react-slot": "^1.2.3", "@simplewebauthn/browser": "^9.0.1", "@simplewebauthn/server": "^9.0.3", "@tanstack/react-query": "^4.36.1", "@trpc/client": "^10.45.2", "@trpc/next": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@types/bcryptjs": "^2.4.6", "@types/ip-address": "^6.0.0", "@types/qrcode": "^1.5.5", "@types/ua-parser-js": "^0.7.39", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "googleapis": "^155.0.0", "ip-address": "^10.0.1", "lucide-react": "^0.462.0", "next": "15.4.5", "next-auth": "5.0.0-beta.26", "otpauth": "^9.4.0", "prisma": "^5.22.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "resend": "^4.8.0", "superjson": "^2.2.2", "tailwind-merge": "^2.5.4", "ua-parser-js": "^2.0.4", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.49.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9", "eslint-config-next": "15.4.5", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "playwright": "^1.49.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5", "vitest": "^2.1.5"}}